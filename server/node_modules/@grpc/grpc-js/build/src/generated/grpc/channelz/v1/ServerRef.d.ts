import type { Long } from '@grpc/proto-loader';
/**
 * ServerRef is a reference to a Server.
 */
export interface ServerRef {
    /**
     * A globally unique identifier for this server.  Must be a positive number.
     */
    'server_id'?: (number | string | Long);
    /**
     * An optional name associated with the server.
     */
    'name'?: (string);
}
/**
 * ServerRef is a reference to a Server.
 */
export interface ServerRef__Output {
    /**
     * A globally unique identifier for this server.  Must be a positive number.
     */
    'server_id': (string);
    /**
     * An optional name associated with the server.
     */
    'name': (string);
}
