{"version": 3, "file": "max-message-size-filter.js", "sourceRoot": "", "sources": ["../../src/max-message-size-filter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,qCAA6D;AAE7D,2CAIqB;AAErB,yCAAsC;AAEtC,MAAa,oBAAqB,SAAQ,mBAAU;IAGlD,YAAY,OAAuB;QACjC,KAAK,EAAE,CAAC;QAHF,uBAAkB,GAAW,2CAA+B,CAAC;QAC7D,0BAAqB,GAAW,8CAAkC,CAAC;QAGzE,IAAI,8BAA8B,IAAI,OAAO,EAAE,CAAC;YAC9C,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,8BAA8B,CAAE,CAAC;QACrE,CAAC;QACD,IAAI,iCAAiC,IAAI,OAAO,EAAE,CAAC;YACjD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,iCAAiC,CAAE,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAA6B;QAC7C;sBACc;QACd,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,CAAC,EAAE,CAAC;YACnC,OAAO,OAAO,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC;YACtC,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC7D,MAAM;oBACJ,IAAI,EAAE,kBAAM,CAAC,kBAAkB;oBAC/B,OAAO,EAAE,iCAAiC,eAAe,CAAC,OAAO,CAAC,MAAM,QAAQ,IAAI,CAAC,kBAAkB,GAAG;oBAC1G,QAAQ,EAAE,IAAI,mBAAQ,EAAE;iBACzB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,eAAe,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAwB;QAC3C;sBACc;QACd,IAAI,IAAI,CAAC,qBAAqB,KAAK,CAAC,CAAC,EAAE,CAAC;YACtC,OAAO,OAAO,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC;YACtC,IAAI,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACxD,MAAM;oBACJ,IAAI,EAAE,kBAAM,CAAC,kBAAkB;oBAC/B,OAAO,EAAE,qCAAqC,eAAe,CAAC,MAAM,QAAQ,IAAI,CAAC,qBAAqB,GAAG;oBACzG,QAAQ,EAAE,IAAI,mBAAQ,EAAE;iBACzB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,eAAe,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAlDD,oDAkDC;AAED,MAAa,2BAA2B;IAGtC,YAA6B,OAAuB;QAAvB,YAAO,GAAP,OAAO,CAAgB;IAAG,CAAC;IAExD,YAAY;QACV,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;CACF;AARD,kEAQC"}