!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.dset={})}(this,(function(t){t.dset=function(t,e,o){e.split&&(e=e.split("."));for(var n,f,p=0,i=e.length,d=t;p<i&&"__proto__"!=(f=""+e[p++])&&"constructor"!==f&&"prototype"!==f;)d=d[f]=p===i?o:typeof(n=d[f])==typeof e?n:0*e[p]!=0||~(""+e[p]).indexOf(".")?{}:[]}}));