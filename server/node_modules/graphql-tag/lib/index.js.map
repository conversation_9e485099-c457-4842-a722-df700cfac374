{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAShC,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAwB,CAAC;AAGjD,IAAM,iBAAiB,GAAG,IAAI,GAAG,EAAuB,CAAC;AAEzD,IAAI,qBAAqB,GAAG,IAAI,CAAC;AACjC,IAAI,6BAA6B,GAAG,KAAK,CAAC;AAI1C,SAAS,SAAS,CAAC,MAAc;IAC/B,OAAO,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAC/C,CAAC;AAED,SAAS,eAAe,CAAC,GAAa;IACpC,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAClE,CAAC;AAKD,SAAS,gBAAgB,CAAC,GAAiB;IACzC,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;IACnC,IAAM,WAAW,GAAqB,EAAE,CAAC;IAEzC,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,UAAA,kBAAkB;QACxC,IAAI,kBAAkB,CAAC,IAAI,KAAK,oBAAoB,EAAE;YACpD,IAAI,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;YACjD,IAAI,SAAS,GAAG,eAAe,CAAC,kBAAkB,CAAC,GAAI,CAAC,CAAC;YAGzD,IAAI,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;YACxD,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBAGhD,IAAI,qBAAqB,EAAE;oBACzB,OAAO,CAAC,IAAI,CAAC,8BAA8B,GAAG,YAAY,GAAG,oBAAoB;0BAC7E,iGAAiG;0BACjG,8EAA8E,CAAC,CAAC;iBACrF;aACF;iBAAM,IAAI,CAAC,YAAY,EAAE;gBACxB,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC;aAC7D;YAED,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAE5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBAC5B,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACxB,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACtC;SACF;aAAM;YACL,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACtC;IACH,CAAC,CAAC,CAAC;IAEH,6BACK,GAAG,KACN,WAAW,aAAA,IACX;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,GAAiB;IACjC,IAAM,OAAO,GAAG,IAAI,GAAG,CAAsB,GAAG,CAAC,WAAW,CAAC,CAAC;IAE9D,OAAO,CAAC,OAAO,CAAC,UAAA,IAAI;QAClB,IAAI,IAAI,CAAC,GAAG;YAAE,OAAO,IAAI,CAAC,GAAG,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;YAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBACtC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACpB;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAM,GAAG,GAAG,GAAG,CAAC,GAA0B,CAAC;IAC3C,IAAI,GAAG,EAAE;QACP,OAAO,GAAG,CAAC,UAAU,CAAC;QACtB,OAAO,GAAG,CAAC,QAAQ,CAAC;KACrB;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,aAAa,CAAC,MAAc;IACnC,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IACjC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC3B,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE;YAC3B,6BAA6B,+BAAA;YAC7B,4BAA4B,EAAE,6BAA6B;SACrD,CAAC,CAAC;QACV,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QACD,QAAQ,CAAC,GAAG,CACV,QAAQ,EAGR,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CACnC,CAAC;KACH;IACD,OAAO,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;AACjC,CAAC;AAGD,MAAM,UAAU,GAAG,CACjB,QAAoC;IACpC,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,6BAAc;;IAGd,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;KACvB;IAED,IAAI,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEzB,IAAI,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,CAAC;QAClB,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE;YAClC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;SAC/B;aAAM;YACL,MAAM,IAAI,GAAG,CAAC;SACf;QACD,MAAM,IAAI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC;AAED,MAAM,UAAU,WAAW;IACzB,QAAQ,CAAC,KAAK,EAAE,CAAC;IACjB,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAC5B,CAAC;AAED,MAAM,UAAU,uBAAuB;IACrC,qBAAqB,GAAG,KAAK,CAAC;AAChC,CAAC;AAED,MAAM,UAAU,mCAAmC;IACjD,6BAA6B,GAAG,IAAI,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,oCAAoC;IAClD,6BAA6B,GAAG,KAAK,CAAC;AACxC,CAAC;AAED,IAAM,MAAM,GAAG;IACb,GAAG,KAAA;IACH,WAAW,aAAA;IACX,uBAAuB,yBAAA;IACvB,mCAAmC,qCAAA;IACnC,oCAAoC,sCAAA;CACrC,CAAC;AAEF,WAAiB,KAAG;IAEhB,SAAG,GAKD,MAAM,IALL,EACH,iBAAW,GAIT,MAAM,YAJG,EACX,6BAAuB,GAGrB,MAAM,wBAHe,EACvB,yCAAmC,GAEjC,MAAM,oCAF2B,EACnC,0CAAoC,GAClC,MAAM,qCAD4B,CAC3B;AACb,CAAC,EARgB,GAAG,KAAH,GAAG,QAQnB;AAED,GAAG,CAAC,SAAO,CAAA,GAAG,GAAG,CAAC;AAElB,eAAe,GAAG,CAAC"}