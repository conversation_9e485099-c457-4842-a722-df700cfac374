import { DocumentNode } from 'graphql/language/ast';
export declare function gql(literals: string | readonly string[], ...args: any[]): DocumentNode;
export declare namespace gql {
    var _a: typeof import(".").gql;
    export { _a as default };
}
export declare function resetCaches(): void;
export declare function disableFragmentWarnings(): void;
export declare function enableExperimentalFragmentVariables(): void;
export declare function disableExperimentalFragmentVariables(): void;
export declare namespace gql {
    const gql: typeof import(".").gql, resetCaches: typeof import(".").resetCaches, disableFragmentWarnings: typeof import(".").disableFragmentWarnings, enableExperimentalFragmentVariables: typeof import(".").enableExperimentalFragmentVariables, disableExperimentalFragmentVariables: typeof import(".").disableExperimentalFragmentVariables;
}
export default gql;
//# sourceMappingURL=index.d.ts.map