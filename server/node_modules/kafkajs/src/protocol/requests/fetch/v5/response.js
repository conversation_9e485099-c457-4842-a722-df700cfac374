const Decoder = require('../../../decoder')
const { parse: parseV1 } = require('../v1/response')
const decodeMessages = require('../v4/decodeMessages')

/**
 * Fetch Response (Version: 5) => throttle_time_ms [responses]
 *  throttle_time_ms => INT32
 *  responses => topic [partition_responses]
 *    topic => STRING
 *    partition_responses => partition_header record_set
 *      partition_header => partition error_code high_watermark last_stable_offset log_start_offset [aborted_transactions]
 *        partition => INT32
 *        error_code => INT16
 *        high_watermark => INT64
 *        last_stable_offset => INT64
 *        log_start_offset => INT64
 *        aborted_transactions => producer_id first_offset
 *          producer_id => INT64
 *          first_offset => INT64
 *      record_set => RECORDS
 */

const decodeAbortedTransactions = decoder => ({
  producerId: decoder.readInt64().toString(),
  firstOffset: decoder.readInt64().toString(),
})

const decodePartition = async decoder => ({
  partition: decoder.readInt32(),
  errorCode: decoder.readInt16(),
  highWatermark: decoder.readInt64().toString(),
  lastStableOffset: decoder.readInt64().toString(),
  lastStartOffset: decoder.readInt64().toString(),
  abortedTransactions: decoder.readArray(decodeAbortedTransactions),
  messages: await decodeMessages(decoder),
})

const decodeResponse = async decoder => ({
  topicName: decoder.readString(),
  partitions: await decoder.readArrayAsync(decodePartition),
})

const decode = async rawData => {
  const decoder = new Decoder(rawData)
  const throttleTime = decoder.readInt32()
  const responses = await decoder.readArrayAsync(decodeResponse)

  return {
    throttleTime,
    responses,
  }
}

module.exports = {
  decode,
  parse: parseV1,
}
