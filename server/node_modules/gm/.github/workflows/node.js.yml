# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:
  build:
    runs-on: ${{ matrix.os }}

    env:
      DEBUG: "gm*"

    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest]
        node-version: [14.x, 16.x, 18.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - name: Install GraphicsMagic and Imagemagick on Ubuntu
      if: contains(matrix.os, 'ubuntu')
      run: sudo apt-get install -y imagemagick graphicsmagick
    - name: Install GraphicsMagic and Imagemagick on Windows
      if: contains(matrix.os, 'windows')
      run: choco install -y imagemagick graphicsmagick
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
    - run: npm i
    - name: Run tests on Windows
      if: contains(matrix.os, 'windows')
      shell: cmd
      run: |
        call refreshenv
        npm test
    - name: Run tests on Ubuntu
      if: contains(matrix.os, 'ubuntu')
      run: npm test