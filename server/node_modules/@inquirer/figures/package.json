{"name": "@inquirer/figures", "version": "1.0.10", "description": "Vendored version of figures, for CJS compatibility", "keywords": ["answer", "answers", "ask", "base", "cli", "command", "command-line", "confirm", "enquirer", "generate", "generator", "hyper", "input", "inquire", "inquirer", "interface", "iterm", "javascript", "menu", "node", "nodejs", "prompt", "promptly", "prompts", "question", "readline", "scaffold", "scaffolder", "scaffolding", "stdin", "stdout", "terminal", "tty", "ui", "yeoman", "yo", "zsh", "types", "typescript"], "repository": {"type": "git", "url": "https://github.com/SBoudrias/Inquirer.js.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "files": ["dist"], "scripts": {"attw": "attw --pack", "tsc": "tshy"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@repo/tsconfig": "workspace:*", "tshy": "^3.0.2"}, "engines": {"node": ">=18"}, "tshy": {"exclude": ["src/**/*.test.ts"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "gitHead": "a8768f9e974362aed97bc47f6c3466718e8edce9"}