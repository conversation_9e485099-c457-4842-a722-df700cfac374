{"name": "pg-protocol", "version": "1.9.5", "description": "The postgres client/server binary protocol, implemented in TypeScript", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./esm/index.js", "require": "./dist/index.js", "default": "./dist/index.js"}, "./dist/*": {"import": "./dist/*", "require": "./dist/*", "default": "./dist/*"}}, "license": "MIT", "devDependencies": {"@types/chai": "^4.2.7", "@types/mocha": "^10.0.7", "@types/node": "^12.12.21", "chai": "^4.2.0", "chunky": "^0.0.0", "mocha": "^10.5.2", "ts-node": "^8.5.4", "typescript": "^4.0.3"}, "scripts": {"test": "mocha dist/**/*.test.js", "build": "tsc", "build:watch": "tsc --watch", "prepublish": "yarn build", "pretest": "yarn build"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg-protocol"}, "files": ["/dist/*{js,ts,map}", "/src", "/esm"], "gitHead": "56e286257724783681f8830b2faa7d407b6563e7"}