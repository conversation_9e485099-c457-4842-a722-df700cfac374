{"version": 3, "file": "loaders.js", "sourceRoot": "", "sources": ["../src/loaders.ts"], "names": [], "mappings": ";AAAA,0DAA0D;;;;;;AAE1D,2BAAuD;AACvD,0CAA4C;AAC5C,gDAAwB;AACxB,6BAAoC;AAGpC,IAAI,WAA0C,CAAC;AACxC,MAAM,UAAU,GAAe,SAAS,UAAU,CAAC,QAAQ;IAChE,IAAI,WAAW,KAAK,SAAS,EAAE;QAC7B,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;KACvC;IAED,OAAO,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/B,CAAC,CAAC;AANW,QAAA,UAAU,cAMrB;AAEK,MAAM,MAAM,GAAW,KAAK,UAAU,MAAM,CAAC,QAAQ;IAC1D,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,mBAAa,EAAC,QAAQ,CAAC,CAAC;QACzC,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;KACrC;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,IAAA,kBAAU,EAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;KACjC;AACH,CAAC,CAAC;AAPW,QAAA,MAAM,UAOjB;AAEF,IAAI,SAAsC,CAAC;AACpC,MAAM,QAAQ,GAAe,SAAS,QAAQ,CAAC,QAAQ,EAAE,OAAO;IACrE,IAAI,SAAS,KAAK,SAAS,EAAE;QAC3B,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;KACnC;IAED,IAAI;QACF,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;KAC3B;IAAC,OAAO,KAAK,EAAE;QACd,KAAK,CAAC,OAAO,GAAG,iBAAiB,QAAQ,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAC/D,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CAAC;AAXW,QAAA,QAAQ,YAWnB;AAEF,IAAI,IAA8B,CAAC;AAC5B,MAAM,QAAQ,GAAe,SAAS,QAAQ,CAAC,QAAQ,EAAE,OAAO;IACrE,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;KAC3B;IAED,IAAI;QACF,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC3B;IAAC,OAAO,KAAK,EAAE;QACd,KAAK,CAAC,OAAO,GAAG,iBAAiB,QAAQ,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAC/D,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CAAC;AAXW,QAAA,QAAQ,YAWnB;AAEF,IAAI,UAAuC,CAAC;AACrC,MAAM,UAAU,GAAe,SAAS,UAAU,CAAC,QAAQ,EAAE,OAAO;IACzE,uCAAuC;IACvC,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;KACpC;IACD,MAAM,gBAAgB,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IACvD,IAAI;QACF,MAAM,MAAM,GAAG,eAAe,CAAC,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7D,MAAM,CAAC,eAAe,GAAG;YACvB,GAAG,MAAM,CAAC,eAAe;YACzB,MAAM,EAAE,UAAU,CAAC,UAAU,CAAC,QAAQ;YACtC,gBAAgB,EAAE,UAAU,CAAC,oBAAoB,CAAC,QAAQ;YAC1D,MAAM,EAAE,UAAU,CAAC,YAAY,CAAC,MAAM;YACtC,MAAM,EAAE,KAAK;SACd,CAAC;QACF,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,UAAU,CAAC;QACjE,IAAA,kBAAa,EAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACzC,OAAO,IAAA,kBAAU,EAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC;KACtD;IAAC,OAAO,KAAK,EAAE;QACd,KAAK,CAAC,OAAO,GAAG,uBAAuB,QAAQ,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QACrE,MAAM,KAAK,CAAC;KACb;YAAS;QACR,IAAI,IAAA,eAAU,EAAC,gBAAgB,CAAC,EAAE;YAChC,IAAA,WAAM,EAAC,gBAAgB,CAAC,CAAC;SAC1B;KACF;AACH,CAAC,CAAC;AA1BW,QAAA,UAAU,cA0BrB;AAEK,MAAM,MAAM,GAAW,KAAK,UAAU,MAAM,CAAC,QAAQ,EAAE,OAAO;IACnE,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,UAAU,GAAG,CAAC,MAAM,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC;KACnD;IACD,MAAM,gBAAgB,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IACvD,IAAI;QACF,MAAM,MAAM,GAAG,eAAe,CAAC,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7D,MAAM,CAAC,eAAe,GAAG;YACvB,GAAG,MAAM,CAAC,eAAe;YACzB,MAAM,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM;YACpC,gBAAgB,EAAE,UAAU,CAAC,oBAAoB,CAAC,OAAO;YACzD,MAAM,EAAE,UAAU,CAAC,YAAY,CAAC,MAAM;YACtC,MAAM,EAAE,KAAK;SACd,CAAC;QACF,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,UAAU,CAAC;QACjE,MAAM,IAAA,oBAAS,EAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,mBAAa,EAAC,gBAAgB,CAAC,CAAC;QACjD,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;KACrC;IAAC,OAAO,KAAK,EAAE;QACd,KAAK,CAAC,OAAO,GAAG,uBAAuB,QAAQ,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QACrE,MAAM,KAAK,CAAC;KACb;YAAS;QACR,IAAI,IAAA,eAAU,EAAC,gBAAgB,CAAC,EAAE;YAChC,MAAM,IAAA,aAAE,EAAC,gBAAgB,CAAC,CAAC;SAC5B;KACF;AACH,CAAC,CAAC;AA1BW,QAAA,MAAM,UA0BjB;AAEF,8DAA8D;AAC9D,SAAS,eAAe,CAAC,SAAiB;IACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,EAAE;QACjE,OAAO,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IACH,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE,CACrE,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAC9B,CAAC;QACF,IAAI,KAAK,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,YAAY,QAAQ,KAAK,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;SAC1E;QACD,OAAO,MAAM,CAAC;KACf;IACD,OAAO;AACT,CAAC"}