{"version": 3, "file": "ExplorerSync.js", "sourceRoot": "", "sources": ["../src/ExplorerSync.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,yCAA4C;AAC5C,uDAA0E;AAC1E,6CAAwC;AAExC,uCAAuD;AAEvD;;GAEG;AACH,MAAa,YAAa,SAAQ,8BAAiC;IAC1D,IAAI,CAAC,QAAgB;QAC1B,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAElC,MAAM,IAAI,GAAG,GAAsB,EAAE;YACnC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC;QACF,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAA,iBAAO,EAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;SAChD;QACD,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAEM,MAAM,CAAC,IAAI,GAAG,EAAE;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAClC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAC/B,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;gBAC7B,OAAO,MAAM,CAAC;aACf;SACF;QAED,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1B,MAAM,MAAM,GAAG,GAAsB,EAAE;YACrC,qCAAqC;YACrC,IAAI,IAAA,2BAAe,EAAC,IAAI,CAAC,EAAE;gBACzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;oBAC5C,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBACxC,IAAI;wBACF,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;wBACjD,IACE,MAAM,KAAK,IAAI;4BACf,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,EACxD;4BACA,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;yBACtC;qBACF;oBAAC,OAAO,KAAK,EAAE;wBACd,IACE,KAAK,CAAC,IAAI,KAAK,QAAQ;4BACvB,KAAK,CAAC,IAAI,KAAK,QAAQ;4BACvB,KAAK,CAAC,IAAI,KAAK,SAAS,EACxB;4BACA,SAAS;yBACV;wBACD,MAAM,KAAK,CAAC;qBACb;iBACF;aACF;YACD,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,GAAG,EAAE;gBACpC,IAAI,GAAG,GAAG,CAAC;gBACX,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,OAAO,IAAA,iBAAO,EAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;iBAChD;gBACD,OAAO,MAAM,EAAE,CAAC;aACjB;YACD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAA,iBAAO,EAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;SAChD;QACD,OAAO,MAAM,EAAE,CAAC;IAClB,CAAC;IAED,kBAAkB,CAAC,QAAgB;QACjC,MAAM,QAAQ,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,mBAAmB,CAC7B,QAAQ,EACR,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAC5C,CAAC;IACJ,CAAC;IAED,kBAAkB,CAAC,QAAgB,EAAE,QAAgB;QACnD,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC1B,OAAO;SACR;QAED,IAAI,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;YAC9C,OAAO,CACL,IAAA,2BAAiB,EACf,IAAA,qBAAQ,EAAC,QAAQ,EAAE,QAAQ,CAAC,EAC5B,IAAI,CAAC,MAAM,CAAC,WAAW,CACxB,IAAI,IAAI,CACV,CAAC;SACH;QAED,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI;YACF,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,OAAO,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aACnC;SACF;QAAC,OAAO,KAAK,EAAE;YACd,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC1B,MAAM,KAAK,CAAC;SACb;QACD,MAAM,IAAI,KAAK,CACb,2BAA2B,IAAA,yCAAuB,EAAC,SAAS,CAAC,EAAE,CAChE,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,0BAA0B;IACnB,QAAQ,CAAC,QAAgB;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,0BAA0B;IACnB,UAAU,CAAC,IAAI,GAAG,EAAE;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF;AAzHD,oCAyHC"}