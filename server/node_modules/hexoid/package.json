{"name": "hexoid", "version": "2.0.0", "repository": "lukeed/hexoid", "description": "A tiny (190B) and extremely fast utility to generate random IDs of fixed length", "module": "dist/index.mjs", "types": "dist/index.d.ts", "main": "dist/index.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=8"}, "scripts": {"build": "node bin/build", "test": "uvu test -r esm -i collisions"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist"], "keywords": ["id", "uid", "uuid", "random", "generate"], "devDependencies": {"esm": "3.2.25", "terser": "4.8.0", "uvu": "0.5.3"}}