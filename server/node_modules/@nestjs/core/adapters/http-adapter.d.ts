import { HttpServer, RequestMethod, VersioningOptions } from '@nestjs/common';
import { RequestHandler, VersionValue } from '@nestjs/common/interfaces';
import { NestApplicationOptions } from '@nestjs/common/interfaces/nest-application-options.interface';
/**
 * @publicApi
 */
export declare abstract class AbstractHttpAdapter<TServer = any, TRequest = any, TResponse = any> implements HttpServer<TRequest, TResponse> {
    protected instance?: any | undefined;
    protected httpServer: TServer;
    constructor(instance?: any | undefined);
    init(): Promise<void>;
    use(...args: any[]): any;
    get(handler: RequestHandler): any;
    get(path: any, handler: RequestHandler): any;
    post(handler: RequestHandler): any;
    post(path: any, handler: RequestHandler): any;
    head(handler: RequestHandler): any;
    head(path: any, handler: RequestHandler): any;
    delete(handler: RequestHandler): any;
    delete(path: any, handler: RequestHandler): any;
    put(handler: RequestHandler): any;
    put(path: any, handler: RequestHandler): any;
    patch(handler: RequestHandler): any;
    patch(path: any, handler: RequestHandler): any;
    propfind(handler: RequestHandler): any;
    propfind(path: any, handler: RequestHandler): any;
    proppatch(handler: RequestHandler): any;
    proppatch(path: any, handler: RequestHandler): any;
    mkcol(handler: RequestHandler): any;
    mkcol(path: any, handler: RequestHandler): any;
    copy(handler: RequestHandler): any;
    copy(path: any, handler: RequestHandler): any;
    move(handler: RequestHandler): any;
    move(path: any, handler: RequestHandler): any;
    lock(handler: RequestHandler): any;
    lock(path: any, handler: RequestHandler): any;
    unlock(handler: RequestHandler): any;
    unlock(path: any, handler: RequestHandler): any;
    all(handler: RequestHandler): any;
    all(path: any, handler: RequestHandler): any;
    search(handler: RequestHandler): any;
    search(path: any, handler: RequestHandler): any;
    options(handler: RequestHandler): any;
    options(path: any, handler: RequestHandler): any;
    listen(port: string | number, callback?: () => void): any;
    listen(port: string | number, hostname: string, callback?: () => void): any;
    getHttpServer(): TServer;
    setHttpServer(httpServer: TServer): void;
    setInstance<T = any>(instance: T): void;
    getInstance<T = any>(): T;
    normalizePath(path: string): string;
    abstract close(): any;
    abstract initHttpServer(options: NestApplicationOptions): any;
    abstract useStaticAssets(...args: any[]): any;
    abstract setViewEngine(engine: string): any;
    abstract getRequestHostname(request: any): any;
    abstract getRequestMethod(request: any): any;
    abstract getRequestUrl(request: any): any;
    abstract status(response: any, statusCode: number): any;
    abstract reply(response: any, body: any, statusCode?: number): any;
    abstract end(response: any, message?: string): any;
    abstract render(response: any, view: string, options: any): any;
    abstract redirect(response: any, statusCode: number, url: string): any;
    abstract setErrorHandler(handler: Function, prefix?: string): any;
    abstract setNotFoundHandler(handler: Function, prefix?: string): any;
    abstract isHeadersSent(response: any): any;
    abstract setHeader(response: any, name: string, value: string): any;
    abstract registerParserMiddleware(prefix?: string, rawBody?: boolean): any;
    abstract enableCors(options?: any, prefix?: string): any;
    abstract createMiddlewareFactory(requestMethod: RequestMethod): ((path: string, callback: Function) => any) | Promise<(path: string, callback: Function) => any>;
    abstract getType(): string;
    abstract applyVersionFilter(handler: Function, version: VersionValue, versioningOptions: VersioningOptions): (req: TRequest, res: TResponse, next: () => void) => Function;
}
