/**
 * Injection tokens
 */
export declare const CONFIGURATION_SERVICE_TOKEN: unique symbol;
export declare const CONFIGURATION_TOKEN = "CONFIGURATION_TOKEN";
export declare const CONFIGURATION_LOADER = "CONFIGURATION_LOADER";
export declare const VALIDATED_ENV_LOADER = "VALIDATED_ENV_LOADER";
export declare const PARTIAL_CONFIGURATION_KEY = "PARTIAL_CONFIGURATION_KEY";
export declare const PARTIAL_CONFIGURATION_PROPNAME = "KEY";
export declare const VALIDATED_ENV_PROPNAME = "_PROCESS_ENV_VALIDATED";
export declare const AS_PROVIDER_METHOD_KEY = "asProvider";
