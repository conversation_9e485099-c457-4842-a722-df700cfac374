"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerEnumType = exports.createUnionType = void 0;
exports.ArgsType = ArgsType;
exports.Directive = Directive;
exports.Extensions = Extensions;
exports.Field = Field;
exports.HideField = HideField;
exports.InputType = InputType;
exports.InterfaceType = InterfaceType;
exports.ObjectType = ObjectType;
exports.Scalar = Scalar;
exports.dummyFn = dummyFn;
// for webpack this is resolved this way:
// resolve: { // see: https://webpack.js.org/configuration/resolve/
//     alias: {
//         @nestjs/graphql: path.resolve(__dirname, "../node_modules/@nestjs/graphql/dist/extra/graphql-model-shim")
//     }
// }
function ArgsType() {
    return (target) => { };
}
function Directive(sdl) {
    return (target, key) => { };
}
function Extensions(value) {
    return (target, propertyKey) => { };
}
function Field(typeOrOptions, fieldOptions) {
    return (prototype, propertyKey, descriptor) => { };
}
function HideField() {
    return (target, propertyKey) => { };
}
function InputType(nameOrOptions, inputTypeOptions) {
    return (target) => { };
}
function InterfaceType(nameOrOptions, interfaceOptions) {
    return (target) => { };
}
function ObjectType(nameOrOptions, objectTypeOptions) {
    return (target) => { };
}
function Scalar(name, typeFunc) {
    return (target, key, descriptor) => { };
}
function dummyFn() {
    return;
}
exports.createUnionType = dummyFn;
exports.registerEnumType = dummyFn;
