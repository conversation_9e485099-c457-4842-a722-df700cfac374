import * as ts from 'typescript';
import { PluginOptions } from '../merge-options';
export declare function getTypeReferenceAsString(type: ts.Type, typeChecker: ts.TypeChecker, arrayDepth?: number): {
    typeName: string;
    isArray?: boolean;
    arrayDepth?: number;
};
export declare function isPromiseOrObservable(type: string): boolean;
export declare function replaceImportPath(typeReference: string, fileName: string, options: PluginOptions): {
    typeReference: string;
    typeName: string;
    importPath: string;
} | {
    typeReference: string;
    importPath: string;
    typeName?: undefined;
};
export declare function insertAt(string: string, index: number, substring: string): string;
export declare function isDynamicallyAdded(identifier: ts.Node): boolean;
/**
 * when "strict" mode enabled, Type<PERSON> transform the enum type to a union composed of
 * the enum values and the undefined type. Hence, we have to lookup all the union types to get the original type
 * @param type
 * @param typeChecker
 */
export declare function isAutoGeneratedEnumUnion(type: ts.Type, typeChecker: ts.TypeChecker): ts.Type;
/**
 * when "strict" mode enabled, Type<PERSON> transform the type signature of optional properties to
 * the {undefined | T} where T is the original type. Hence, we have to extract the last type of type union
 * @param type
 */
export declare function isAutoGeneratedTypeUnion(type: ts.Type): boolean;
/**
 * Converts Windows specific file paths to posix
 * @param windowsPath
 */
export declare function convertPath(windowsPath: string): string;
//# sourceMappingURL=plugin-utils.d.ts.map