!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).graphqlWs={})}(this,(function(e){"use strict";function t(e){return null===e?"null":Array.isArray(e)?"array":typeof e}function o(e){return"object"===t(e)}function r(e,t){return e.length<124?e:t}const n="graphql-transport-ws";var i=(e=>(e[e.InternalServerError=4500]="InternalServerError",e[e.InternalClientError=4005]="InternalClientError",e[e.BadRequest=4400]="BadRequest",e[e.BadResponse=4004]="BadResponse",e[e.Unauthorized=4401]="Unauthorized",e[e.Forbidden=4403]="Forbidden",e[e.SubprotocolNotAcceptable=4406]="SubprotocolNotAcceptable",e[e.ConnectionInitialisationTimeout=4408]="ConnectionInitialisationTimeout",e[e.ConnectionAcknowledgementTimeout=4504]="ConnectionAcknowledgementTimeout",e[e.SubscriberAlreadyExists=4409]="SubscriberAlreadyExists",e[e.TooManyInitialisationRequests=4429]="TooManyInitialisationRequests",e))(i||{}),a=(e=>(e.ConnectionInit="connection_init",e.ConnectionAck="connection_ack",e.Ping="ping",e.Pong="pong",e.Subscribe="subscribe",e.Next="next",e.Error="error",e.Complete="complete",e))(a||{});function s(e){if(!o(e))throw new Error(`Message is expected to be an object, but got ${t(e)}`);if(!e.type)throw new Error("Message is missing the 'type' property");if("string"!=typeof e.type)throw new Error(`Message is expects the 'type' property to be a string, but got ${t(e.type)}`);switch(e.type){case"connection_init":case"connection_ack":case"ping":case"pong":if(null!=e.payload&&!o(e.payload))throw new Error(`"${e.type}" message expects the 'payload' property to be an object or nullish or missing, but got "${e.payload}"`);break;case"subscribe":if("string"!=typeof e.id)throw new Error(`"${e.type}" message expects the 'id' property to be a string, but got ${t(e.id)}`);if(!e.id)throw new Error(`"${e.type}" message requires a non-empty 'id' property`);if(!o(e.payload))throw new Error(`"${e.type}" message expects the 'payload' property to be an object, but got ${t(e.payload)}`);if("string"!=typeof e.payload.query)throw new Error(`"${e.type}" message payload expects the 'query' property to be a string, but got ${t(e.payload.query)}`);if(null!=e.payload.variables&&!o(e.payload.variables))throw new Error(`"${e.type}" message payload expects the 'variables' property to be a an object or nullish or missing, but got ${t(e.payload.variables)}`);if(null!=e.payload.operationName&&"string"!==t(e.payload.operationName))throw new Error(`"${e.type}" message payload expects the 'operationName' property to be a string or nullish or missing, but got ${t(e.payload.operationName)}`);if(null!=e.payload.extensions&&!o(e.payload.extensions))throw new Error(`"${e.type}" message payload expects the 'extensions' property to be a an object or nullish or missing, but got ${t(e.payload.extensions)}`);break;case"next":if("string"!=typeof e.id)throw new Error(`"${e.type}" message expects the 'id' property to be a string, but got ${t(e.id)}`);if(!e.id)throw new Error(`"${e.type}" message requires a non-empty 'id' property`);if(!o(e.payload))throw new Error(`"${e.type}" message expects the 'payload' property to be an object, but got ${t(e.payload)}`);break;case"error":if("string"!=typeof e.id)throw new Error(`"${e.type}" message expects the 'id' property to be a string, but got ${t(e.id)}`);if(!e.id)throw new Error(`"${e.type}" message requires a non-empty 'id' property`);if(r=e.payload,!(Array.isArray(r)&&r.length>0&&r.every((e=>"message"in e))))throw new Error(`"${e.type}" message expects the 'payload' property to be an array of GraphQL errors, but got ${JSON.stringify(e.payload)}`);break;case"complete":if("string"!=typeof e.id)throw new Error(`"${e.type}" message expects the 'id' property to be a string, but got ${t(e.id)}`);if(!e.id)throw new Error(`"${e.type}" message requires a non-empty 'id' property`);break;default:throw new Error(`Invalid message 'type' property "${e.type}"`)}var r;return e}function c(e,t){return s("string"==typeof e?JSON.parse(e,t):e)}function p(e,t){return s(e),JSON.stringify(e,t)}class l extends Error{name="TerminatedCloseEvent";message="4499: Terminated";code=4499;reason="Terminated";wasClean=!1}function d(e){return o(e)&&"code"in e&&"reason"in e}e.CloseCode=i,e.DEPRECATED_GRAPHQL_WS_PROTOCOL="graphql-ws",e.GRAPHQL_TRANSPORT_WS_PROTOCOL=n,e.MessageType=a,e.TerminatedCloseEvent=l,e.createClient=function(e){const{url:t,connectionParams:o,lazy:s=!0,onNonLazyError:y=console.error,lazyCloseTimeout:u=0,keepAlive:g=0,disablePong:m,connectionAckWaitTimeout:f=0,retryAttempts:b=5,retryWait:w=async function(e){const t=Math.pow(2,e);await new Promise((e=>setTimeout(e,1e3*t+Math.floor(2700*Math.random()+300))))},shouldRetry:h=d,on:x,webSocketImpl:E,generateID:S=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>{const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))},jsonMessageReplacer:v,jsonMessageReviver:C}=e;let T;if(E){if(!("function"==typeof($=E)&&"constructor"in $&&"CLOSED"in $&&"CLOSING"in $&&"CONNECTING"in $&&"OPEN"in $))throw new Error("Invalid WebSocket implementation provided");T=E}else"undefined"!=typeof WebSocket?T=WebSocket:"undefined"!=typeof global?T=global.WebSocket||global.MozWebSocket:"undefined"!=typeof window&&(T=window.WebSocket||window.MozWebSocket);var $;if(!T)throw new Error("WebSocket implementation missing; on Node you can `import WebSocket from 'ws';` and pass `webSocketImpl: WebSocket` to `createClient`");const k=T,N=(()=>{const e=(()=>{const e={};return{on:(t,o)=>(e[t]=o,()=>{delete e[t]}),emit(t){"id"in t&&e[t.id]?.(t)}}})(),t={connecting:x?.connecting?[x.connecting]:[],opened:x?.opened?[x.opened]:[],connected:x?.connected?[x.connected]:[],ping:x?.ping?[x.ping]:[],pong:x?.pong?[x.pong]:[],message:x?.message?[e.emit,x.message]:[e.emit],closed:x?.closed?[x.closed]:[],error:x?.error?[x.error]:[]};return{onMessage:e.on,on(e,o){const r=t[e];return r.push(o),()=>{r.splice(r.indexOf(o),1)}},emit(e,...o){for(const r of[...t[e]])r(...o)}}})();function I(e){const t=[N.on("error",(o=>{t.forEach((e=>e())),e(o)})),N.on("closed",(o=>{t.forEach((e=>e())),e(o)}))]}let P,A,M=0,O=!1,R=0,q=!1;async function W(){clearTimeout(A);const[e,s]=await(P??(P=new Promise(((e,s)=>(async()=>{if(O){if(await w(R),!M)return P=void 0,s({code:1e3,reason:"All Subscriptions Gone"});R++}N.emit("connecting",O);const d=new k("function"==typeof t?await t():t,n);let y,u;function b(){isFinite(g)&&g>0&&(clearTimeout(u),u=setTimeout((()=>{d.readyState===k.OPEN&&(d.send(p({type:a.Ping})),N.emit("ping",!1,void 0))}),g))}I((e=>{P=void 0,clearTimeout(y),clearTimeout(u),s(e),e instanceof l&&(d.close(4499,"Terminated"),d.onerror=null,d.onclose=null)})),d.onerror=e=>N.emit("error",e),d.onclose=e=>N.emit("closed",e),d.onopen=async()=>{try{N.emit("opened",d);const e="function"==typeof o?await o():o;if(d.readyState!==k.OPEN)return;d.send(p(e?{type:a.ConnectionInit,payload:e}:{type:a.ConnectionInit},v)),isFinite(f)&&f>0&&(y=setTimeout((()=>{d.close(i.ConnectionAcknowledgementTimeout,"Connection acknowledgement timeout")}),f)),b()}catch(e){N.emit("error",e),d.close(i.InternalClientError,r(e instanceof Error?e.message:String(e),"Internal client error"))}};let h=!1;d.onmessage=({data:t})=>{try{const o=c(t,C);if(N.emit("message",o),"ping"===o.type||"pong"===o.type)return N.emit(o.type,!0,o.payload),void("pong"===o.type?b():m||(d.send(p(o.payload?{type:a.Pong,payload:o.payload}:{type:a.Pong})),N.emit("pong",!1,o.payload)));if(h)return;if(o.type!==a.ConnectionAck)throw new Error(`First message cannot be of type ${o.type}`);clearTimeout(y),h=!0,N.emit("connected",d,o.payload,O),O=!1,R=0,e([d,new Promise(((e,t)=>I(t)))])}catch(e){d.onmessage=null,N.emit("error",e),d.close(i.BadResponse,r(e instanceof Error?e.message:String(e),"Bad response"))}}})()))));e.readyState===k.CLOSING&&await s;let d=()=>{};const y=new Promise((e=>d=e));return[e,d,Promise.race([y.then((()=>{if(!M){const t=()=>e.close(1e3,"Normal Closure");isFinite(u)&&u>0?A=setTimeout((()=>{e.readyState===k.OPEN&&t()}),u):t()}})),s])]}function j(e){if(d(e)&&(t=e.code,![1e3,1001,1006,1005,1012,1013,1014].includes(t)&&t>=1e3&&t<=1999||[i.InternalServerError,i.InternalClientError,i.BadRequest,i.BadResponse,i.Unauthorized,i.SubprotocolNotAcceptable,i.SubscriberAlreadyExists,i.TooManyInitialisationRequests].includes(e.code)))throw e;var t;if(q)return!1;if(d(e)&&1e3===e.code)return M>0;if(!b||R>=b)throw e;if(!h(e))throw e;return O=!0}function _(e,t){const o=S(e);let r=!1,n=!1,i=()=>{M--,r=!0};return(async()=>{for(M++;;)try{const[s,c,l]=await W();if(r)return c();const d=N.onMessage(o,(e=>{switch(e.type){case a.Next:return void t.next(e.payload);case a.Error:return n=!0,r=!0,t.error(e.payload),void i();case a.Complete:return r=!0,void i()}}));return s.send(p({id:o,type:a.Subscribe,payload:e},v)),i=()=>{r||s.readyState!==k.OPEN||s.send(p({id:o,type:a.Complete},v)),M--,r=!0,c()},void await l.finally(d)}catch(e){if(!j(e))return}})().then((()=>{n||t.complete()})).catch((e=>{t.error(e)})),()=>{r||i()}}return s||(async()=>{for(M++;;)try{const[,,e]=await W();await e}catch(e){try{if(!j(e))return}catch(e){return y?.(e)}}})(),{on:N.on,subscribe:_,iterate(e){const t=[],o={done:!1,error:null,resolve:()=>{}},r=_(e,{next(e){t.push(e),o.resolve()},error(e){o.done=!0,o.error=e,o.resolve()},complete(){o.done=!0,o.resolve()}}),n=async function*(){for(;;){for(t.length||await new Promise((e=>o.resolve=e));t.length;)yield t.shift();if(o.error)throw o.error;if(o.done)return}}();return n.throw=async e=>(o.done||(o.done=!0,o.error=e,o.resolve()),{done:!0,value:void 0}),n.return=async()=>(r(),{done:!0,value:void 0}),n},async dispose(){if(q=!0,P){const[e]=await P;e.close(1e3,"Normal Closure")}},terminate(){P&&N.emit("closed",new l)}}},e.parseMessage=c,e.stringifyMessage=p,e.validateMessage=s}));
