{"version": 3, "file": "iterate.d.ts", "sourceRoot": "", "sources": ["../src/iterate.ts"], "names": [], "mappings": "AAQA,qBAAa,qBAAqB,CAAC,CAAC,CAAE,YAAW,gBAAgB,CAAC,CAAC,CAAC;IAIpD,OAAO,CAAC,MAAM;IAH1B;;OAEG;gBACiB,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEvC;;OAEG;IACH,IAAI,IAAI,cAAc,CAAC,CAAC,CAAC;IAIzB;;;OAGG;IACH,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI;IAIzB;;OAEG;IACH,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC;IAI3D;;OAEG;IACH,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,OAAO,GAAG,qBAAqB,CAAC,CAAC,CAAC;IACpE,MAAM,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,OAAO,IAAI,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC;IAKtF;;OAEG;IACH,MAAM,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;IAI9E;;;;OAIG;IACH,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,qBAAqB,CAAC,CAAC,CAAC;IAI7C;;;;OAIG;IACH,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,qBAAqB,CAAC,CAAC,CAAC;IAIzC;;;;;OAKG;IACH,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,SAAW,GAAG,qBAAqB,CAAC,CAAC,CAAC;IAI9D;;OAEG;IACH,OAAO,IAAI,qBAAqB,CAAC,CAAC,SAAS,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAIrE;;;;OAIG;IACH,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;IAC1C;;;;;OAKG;IACH,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC;IAoB9D;;;;;;OAMG;IACH,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS;IACrE;;;;;OAKG;IACH,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,CAAC,GAAG,SAAS;IAcrD;;;;OAIG;IACH,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO;IAW3B;;OAEG;IACH,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,OAAO;IAW/C;;OAEG;IACH,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,GAAG,OAAO;IAWhD;;OAEG;IACH,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,GAAG,IAAI;IAW1C;;OAEG;IACH,IAAI,CAAC,SAAS,SAAM,GAAG,MAAM;IAa7B;;;OAGG;IACH,OAAO,IAAI,CAAC,EAAE;IAId;;;OAGG;IACH,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC;IAWf;;;OAGG;IACH,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;CAG9D;AAED;;GAEG;AACH,wBAAgB,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAE1F;AAED;;GAEG;AACH,wBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAEnH;AAED,eAAe,OAAO,CAAA"}