{"name": "@josephg/resolvable", "version": "1.0.1", "description": "Promise with .resolve() and .reject() methods", "main": "index.js", "types": "index.d.ts", "scripts": {"prepare": "npm run build", "build": "tsc"}, "files": ["index.*"], "repository": "**************:josephg/resolvable.git", "author": "<PERSON> <<EMAIL>>", "license": "ISC", "devDependencies": {"@types/node": "^15.0.1", "typescript": "^4.2.4"}}