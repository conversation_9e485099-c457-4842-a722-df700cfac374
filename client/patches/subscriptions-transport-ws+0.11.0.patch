diff --git a/node_modules/subscriptions-transport-ws/dist/server.d.ts b/node_modules/subscriptions-transport-ws/dist/server.d.ts
index d46ca46..3105b49 100644
--- a/node_modules/subscriptions-transport-ws/dist/server.d.ts
+++ b/node_modules/subscriptions-transport-ws/dist/server.d.ts
@@ -1,5 +1,6 @@
 /// <reference types="node" />
 import * as WebSocket from 'ws';
+import { WebSocketServer } from 'ws';
 import { ExecutionResult, GraphQLSchema, DocumentNode, ValidationContext, ExecutionArgs, SubscriptionArgs } from 'graphql';
 import { IncomingMessage } from 'http';
 export declare type ExecutionIterator = AsyncIterator<ExecutionResult>;
@@ -64,9 +65,9 @@ export declare class SubscriptionServer {
     private keepAlive;
     private closeHandler;
     private specifiedRules;
-    static create(options: ServerOptions, socketOptionsOrServer: WebSocket.ServerOptions | WebSocket.Server): SubscriptionServer;
-    constructor(options: ServerOptions, socketOptionsOrServer: WebSocket.ServerOptions | WebSocket.Server);
-    get server(): WebSocket.Server;
+    static create(options: ServerOptions, socketOptionsOrServer: WebSocket.ServerOptions | WebSocketServer): SubscriptionServer;
+    constructor(options: ServerOptions, socketOptionsOrServer: WebSocket.ServerOptions | WebSocketServer);
+    get server(): WebSocketServer;
     close(): void;
     private loadExecutor;
     private unsubscribe;
