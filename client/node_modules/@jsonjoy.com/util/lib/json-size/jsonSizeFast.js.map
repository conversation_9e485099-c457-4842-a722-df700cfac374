{"version": 3, "file": "jsonSizeFast.js", "sourceRoot": "", "sources": ["../../src/json-size/jsonSizeFast.ts"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS,GAAG,CAAC,GAAc,EAAU,EAAE;IAC3C,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;QAAE,IAAI,IAAI,IAAA,oBAAY,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,GAA4B,EAAU,EAAE;IAC1D,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,MAAM,GAAG,IAAI,GAAG;QAAE,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC;YAAE,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,IAAA,oBAAY,EAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACpG,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAiCK,MAAM,YAAY,GAAG,CAAC,KAAc,EAAU,EAAE;IACrD,IAAI,KAAK,KAAK,IAAI;QAAE,OAAO,CAAC,CAAC;IAC7B,QAAQ,OAAO,KAAK,EAAE,CAAC;QACrB,KAAK,QAAQ;YACX,OAAO,CAAC,CAAC;QACX,KAAK,QAAQ;YACX,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,KAAK,SAAS;YACZ,OAAO,CAAC,CAAC;IACb,CAAC;IACD,IAAI,KAAK,YAAY,KAAK;QAAE,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;IACpD,OAAO,UAAU,CAAC,KAAgC,CAAC,CAAC;AACtD,CAAC,CAAC;AAZW,QAAA,YAAY,gBAYvB"}