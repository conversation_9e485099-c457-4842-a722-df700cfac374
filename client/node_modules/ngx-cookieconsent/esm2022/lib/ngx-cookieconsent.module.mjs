import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { provideNgcCookieConsent } from './ngx-cookieconsent.config';
import * as i0 from "@angular/core";
/**
 * Main module of the library
 */
export class NgcCookieConsentModule {
    static forRoot(config) {
        return {
            ngModule: NgcCookieConsentModule,
            providers: provideNgcCookieConsent(config)
        };
    }
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.2.12", ngImport: i0, type: NgcCookieConsentModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }
    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: "14.0.0", version: "16.2.12", ngImport: i0, type: NgcCookieConsentModule, imports: [CommonModule] }); }
    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: "12.0.0", version: "16.2.12", ngImport: i0, type: NgcCookieConsentModule, imports: [CommonModule] }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.2.12", ngImport: i0, type: NgcCookieConsentModule, decorators: [{
            type: NgModule,
            args: [{
                    imports: [
                        CommonModule
                    ]
                }]
        }] });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibmd4LWNvb2tpZWNvbnNlbnQubW9kdWxlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vbGlicy9uZ3gtY29va2llY29uc2VudC9zcmMvbGliL25neC1jb29raWVjb25zZW50Lm1vZHVsZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxPQUFPLEVBQUUsUUFBUSxFQUF1QixNQUFNLGVBQWUsQ0FBQztBQUM5RCxPQUFPLEVBQUUsWUFBWSxFQUFFLE1BQU0saUJBQWlCLENBQUM7QUFFL0MsT0FBTyxFQUFFLHVCQUF1QixFQUFFLE1BQU0sNEJBQTRCLENBQUM7O0FBRXJFOztHQUVHO0FBTUgsTUFBTSxPQUFPLHNCQUFzQjtJQUVqQyxNQUFNLENBQUMsT0FBTyxDQUFDLE1BQThCO1FBQzNDLE9BQU87WUFDTCxRQUFRLEVBQUUsc0JBQXNCO1lBQ2hDLFNBQVMsRUFBRSx1QkFBdUIsQ0FBQyxNQUFNLENBQUM7U0FDM0MsQ0FBQztJQUNKLENBQUM7K0dBUFUsc0JBQXNCO2dIQUF0QixzQkFBc0IsWUFIL0IsWUFBWTtnSEFHSCxzQkFBc0IsWUFIL0IsWUFBWTs7NEZBR0gsc0JBQXNCO2tCQUxsQyxRQUFRO21CQUFDO29CQUNSLE9BQU8sRUFBRTt3QkFDUCxZQUFZO3FCQUNiO2lCQUNGIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmdNb2R1bGUsIE1vZHVsZVdpdGhQcm92aWRlcnMgfSBmcm9tICdAYW5ndWxhci9jb3JlJztcbmltcG9ydCB7IENvbW1vbk1vZHVsZSB9IGZyb20gJ0Bhbmd1bGFyL2NvbW1vbic7XG5pbXBvcnQgeyBXaW5kb3dTZXJ2aWNlLCBOZ2NDb29raWVDb25zZW50Q29uZmlnLCBOZ2NDb29raWVDb25zZW50U2VydmljZSB9IGZyb20gJy4vc2VydmljZS9pbmRleCc7XG5pbXBvcnQgeyBwcm92aWRlTmdjQ29va2llQ29uc2VudCB9IGZyb20gJy4vbmd4LWNvb2tpZWNvbnNlbnQuY29uZmlnJztcblxuLyoqXG4gKiBNYWluIG1vZHVsZSBvZiB0aGUgbGlicmFyeVxuICovXG5ATmdNb2R1bGUoe1xuICBpbXBvcnRzOiBbXG4gICAgQ29tbW9uTW9kdWxlXG4gIF1cbn0pXG5leHBvcnQgY2xhc3MgTmdjQ29va2llQ29uc2VudE1vZHVsZSB7XG5cbiAgc3RhdGljIGZvclJvb3QoY29uZmlnOiBOZ2NDb29raWVDb25zZW50Q29uZmlnKTogTW9kdWxlV2l0aFByb3ZpZGVyczxOZ2NDb29raWVDb25zZW50TW9kdWxlPiB7XG4gICAgcmV0dXJuIHtcbiAgICAgIG5nTW9kdWxlOiBOZ2NDb29raWVDb25zZW50TW9kdWxlLFxuICAgICAgcHJvdmlkZXJzOiBwcm92aWRlTmdjQ29va2llQ29uc2VudChjb25maWcpXG4gICAgfTtcbiAgfVxufVxuIl19