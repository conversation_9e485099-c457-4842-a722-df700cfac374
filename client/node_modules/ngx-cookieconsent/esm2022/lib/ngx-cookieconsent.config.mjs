import { WindowService, NgcCookieConsentConfig, NgcCookieConsentService } from './service/index';
/**
 * Provides the necessary dependencies to use the library in a Standalone Application
 */
export function provideNgcCookieConsent(config) {
    return [
        [WindowService, { provide: NgcCookieConsentConfig, useValue: config }, NgcCookieConsentService]
    ];
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibmd4LWNvb2tpZWNvbnNlbnQuY29uZmlnLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vbGlicy9uZ3gtY29va2llY29uc2VudC9zcmMvbGliL25neC1jb29raWVjb25zZW50LmNvbmZpZy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFQSxPQUFPLEVBQUUsYUFBYSxFQUFFLHNCQUFzQixFQUFFLHVCQUF1QixFQUFFLE1BQU0saUJBQWlCLENBQUM7QUFFakc7O0dBRUc7QUFFSCxNQUFNLFVBQVUsdUJBQXVCLENBQUMsTUFBOEI7SUFDcEUsT0FBTztRQUNMLENBQUMsYUFBYSxFQUFFLEVBQUUsT0FBTyxFQUFFLHNCQUFzQixFQUFFLFFBQVEsRUFBRSxNQUFNLEVBQUUsRUFBRSx1QkFBdUIsQ0FBQztLQUNoRyxDQUFBO0FBQ0gsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5nTW9kdWxlLCBNb2R1bGVXaXRoUHJvdmlkZXJzLCBQcm92aWRlciB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xuaW1wb3J0IHsgQ29tbW9uTW9kdWxlIH0gZnJvbSAnQGFuZ3VsYXIvY29tbW9uJztcbmltcG9ydCB7IFdpbmRvd1NlcnZpY2UsIE5nY0Nvb2tpZUNvbnNlbnRDb25maWcsIE5nY0Nvb2tpZUNvbnNlbnRTZXJ2aWNlIH0gZnJvbSAnLi9zZXJ2aWNlL2luZGV4JztcblxuLyoqXG4gKiBQcm92aWRlcyB0aGUgbmVjZXNzYXJ5IGRlcGVuZGVuY2llcyB0byB1c2UgdGhlIGxpYnJhcnkgaW4gYSBTdGFuZGFsb25lIEFwcGxpY2F0aW9uXG4gKi9cblxuZXhwb3J0IGZ1bmN0aW9uIHByb3ZpZGVOZ2NDb29raWVDb25zZW50KGNvbmZpZzogTmdjQ29va2llQ29uc2VudENvbmZpZyk6IFByb3ZpZGVyW10ge1xuICByZXR1cm4gW1xuICAgIFtXaW5kb3dTZXJ2aWNlLCB7IHByb3ZpZGU6IE5nY0Nvb2tpZUNvbnNlbnRDb25maWcsIHVzZVZhbHVlOiBjb25maWcgfSwgTmdjQ29va2llQ29uc2VudFNlcnZpY2VdXG4gIF1cbn0iXX0=