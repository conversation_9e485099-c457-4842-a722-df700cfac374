/*! For license information please see tsparticles.move.base.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var i="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var n in i)("object"==typeof exports?exports:e)[n]=i[n]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},i={};function n(e){var o=i[e];if(void 0!==o)return o.exports;var a=i[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};n.r(o),n.d(o,{loadBaseMover:()=>l});var a=n(303);const s=1;function r(e,t,i,n,o,r){!function(e,t){const i=e.options,n=i.move.path;if(!n.enable)return;if(e.lastPathTime<=e.pathDelay)return void(e.lastPathTime+=t.value);const o=e.pathGenerator?.generate(e,t);o&&e.velocity.addTo(o);n.clamp&&(e.velocity.x=(0,a.clamp)(e.velocity.x,-s,s),e.velocity.y=(0,a.clamp)(e.velocity.y,-s,s));e.lastPathTime-=e.pathDelay}(e,r);const c=e.gravity,l=c?.enable&&c.inverse?-s:s;o&&i&&(e.velocity.x+=o*r.factor/(60*i)),c?.enable&&i&&(e.velocity.y+=l*(c.acceleration*r.factor)/(60*i));const p=e.moveDecay;e.velocity.multTo(p);const y=e.velocity.mult(i);c?.enable&&n>0&&(!c.inverse&&y.y>=0&&y.y>=n||c.inverse&&y.y<=0&&y.y<=-n)&&(y.y=l*n,i&&(e.velocity.y=y.y/i));const v=e.options.zIndex,d=(s-e.zIndexFactor)**v.velocityRate;y.multTo(d);const{position:u}=e;u.addTo(y),t.vibrate&&(u.x+=Math.sin(u.x*Math.cos(u.y)),u.y+=Math.cos(u.y*Math.sin(u.x)))}class c{init(e){const t=e.options.move.gravity;e.gravity={enable:t.enable,acceleration:(0,a.getRangeValue)(t.acceleration),inverse:t.inverse},function(e){const t=e.container,i=e.options.move.spin;if(!i.enable)return;const n=i.position??{x:50,y:50},o={x:.01*n.x*t.canvas.size.width,y:.01*n.y*t.canvas.size.height},s=e.getPosition(),r=(0,a.getDistance)(s,o),c=(0,a.getRangeValue)(i.acceleration);e.retina.spinAcceleration=c*t.retina.pixelRatio,e.spin={center:o,direction:e.velocity.x>=0?a.RotateDirection.clockwise:a.RotateDirection.counterClockwise,angle:e.velocity.angle,radius:r,acceleration:e.retina.spinAcceleration}}(e)}isEnabled(e){return!e.destroyed&&e.options.move.enable}move(e,t){const i=e.options,n=i.move;if(!n.enable)return;const o=e.container,c=o.retina.pixelRatio;e.retina.moveSpeed??=(0,a.getRangeValue)(n.speed)*c,e.retina.moveDrift??=(0,a.getRangeValue)(e.options.move.drift)*c;const l=function(e){return e.slow.inRange?e.slow.factor:s}(e),p=e.retina.moveSpeed*o.retina.reduceFactor,y=e.retina.moveDrift,v=(0,a.getRangeMax)(i.size.value)*c,d=p*(n.size?e.getRadius()/v:1)*l*(t.factor||1)/2,u=e.retina.maxSpeed??o.retina.maxSpeed;n.spin.enable?function(e,t){const i=e.container;if(!e.spin)return;const n={x:e.spin.direction===a.RotateDirection.clockwise?Math.cos:Math.sin,y:e.spin.direction===a.RotateDirection.clockwise?Math.sin:Math.cos};e.position.x=e.spin.center.x+e.spin.radius*n.x(e.spin.angle),e.position.y=e.spin.center.y+e.spin.radius*n.y(e.spin.angle),e.spin.radius+=e.spin.acceleration;const o=Math.max(i.canvas.size.width,i.canvas.size.height),r=.5*o;e.spin.radius>r?(e.spin.radius=r,e.spin.acceleration*=-s):e.spin.radius<0&&(e.spin.radius=0,e.spin.acceleration*=-s),e.spin.angle+=.01*t*(s-e.spin.radius/o)}(e,d):r(e,n,d,u,y,t),function(e){const t=e.initialPosition,{dx:i,dy:n}=(0,a.getDistances)(t,e.position),o=Math.abs(i),s=Math.abs(n),{maxDistance:r}=e.retina,c=r.horizontal,l=r.vertical;if(!c&&!l)return;if((c&&o>=c||l&&s>=l)&&!e.misplaced)e.misplaced=!!c&&o>c||!!l&&s>l,c&&(e.velocity.x=.5*e.velocity.y-e.velocity.x),l&&(e.velocity.y=.5*e.velocity.x-e.velocity.y);else if((!c||o<c)&&(!l||s<l)&&e.misplaced)e.misplaced=!1;else if(e.misplaced){const i=e.position,n=e.velocity;c&&(i.x<t.x&&n.x<0||i.x>t.x&&n.x>0)&&(n.x*=-(0,a.getRandom)()),l&&(i.y<t.y&&n.y<0||i.y>t.y&&n.y>0)&&(n.y*=-(0,a.getRandom)())}}(e)}}async function l(e,t=!0){(0,a.assertValidVersion)(e,"3.7.1"),await e.addMover("base",(()=>Promise.resolve(new c)),t)}return o})()));