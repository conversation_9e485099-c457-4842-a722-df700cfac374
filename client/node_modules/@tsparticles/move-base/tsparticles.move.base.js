/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.7.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/BaseMover.js":
/*!***********************************!*\
  !*** ./dist/browser/BaseMover.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseMover: () => (/* binding */ BaseMover)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n\n\nconst diffFactor = 2,\n  defaultSizeFactor = 1,\n  defaultDeltaFactor = 1;\nclass BaseMover {\n  init(particle) {\n    const options = particle.options,\n      gravityOptions = options.move.gravity;\n    particle.gravity = {\n      enable: gravityOptions.enable,\n      acceleration: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(gravityOptions.acceleration),\n      inverse: gravityOptions.inverse\n    };\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.initSpin)(particle);\n  }\n  isEnabled(particle) {\n    return !particle.destroyed && particle.options.move.enable;\n  }\n  move(particle, delta) {\n    const particleOptions = particle.options,\n      moveOptions = particleOptions.move;\n    if (!moveOptions.enable) {\n      return;\n    }\n    const container = particle.container,\n      pxRatio = container.retina.pixelRatio;\n    particle.retina.moveSpeed ??= (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(moveOptions.speed) * pxRatio;\n    particle.retina.moveDrift ??= (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(particle.options.move.drift) * pxRatio;\n    const slowFactor = (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.getProximitySpeedFactor)(particle),\n      baseSpeed = particle.retina.moveSpeed * container.retina.reduceFactor,\n      moveDrift = particle.retina.moveDrift,\n      maxSize = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeMax)(particleOptions.size.value) * pxRatio,\n      sizeFactor = moveOptions.size ? particle.getRadius() / maxSize : defaultSizeFactor,\n      deltaFactor = delta.factor || defaultDeltaFactor,\n      moveSpeed = baseSpeed * sizeFactor * slowFactor * deltaFactor / diffFactor,\n      maxSpeed = particle.retina.maxSpeed ?? container.retina.maxSpeed;\n    if (moveOptions.spin.enable) {\n      (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.spin)(particle, moveSpeed);\n    } else {\n      (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.move)(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta);\n    }\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.applyDistance)(particle);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/move-base/./dist/browser/BaseMover.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyDistance: () => (/* binding */ applyDistance),\n/* harmony export */   applyPath: () => (/* binding */ applyPath),\n/* harmony export */   getProximitySpeedFactor: () => (/* binding */ getProximitySpeedFactor),\n/* harmony export */   initSpin: () => (/* binding */ initSpin),\n/* harmony export */   move: () => (/* binding */ move),\n/* harmony export */   spin: () => (/* binding */ spin)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst half = 0.5,\n  minVelocity = 0,\n  identity = 1,\n  moveSpeedFactor = 60,\n  minSpinRadius = 0,\n  spinFactor = 0.01;\nfunction applyDistance(particle) {\n  const initialPosition = particle.initialPosition,\n    {\n      dx,\n      dy\n    } = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(initialPosition, particle.position),\n    dxFixed = Math.abs(dx),\n    dyFixed = Math.abs(dy),\n    {\n      maxDistance\n    } = particle.retina,\n    hDistance = maxDistance.horizontal,\n    vDistance = maxDistance.vertical;\n  if (!hDistance && !vDistance) {\n    return;\n  }\n  const hasHDistance = (hDistance && dxFixed >= hDistance) ?? false,\n    hasVDistance = (vDistance && dyFixed >= vDistance) ?? false;\n  if ((hasHDistance || hasVDistance) && !particle.misplaced) {\n    particle.misplaced = !!hDistance && dxFixed > hDistance || !!vDistance && dyFixed > vDistance;\n    if (hDistance) {\n      particle.velocity.x = particle.velocity.y * half - particle.velocity.x;\n    }\n    if (vDistance) {\n      particle.velocity.y = particle.velocity.x * half - particle.velocity.y;\n    }\n  } else if ((!hDistance || dxFixed < hDistance) && (!vDistance || dyFixed < vDistance) && particle.misplaced) {\n    particle.misplaced = false;\n  } else if (particle.misplaced) {\n    const pos = particle.position,\n      vel = particle.velocity;\n    if (hDistance && (pos.x < initialPosition.x && vel.x < minVelocity || pos.x > initialPosition.x && vel.x > minVelocity)) {\n      vel.x *= -(0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n    }\n    if (vDistance && (pos.y < initialPosition.y && vel.y < minVelocity || pos.y > initialPosition.y && vel.y > minVelocity)) {\n      vel.y *= -(0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n    }\n  }\n}\nfunction move(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta) {\n  applyPath(particle, delta);\n  const gravityOptions = particle.gravity,\n    gravityFactor = gravityOptions?.enable && gravityOptions.inverse ? -identity : identity;\n  if (moveDrift && moveSpeed) {\n    particle.velocity.x += moveDrift * delta.factor / (moveSpeedFactor * moveSpeed);\n  }\n  if (gravityOptions?.enable && moveSpeed) {\n    particle.velocity.y += gravityFactor * (gravityOptions.acceleration * delta.factor) / (moveSpeedFactor * moveSpeed);\n  }\n  const decay = particle.moveDecay;\n  particle.velocity.multTo(decay);\n  const velocity = particle.velocity.mult(moveSpeed);\n  if (gravityOptions?.enable && maxSpeed > minVelocity && (!gravityOptions.inverse && velocity.y >= minVelocity && velocity.y >= maxSpeed || gravityOptions.inverse && velocity.y <= minVelocity && velocity.y <= -maxSpeed)) {\n    velocity.y = gravityFactor * maxSpeed;\n    if (moveSpeed) {\n      particle.velocity.y = velocity.y / moveSpeed;\n    }\n  }\n  const zIndexOptions = particle.options.zIndex,\n    zVelocityFactor = (identity - particle.zIndexFactor) ** zIndexOptions.velocityRate;\n  velocity.multTo(zVelocityFactor);\n  const {\n    position\n  } = particle;\n  position.addTo(velocity);\n  if (moveOptions.vibrate) {\n    position.x += Math.sin(position.x * Math.cos(position.y));\n    position.y += Math.cos(position.y * Math.sin(position.x));\n  }\n}\nfunction spin(particle, moveSpeed) {\n  const container = particle.container;\n  if (!particle.spin) {\n    return;\n  }\n  const updateFunc = {\n    x: particle.spin.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.clockwise ? Math.cos : Math.sin,\n    y: particle.spin.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.clockwise ? Math.sin : Math.cos\n  };\n  particle.position.x = particle.spin.center.x + particle.spin.radius * updateFunc.x(particle.spin.angle);\n  particle.position.y = particle.spin.center.y + particle.spin.radius * updateFunc.y(particle.spin.angle);\n  particle.spin.radius += particle.spin.acceleration;\n  const maxCanvasSize = Math.max(container.canvas.size.width, container.canvas.size.height),\n    halfMaxSize = maxCanvasSize * half;\n  if (particle.spin.radius > halfMaxSize) {\n    particle.spin.radius = halfMaxSize;\n    particle.spin.acceleration *= -identity;\n  } else if (particle.spin.radius < minSpinRadius) {\n    particle.spin.radius = minSpinRadius;\n    particle.spin.acceleration *= -identity;\n  }\n  particle.spin.angle += moveSpeed * spinFactor * (identity - particle.spin.radius / maxCanvasSize);\n}\nfunction applyPath(particle, delta) {\n  const particlesOptions = particle.options,\n    pathOptions = particlesOptions.move.path,\n    pathEnabled = pathOptions.enable;\n  if (!pathEnabled) {\n    return;\n  }\n  if (particle.lastPathTime <= particle.pathDelay) {\n    particle.lastPathTime += delta.value;\n    return;\n  }\n  const path = particle.pathGenerator?.generate(particle, delta);\n  if (path) {\n    particle.velocity.addTo(path);\n  }\n  if (pathOptions.clamp) {\n    particle.velocity.x = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(particle.velocity.x, -identity, identity);\n    particle.velocity.y = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(particle.velocity.y, -identity, identity);\n  }\n  particle.lastPathTime -= particle.pathDelay;\n}\nfunction getProximitySpeedFactor(particle) {\n  return particle.slow.inRange ? particle.slow.factor : identity;\n}\nfunction initSpin(particle) {\n  const container = particle.container,\n    options = particle.options,\n    spinOptions = options.move.spin;\n  if (!spinOptions.enable) {\n    return;\n  }\n  const spinPos = spinOptions.position ?? {\n      x: 50,\n      y: 50\n    },\n    spinFactor = 0.01,\n    spinCenter = {\n      x: spinPos.x * spinFactor * container.canvas.size.width,\n      y: spinPos.y * spinFactor * container.canvas.size.height\n    },\n    pos = particle.getPosition(),\n    distance = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistance)(pos, spinCenter),\n    spinAcceleration = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(spinOptions.acceleration);\n  particle.retina.spinAcceleration = spinAcceleration * container.retina.pixelRatio;\n  const minVelocity = 0;\n  particle.spin = {\n    center: spinCenter,\n    direction: particle.velocity.x >= minVelocity ? _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.clockwise : _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.counterClockwise,\n    angle: particle.velocity.angle,\n    radius: distance,\n    acceleration: particle.retina.spinAcceleration\n  };\n}\n\n//# sourceURL=webpack://@tsparticles/move-base/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadBaseMover: () => (/* binding */ loadBaseMover)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _BaseMover_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseMover.js */ \"./dist/browser/BaseMover.js\");\n\n\nasync function loadBaseMover(engine, refresh = true) {\n  (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.assertValidVersion)(engine, \"3.7.1\");\n  await engine.addMover(\"base\", () => {\n    return Promise.resolve(new _BaseMover_js__WEBPACK_IMPORTED_MODULE_1__.BaseMover());\n  }, refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/move-base/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});