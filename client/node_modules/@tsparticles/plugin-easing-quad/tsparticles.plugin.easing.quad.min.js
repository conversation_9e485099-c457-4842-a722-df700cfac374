/*! For license information please see tsparticles.plugin.easing.quad.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var r="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var a in r)("object"==typeof exports?exports:e)[a]=r[a]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},r={};function a(e){var o=r[e];if(void 0!==o)return o.exports;var n=r[e]={exports:{}};return t[e](n,n.exports,a),n.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};a.r(o),a.d(o,{loadEasingQuadPlugin:()=>i});var n=a(303);async function i(e,t=!0){(0,n.assertValidVersion)(e,"3.7.1"),await e.addEasing(n.EasingType.easeInQuad,(e=>e**2),!1),await e.addEasing(n.EasingType.easeOutQuad,(e=>1-(1-e)**2),!1),await e.addEasing(n.EasingType.easeInOutQuad,(e=>e<.5?2*e**2:1-(-2*e+2)**2/2),!1),await e.refresh(t)}return o})()));