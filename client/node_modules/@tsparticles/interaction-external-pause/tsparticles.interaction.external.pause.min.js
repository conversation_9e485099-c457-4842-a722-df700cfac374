/*! For license information please see tsparticles.interaction.external.pause.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var r="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var o in r)("object"==typeof exports?exports:e)[o]=r[o]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},r={};function o(e){var n=r[e];if(void 0!==n)return n.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,o),a.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};o.r(n),o.d(n,{loadExternalPauseInteraction:()=>s});var a=o(303);class i extends a.ExternalInteractorBase{constructor(e){super(e),this.handleClickMode=e=>{if("pause"!==e)return;const t=this.container;t.animationStatus?t.pause():t.play()}}clear(){}init(){}interact(){}isEnabled(){return!0}reset(){}}async function s(e,t=!0){(0,a.assertValidVersion)(e,"3.7.1"),await e.addInteractor("externalPause",(e=>Promise.resolve(new i(e))),t)}return n})()));