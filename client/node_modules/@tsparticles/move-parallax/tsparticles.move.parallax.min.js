/*! For license information please see tsparticles.move.parallax.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var r="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var o in r)("object"==typeof exports?exports:e)[o]=r[o]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},r={};function o(e){var n=r[e];if(void 0!==n)return n.exports;var i=r[e]={exports:{}};return t[e](i,i.exports,o),i.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};o.r(n),o.d(n,{loadParallaxMover:()=>s});var i=o(303);class a{init(){}isEnabled(e){return!(0,i.isSsr)()&&!e.destroyed&&e.container.actualOptions.interactivity.events.onHover.parallax.enable}move(e){const t=e.container,r=t.actualOptions.interactivity.events.onHover.parallax;if((0,i.isSsr)()||!r.enable)return;const o=r.force,n=t.interactivity.mouse.position;if(!n)return;const a=t.canvas.size,s=.5*a.width,l=.5*a.height,p=r.smooth,c=e.getRadius()/o,f=(n.x-s)*c,d=(n.y-l)*c,{offset:u}=e;u.x+=(f-u.x)/p,u.y+=(d-u.y)/p}}async function s(e,t=!0){(0,i.assertValidVersion)(e,"3.7.1"),await e.addMover("parallax",(()=>Promise.resolve(new a)),t)}return n})()));