/*! For license information please see tsparticles.interaction.external.connect.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],e);else{var o="object"==typeof exports?e(require("@tsparticles/engine")):e(t.window);for(var n in o)("object"==typeof exports?exports:t)[n]=o[n]}}(this,(t=>(()=>{var e={303:e=>{e.exports=t}},o={};function n(t){var i=o[t];if(void 0!==i)return i.exports;var r=o[t]={exports:{}};return e[t](r,r.exports,n),r.exports}n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i={};n.r(i),n.d(i,{Connect:()=>a,ConnectLinks:()=>s,loadExternalConnectInteraction:()=>y});var r=n(303);class s{constructor(){this.opacity=.5}load(t){(0,r.isNull)(t)||void 0!==t.opacity&&(this.opacity=t.opacity)}}class a{constructor(){this.distance=80,this.links=new s,this.radius=60}load(t){(0,r.isNull)(t)||(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links),void 0!==t.radius&&(this.radius=t.radius))}}const c=0,l=1;function d(t,e,o,n){const i=t.actualOptions.interactivity.modes.connect;if(i)return function(t,e,o,n){const i=Math.floor(o.getRadius()/e.getRadius()),s=e.getFillColor(),a=o.getFillColor();if(!s||!a)return;const d=e.getPosition(),u=o.getPosition(),p=(0,r.colorMix)(s,a,e.getRadius(),o.getRadius()),y=t.createLinearGradient(d.x,d.y,u.x,u.y);return y.addColorStop(c,(0,r.getStyleFromHsl)(s,n)),y.addColorStop((0,r.clamp)(i,c,l),(0,r.getStyleFromRgb)(p,n)),y.addColorStop(l,(0,r.getStyleFromHsl)(a,n)),y}(e,o,n,i.links.opacity)}function u(t,e,o){t.canvas.draw((n=>{const i=d(t,n,e,o);if(!i)return;const s=e.getPosition(),a=o.getPosition();!function(t,e,o,n,i){(0,r.drawLine)(t,n,i),t.lineWidth=e,t.strokeStyle=o,t.stroke()}(n,e.retina.linksWidth??0,i,s,a)}))}class p extends r.ExternalInteractorBase{constructor(t){super(t)}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.connect;e&&(t.retina.connectModeDistance=e.distance*t.retina.pixelRatio,t.retina.connectModeRadius=e.radius*t.retina.pixelRatio)}interact(){const t=this.container;if(t.actualOptions.interactivity.events.onHover.enable&&"pointermove"===t.interactivity.status){const e=t.interactivity.mouse.position,{connectModeDistance:o,connectModeRadius:n}=t.retina;if(!o||o<0||!n||n<0||!e)return;const i=Math.abs(n),r=t.particles.quadTree.queryCircle(e,i,(t=>this.isEnabled(t)));r.forEach(((e,n)=>{const i=e.getPosition();for(const s of r.slice(n+1)){const n=s.getPosition(),r=Math.abs(o),a=Math.abs(i.x-n.x),c=Math.abs(i.y-n.y);a<r&&c<r&&u(t,e,s)}}))}}isEnabled(t){const e=this.container,o=e.interactivity.mouse,n=(t?.interactivity??e.actualOptions.interactivity).events;return!(!n.onHover.enable||!o.position)&&(0,r.isInArray)("connect",n.onHover.mode)}loadModeOptions(t,...e){t.connect||(t.connect=new a);for(const o of e)t.connect.load(o?.connect)}reset(){}}async function y(t,e=!0){(0,r.assertValidVersion)(t,"3.7.1"),await t.addInteractor("externalConnect",(t=>Promise.resolve(new p(t))),e)}return i})()));