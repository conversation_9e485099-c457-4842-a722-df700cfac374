/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.7.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Connector.js":
/*!***********************************!*\
  !*** ./dist/browser/Connector.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Connector: () => (/* binding */ Connector)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Connect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Connect.js */ \"./dist/browser/Options/Classes/Connect.js\");\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n\n\n\nconst connectMode = \"connect\",\n  minDistance = 0;\nclass Connector extends _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ExternalInteractorBase {\n  constructor(container) {\n    super(container);\n  }\n  clear() {}\n  init() {\n    const container = this.container,\n      connect = container.actualOptions.interactivity.modes.connect;\n    if (!connect) {\n      return;\n    }\n    container.retina.connectModeDistance = connect.distance * container.retina.pixelRatio;\n    container.retina.connectModeRadius = connect.radius * container.retina.pixelRatio;\n  }\n  interact() {\n    const container = this.container,\n      options = container.actualOptions;\n    if (options.interactivity.events.onHover.enable && container.interactivity.status === \"pointermove\") {\n      const mousePos = container.interactivity.mouse.position,\n        {\n          connectModeDistance,\n          connectModeRadius\n        } = container.retina;\n      if (!connectModeDistance || connectModeDistance < minDistance || !connectModeRadius || connectModeRadius < minDistance || !mousePos) {\n        return;\n      }\n      const distance = Math.abs(connectModeRadius),\n        query = container.particles.quadTree.queryCircle(mousePos, distance, p => this.isEnabled(p));\n      query.forEach((p1, i) => {\n        const pos1 = p1.getPosition(),\n          indexOffset = 1;\n        for (const p2 of query.slice(i + indexOffset)) {\n          const pos2 = p2.getPosition(),\n            distMax = Math.abs(connectModeDistance),\n            xDiff = Math.abs(pos1.x - pos2.x),\n            yDiff = Math.abs(pos1.y - pos2.y);\n          if (xDiff < distMax && yDiff < distMax) {\n            (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.drawConnection)(container, p1, p2);\n          }\n        }\n      });\n    }\n  }\n  isEnabled(particle) {\n    const container = this.container,\n      mouse = container.interactivity.mouse,\n      events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n    if (!(events.onHover.enable && mouse.position)) {\n      return false;\n    }\n    return (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(connectMode, events.onHover.mode);\n  }\n  loadModeOptions(options, ...sources) {\n    if (!options.connect) {\n      options.connect = new _Options_Classes_Connect_js__WEBPACK_IMPORTED_MODULE_2__.Connect();\n    }\n    for (const source of sources) {\n      options.connect.load(source?.connect);\n    }\n  }\n  reset() {}\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-connect/./dist/browser/Connector.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Connect.js":
/*!*************************************************!*\
  !*** ./dist/browser/Options/Classes/Connect.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Connect: () => (/* binding */ Connect)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ConnectLinks_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ConnectLinks.js */ \"./dist/browser/Options/Classes/ConnectLinks.js\");\n\n\nclass Connect {\n  constructor() {\n    this.distance = 80;\n    this.links = new _ConnectLinks_js__WEBPACK_IMPORTED_MODULE_1__.ConnectLinks();\n    this.radius = 60;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = data.distance;\n    }\n    this.links.load(data.links);\n    if (data.radius !== undefined) {\n      this.radius = data.radius;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-connect/./dist/browser/Options/Classes/Connect.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/ConnectLinks.js":
/*!******************************************************!*\
  !*** ./dist/browser/Options/Classes/ConnectLinks.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectLinks: () => (/* binding */ ConnectLinks)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass ConnectLinks {\n  constructor() {\n    this.opacity = 0.5;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-connect/./dist/browser/Options/Classes/ConnectLinks.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawConnectLine: () => (/* binding */ drawConnectLine),\n/* harmony export */   drawConnection: () => (/* binding */ drawConnection),\n/* harmony export */   gradient: () => (/* binding */ gradient),\n/* harmony export */   lineStyle: () => (/* binding */ lineStyle)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst gradientMin = 0,\n  gradientMax = 1,\n  defaultLinksWidth = 0;\nfunction gradient(context, p1, p2, opacity) {\n  const gradStop = Math.floor(p2.getRadius() / p1.getRadius()),\n    color1 = p1.getFillColor(),\n    color2 = p2.getFillColor();\n  if (!color1 || !color2) {\n    return;\n  }\n  const sourcePos = p1.getPosition(),\n    destPos = p2.getPosition(),\n    midRgb = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.colorMix)(color1, color2, p1.getRadius(), p2.getRadius()),\n    grad = context.createLinearGradient(sourcePos.x, sourcePos.y, destPos.x, destPos.y);\n  grad.addColorStop(gradientMin, (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromHsl)(color1, opacity));\n  grad.addColorStop((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.clamp)(gradStop, gradientMin, gradientMax), (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromRgb)(midRgb, opacity));\n  grad.addColorStop(gradientMax, (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromHsl)(color2, opacity));\n  return grad;\n}\nfunction drawConnectLine(context, width, lineStyle, begin, end) {\n  (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.drawLine)(context, begin, end);\n  context.lineWidth = width;\n  context.strokeStyle = lineStyle;\n  context.stroke();\n}\nfunction lineStyle(container, ctx, p1, p2) {\n  const options = container.actualOptions,\n    connectOptions = options.interactivity.modes.connect;\n  if (!connectOptions) {\n    return;\n  }\n  return gradient(ctx, p1, p2, connectOptions.links.opacity);\n}\nfunction drawConnection(container, p1, p2) {\n  container.canvas.draw(ctx => {\n    const ls = lineStyle(container, ctx, p1, p2);\n    if (!ls) {\n      return;\n    }\n    const pos1 = p1.getPosition(),\n      pos2 = p2.getPosition();\n    drawConnectLine(ctx, p1.retina.linksWidth ?? defaultLinksWidth, ls, pos1, pos2);\n  });\n}\n\n//# sourceURL=webpack://@tsparticles/interaction-external-connect/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Connect: () => (/* reexport safe */ _Options_Classes_Connect_js__WEBPACK_IMPORTED_MODULE_2__.Connect),\n/* harmony export */   ConnectLinks: () => (/* reexport safe */ _Options_Classes_ConnectLinks_js__WEBPACK_IMPORTED_MODULE_3__.ConnectLinks),\n/* harmony export */   loadExternalConnectInteraction: () => (/* binding */ loadExternalConnectInteraction)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Connector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Connector.js */ \"./dist/browser/Connector.js\");\n/* harmony import */ var _Options_Classes_Connect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Connect.js */ \"./dist/browser/Options/Classes/Connect.js\");\n/* harmony import */ var _Options_Classes_ConnectLinks_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Options/Classes/ConnectLinks.js */ \"./dist/browser/Options/Classes/ConnectLinks.js\");\n\n\nasync function loadExternalConnectInteraction(engine, refresh = true) {\n  (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.assertValidVersion)(engine, \"3.7.1\");\n  await engine.addInteractor(\"externalConnect\", container => {\n    return Promise.resolve(new _Connector_js__WEBPACK_IMPORTED_MODULE_1__.Connector(container));\n  }, refresh);\n}\n\n\n\n\n\n//# sourceURL=webpack://@tsparticles/interaction-external-connect/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});