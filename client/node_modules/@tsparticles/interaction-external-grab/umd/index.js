var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "@tsparticles/engine", "./Grabber.js", "./Options/Classes/Grab.js", "./Options/Classes/GrabLinks.js", "./Options/Interfaces/IGrab.js", "./Options/Interfaces/IGrabLinks.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadExternalGrabInteraction = loadExternalGrabInteraction;
    const engine_1 = require("@tsparticles/engine");
    const Grabber_js_1 = require("./Grabber.js");
    async function loadExternalGrabInteraction(engine, refresh = true) {
        (0, engine_1.assertValidVersion)(engine, "3.7.1");
        await engine.addInteractor("externalGrab", container => {
            return Promise.resolve(new Grabber_js_1.Grabber(container, engine));
        }, refresh);
    }
    __exportStar(require("./Options/Classes/Grab.js"), exports);
    __exportStar(require("./Options/Classes/GrabLinks.js"), exports);
    __exportStar(require("./Options/Interfaces/IGrab.js"), exports);
    __exportStar(require("./Options/Interfaces/IGrabLinks.js"), exports);
});
