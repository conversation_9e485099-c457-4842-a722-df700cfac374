/*! For license information please see tsparticles.interaction.external.grab.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],e);else{var o="object"==typeof exports?e(require("@tsparticles/engine")):e(t.window);for(var i in o)("object"==typeof exports?exports:t)[i]=o[i]}}(this,(t=>(()=>{var e={303:e=>{e.exports=t}},o={};function i(t){var n=o[t];if(void 0!==n)return n.exports;var r=o[t]={exports:{}};return e[t](r,r.exports,i),r.exports}i.d=(t,e)=>{for(var o in e)i.o(e,o)&&!i.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};i.r(n),i.d(n,{Grab:()=>a,GrabLinks:()=>s,loadExternalGrabInteraction:()=>d});var r=i(303);class s{constructor(){this.blink=!1,this.consent=!1,this.opacity=1}load(t){(0,r.isNull)(t)||(void 0!==t.blink&&(this.blink=t.blink),void 0!==t.color&&(this.color=r.OptionsColor.create(this.color,t.color)),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.opacity&&(this.opacity=t.opacity))}}class a{constructor(){this.distance=100,this.links=new s}load(t){(0,r.isNull)(t)||(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links))}}function c(t,e,o,i,n){t.canvas.draw((t=>{const s=e.getPosition();!function(t,e,o,i,n,s){(0,r.drawLine)(t,o,i),t.strokeStyle=(0,r.getStyleFromRgb)(n,s),t.lineWidth=e,t.stroke()}(t,e.retina.linksWidth??0,s,n,o,i)}))}class l extends r.ExternalInteractorBase{constructor(t,e){super(t),this._engine=e}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.grab;e&&(t.retina.grabModeDistance=e.distance*t.retina.pixelRatio)}interact(){const t=this.container,e=t.actualOptions.interactivity;if(!e.modes.grab||!e.events.onHover.enable||t.interactivity.status!==r.mouseMoveEvent)return;const o=t.interactivity.mouse.position;if(!o)return;const i=t.retina.grabModeDistance;if(!i||i<0)return;const n=t.particles.quadTree.queryCircle(o,i,(t=>this.isEnabled(t)));for(const s of n){const n=s.getPosition(),a=(0,r.getDistance)(n,o);if(a>i)continue;const l=e.modes.grab.links,d=l.opacity,p=d-a*d/i;if(p<=0)continue;const b=l.color??s.options.links?.color;if(!t.particles.grabLineColor&&b){const o=e.modes.grab.links;t.particles.grabLineColor=(0,r.getLinkRandomColor)(this._engine,b,o.blink,o.consent)}const u=(0,r.getLinkColor)(s,void 0,t.particles.grabLineColor);u&&c(t,s,u,p,o)}}isEnabled(t){const e=this.container,o=e.interactivity.mouse,i=(t?.interactivity??e.actualOptions.interactivity).events;return i.onHover.enable&&!!o.position&&(0,r.isInArray)("grab",i.onHover.mode)}loadModeOptions(t,...e){t.grab||(t.grab=new a);for(const o of e)t.grab.load(o?.grab)}reset(){}}async function d(t,e=!0){(0,r.assertValidVersion)(t,"3.7.1"),await t.addInteractor("externalGrab",(e=>Promise.resolve(new l(e,t))),e)}return n})()));