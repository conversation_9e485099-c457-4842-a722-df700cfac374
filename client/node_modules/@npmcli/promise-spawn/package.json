{"name": "@npmcli/promise-spawn", "version": "8.0.2", "files": ["bin/", "lib/"], "main": "./lib/index.js", "description": "spawn processes the way the npm cli likes to do", "repository": {"type": "git", "url": "git+https://github.com/npm/promise-spawn.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.4", "spawk": "^1.7.1", "tap": "^16.0.1"}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "publish": true}, "dependencies": {"which": "^5.0.0"}}