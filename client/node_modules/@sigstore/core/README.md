# @sigstore/core &middot; [![npm version](https://img.shields.io/npm/v/@sigstore/core.svg?style=flat)](https://www.npmjs.com/package/@sigstore/core) [![CI Status](https://github.com/sigstore/sigstore-js/workflows/CI/badge.svg)](https://github.com/sigstore/sigstore-js/actions/workflows/ci.yml) [![Smoke Test Status](https://github.com/sigstore/sigstore-js/workflows/smoke-test/badge.svg)](https://github.com/sigstore/sigstore-js/actions/workflows/smoke-test.yml)

Base library for [Sigstore][1] JavaScript packages.

## Prerequisites

- Node.js version >= 18.17.0


[1]: https://www.sigstore.dev
