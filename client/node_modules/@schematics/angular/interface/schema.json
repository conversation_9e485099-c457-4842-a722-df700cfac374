{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularInterface", "title": "Angular Interface Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic interface definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the interface.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the interface?"}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interface, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "prefix": {"type": "string", "description": "A prefix to apply to generated selectors."}, "type": {"type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\".", "$default": {"$source": "argv", "index": 1}}}, "required": ["name", "project"]}