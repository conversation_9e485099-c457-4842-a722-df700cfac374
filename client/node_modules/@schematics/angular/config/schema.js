"use strict";
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN devkit-admin build (or bazel build ...).
Object.defineProperty(exports, "__esModule", { value: true });
exports.Type = void 0;
/**
 * Specifies which type of configuration file to create.
 */
var Type;
(function (Type) {
    Type["Browserslist"] = "browserslist";
    Type["Karma"] = "karma";
})(Type || (exports.Type = Type = {}));
