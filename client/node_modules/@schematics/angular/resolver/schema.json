{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularResolver", "title": "Angular resolver Options Schema", "type": "object", "additionalProperties": false, "description": "Generates a new, generic resolver definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the new resolver.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the resolver?"}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new resolver.", "default": false}, "flat": {"type": "boolean", "description": "When true (the default), creates the new files at the top level of the current project.", "default": true}, "functional": {"type": "boolean", "description": "Creates the resolver as a `ResolveFn`.", "default": true}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interface that defines the resolver, relative to the current workspace.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}}, "required": ["name", "project"]}