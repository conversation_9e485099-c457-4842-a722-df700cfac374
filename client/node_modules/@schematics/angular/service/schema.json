{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularService", "title": "Angular Service Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic service definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the service.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the service?"}, "path": {"type": "string", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the service, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "flat": {"type": "boolean", "default": true, "description": "When true (the default), creates files at the top level of the project."}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new service.", "default": false}}, "required": ["name", "project"]}