{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularInterceptor", "title": "Angular Interceptor Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic interceptor definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the interceptor.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the interceptor?"}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interceptor, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "flat": {"type": "boolean", "default": true, "description": "When true (the default), creates files at the top level of the project."}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new interceptor.", "default": false}, "functional": {"type": "boolean", "description": "Creates the interceptor as a `HttpInterceptorFn`.", "default": true}}, "required": ["name", "project"]}