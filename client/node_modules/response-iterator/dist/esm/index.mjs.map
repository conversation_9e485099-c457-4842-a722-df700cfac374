{"version": 3, "sources": ["index.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { Response as NodeResponse } from 'node-fetch';\nimport { Readable as NodeReadableStream } from 'stream';\n\n// @ts-ignore\nimport asyncIterator from './iterators/async.ts';\n// @ts-ignore\nimport nodeStreamIterator from './iterators/nodeStream.ts';\n// @ts-ignore\nimport promiseIterator from './iterators/promise.ts';\n// @ts-ignore\nimport readerIterator from './iterators/reader.ts';\n\ninterface CrossFetchResponse {\n  _bodyBlob: Blob;\n}\n\nconst hasIterator = typeof Symbol !== 'undefined' && Symbol.asyncIterator;\n\n/**\n * @param response A response. Supports fetch, node-fetch, and cross-fetch\n */\nexport default function responseIterator<T>(response: unknown): AsyncIterableIterator<T> {\n  if (response === undefined) throw new Error('Missing response for responseIterator');\n\n  // determine the body\n  let body: unknown = response;\n  if ((response as NodeResponse).body) body = (response as NodeResponse).body;\n  // node-fetch, browser fetch, undici\n  else if ((response as AxiosResponse).data) body = (response as AxiosResponse).data;\n  // axios\n  /* c8 ignore start */ else if ((response as CrossFetchResponse)._bodyBlob) body = (response as CrossFetchResponse)._bodyBlob; // cross-fetch\n  /* c8 ignore stop */\n\n  // adapt the body\n  if (hasIterator && body[Symbol.asyncIterator]) return asyncIterator<T>(body as AsyncIterableIterator<T>);\n  /* c8 ignore start */\n  if ((body as ReadableStream<T>).getReader) return readerIterator<T>((body as ReadableStream<T>).getReader());\n  if ((body as Blob).stream) return readerIterator<T>(((body as Blob).stream() as unknown as ReadableStream<T>).getReader());\n  if ((body as Blob).arrayBuffer) return promiseIterator<T>((body as Blob).arrayBuffer());\n  if ((body as NodeReadableStream).pipe) return nodeStreamIterator<T>(body as NodeReadableStream);\n  /* c8 ignore stop */\n\n  throw new Error('Unknown body type for responseIterator. Maybe you are not passing a streamable response');\n}\n"], "names": ["asyncIterator", "nodeStreamIterator", "promiseIterator", "readerIterator", "hasIterator", "Symbol", "responseIterator", "response", "undefined", "Error", "body", "data", "_bodyBlob", "<PERSON><PERSON><PERSON><PERSON>", "stream", "arrayBuffer", "pipe"], "mappings": "AAIA,aAAa;AACb,OAAOA,aAAa,MAAM,sBAAsB,CAAC;AACjD,aAAa;AACb,OAAOC,kBAAkB,MAAM,2BAA2B,CAAC;AAC3D,aAAa;AACb,OAAOC,eAAe,MAAM,wBAAwB,CAAC;AACrD,aAAa;AACb,OAAOC,cAAc,MAAM,uBAAuB,CAAC;AAMnD,MAAMC,WAAW,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACL,aAAa,AAAC;AAE1E;;GAEG,CACH,eAAe,SAASM,gBAAgB,CAAIC,QAAiB,EAA4B;IACvF,IAAIA,QAAQ,KAAKC,SAAS,EAAE,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAErF,qBAAqB;IACrB,IAAIC,IAAI,GAAYH,QAAQ,AAAC;IAC7B,IAAI,AAACA,QAAQ,CAAkBG,IAAI,EAAEA,IAAI,GAAG,AAACH,QAAQ,CAAkBG,IAAI,CAAC;SAEvE,IAAI,AAACH,QAAQ,CAAmBI,IAAI,EAAED,IAAI,GAAG,AAACH,QAAQ,CAAmBI,IAAI,CAAC;SAExD,IAAI,AAACJ,QAAQ,CAAwBK,SAAS,EAAEF,IAAI,GAAG,AAACH,QAAQ,CAAwBK,SAAS,CAAC,CAAC,cAAc;IAC5I,oBAAoB,CAEpB,iBAAiB;IACjB,IAAIR,WAAW,IAAIM,IAAI,CAACL,MAAM,CAACL,aAAa,CAAC,EAAE,OAAOA,aAAa,CAAIU,IAAI,CAA6B,CAAC;IACzG,qBAAqB,CACrB,IAAI,AAACA,IAAI,CAAuBG,SAAS,EAAE,OAAOV,cAAc,CAAI,AAACO,IAAI,CAAuBG,SAAS,EAAE,CAAC,CAAC;IAC7G,IAAI,AAACH,IAAI,CAAUI,MAAM,EAAE,OAAOX,cAAc,CAAI,AAAC,AAACO,IAAI,CAAUI,MAAM,EAAE,CAAkCD,SAAS,EAAE,CAAC,CAAC;IAC3H,IAAI,AAACH,IAAI,CAAUK,WAAW,EAAE,OAAOb,eAAe,CAAI,AAACQ,IAAI,CAAUK,WAAW,EAAE,CAAC,CAAC;IACxF,IAAI,AAACL,IAAI,CAAwBM,IAAI,EAAE,OAAOf,kBAAkB,CAAIS,IAAI,CAAuB,CAAC;IAChG,oBAAoB,CAEpB,MAAM,IAAID,KAAK,CAAC,yFAAyF,CAAC,CAAC;CAC5G,CAAA"}