# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

# [5.0.0](https://github.com/ngneat/transloco/compare/transloco-utils-4.0.0...transloco-utils-5.0.0) (2023-07-30)

# [4.0.0](https://github.com/ngneat/transloco/compare/transloco-utils-3.0.5...transloco-utils-4.0.0) (2023-06-17)

### chore

- 🤖 upgrade to angular v16 ([#661](https://github.com/ngneat/transloco/issues/661)) ([08db7e7](https://github.com/ngneat/transloco/commit/08db7e7d1f64846fa0b07123dee8ff5bff20b4f0))
- **utils:** 🤖 min node version is 16 ([4c31b86](https://github.com/ngneat/transloco/commit/4c31b865ec57786ea961a414ef07510532afcddc))

### BREAKING CHANGES

- **utils:** 🧨 min node version is 16
- 🧨 Angular 16

## [3.0.5](https://github.com/ngneat/transloco/compare/transloco-utils-3.0.4...transloco-utils-3.0.5) (2023-05-18)

## [3.0.4](https://github.com/ngneat/transloco/compare/transloco-utils-3.0.3...transloco-utils-3.0.4) (2022-07-19)

### Bug Fixes

- **utils:** 🐛 remove peer dependencies ([39b46e8](https://github.com/ngneat/transloco/commit/39b46e871ddf73ac8e23ce3e169ba9c09c8c9f97))

## [3.0.3](https://github.com/ngneat/transloco/compare/transloco-utils-3.0.2...transloco-utils-3.0.3) (2022-07-19)

### Bug Fixes

- **utils:** 🐛 add missing declaration files ([b7c4c23](https://github.com/ngneat/transloco/commit/b7c4c23ba2c9b337615166f94106b416a4ed0377))

## [3.0.2](https://github.com/ngneat/transloco/compare/transloco-utils-3.0.1...transloco-utils-3.0.2) (2021-10-28)

### Bug Fixes

- **utils:** 🐛 global config scoped libs type ([65359bb](https://github.com/ngneat/transloco/commit/65359bb503a94dea9ff24b16fe2e06a3bb15cffc))

## [3.0.1](https://github.com/ngneat/transloco/compare/transloco-utils-3.0.0...transloco-utils-3.0.1) (2021-10-28)

### Bug Fixes

- **utils:** 🐛 wrong config search ([29afbf9](https://github.com/ngneat/transloco/commit/29afbf9164607c7e941451031a8f6bf426ff3a6d))
