{"name": "@ngneat/transloco-utils", "version": "5.0.0", "description": "Transloco utils library", "main": "src/index.js", "engines": {"node": ">=16"}, "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/ngneat/transloco/issues"}, "homepage": "https://github.com/ngneat/transloco#readme", "repository": {"type": "git", "url": "https://github.com/ngneat/transloco"}, "author": "<PERSON><PERSON><PERSON> Basal", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/shaharkazaz"}, {"name": "<PERSON><PERSON>ded", "url": "https://github.com/itayod"}], "keywords": ["angular", "angular 2", "i18n", "translate", "angular translate", "angular i18n", "transloco", "comments", "build", "optimize"], "license": "MIT", "dependencies": {"cosmiconfig": "^8.1.3", "tslib": "^2.3.0"}, "types": "./src/index.d.ts"}