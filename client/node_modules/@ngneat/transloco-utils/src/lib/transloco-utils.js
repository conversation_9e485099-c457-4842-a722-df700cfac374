"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGlobalConfig = void 0;
const path = require("node:path");
const cosmiconfig_1 = require("cosmiconfig");
function getGlobalConfig(searchPath = '') {
    const explorer = (0, cosmiconfig_1.cosmiconfigSync)('transloco');
    const resolvedPath = path.resolve(process.cwd(), searchPath);
    const configSearch = explorer.search(resolvedPath);
    return configSearch ? configSearch.config : {};
}
exports.getGlobalConfig = getGlobalConfig;
