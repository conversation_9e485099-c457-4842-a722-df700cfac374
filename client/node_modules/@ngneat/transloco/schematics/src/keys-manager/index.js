"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWebpackConfig = exports.updateAngularJson = void 0;
const child_process_1 = require("child_process");
const schematics_1 = require("@angular-devkit/schematics");
const config_1 = require("@angular/cli/src/utilities/config");
const rxjs_1 = require("rxjs");
const package_1 = require("../utils/package");
const projects_1 = require("../utils/projects");
const transloco_1 = require("../utils/transloco");
const config_2 = require("../utils/config");
async function installKeysManager() {
    const packageManager = await (0, config_1.getConfiguredPackageManager)();
    console.log('Installing packages for tooling...');
    if (packageManager === 'yarn') {
        (0, child_process_1.execSync)('yarn add --dev @ngneat/transloco-keys-manager ngx-build-plus');
    }
    else {
        (0, child_process_1.execSync)('npm install --save-dev @ngneat/transloco-keys-manager ngx-build-plus');
    }
}
function updateAngularJson(host, options) {
    const angularJson = (0, projects_1.getWorkspace)(host);
    if (angularJson) {
        const project = angularJson.projects[options.project];
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore - This is a custom builder type added after installing ngx-build-plus
        project.architect.serve.builder = 'ngx-build-plus:dev-server';
    }
    (0, projects_1.setWorkspace)(host, angularJson);
}
exports.updateAngularJson = updateAngularJson;
function createWebpackConfig(host) {
    const webpackConfig = `const { TranslocoExtractKeysWebpackPlugin } = require('@ngneat/transloco-keys-manager');
 
module.exports = {
  plugins: [new TranslocoExtractKeysWebpackPlugin()]
};
`;
    host.create('webpack-dev.config.js', webpackConfig);
}
exports.createWebpackConfig = createWebpackConfig;
function addKeysDetectiveScript(host, strategy) {
    if (strategy === 'Both') {
        (0, package_1.addScriptToPackageJson)(host, 'start', 'ng serve --extra-webpack-config webpack-dev.config.js');
        (0, package_1.addScriptToPackageJson)(host, 'i18n:extract', 'transloco-keys-manager extract');
    }
    if (strategy === 'CLI') {
        (0, package_1.addScriptToPackageJson)(host, 'i18n:extract', 'transloco-keys-manager extract');
    }
    if (strategy === 'Webpack Plugin') {
        (0, package_1.addScriptToPackageJson)(host, 'start', 'ng serve --extra-webpack-config webpack-dev.config.js');
    }
    (0, package_1.addScriptToPackageJson)(host, 'i18n:find', 'transloco-keys-manager find');
}
function updateTranslocoConfig(host, options) {
    const config = (0, config_2.getConfig)() || {};
    let shouldUpdate = false;
    if (!config.rootTranslationsPath) {
        if (!options.translationPath) {
            throw new schematics_1.SchematicsException('Please provide the translation root path by using the --translation-path flag');
        }
        config.rootTranslationsPath = options.translationPath;
        shouldUpdate = true;
    }
    if (!config.langs) {
        if (!options.langs) {
            throw new schematics_1.SchematicsException('Please provide the available languages either by using --langs or through the "langs" property in transloco.config.js file');
        }
        config.langs = options.langs.split(',').map((l) => l.trim());
        shouldUpdate = true;
    }
    if (!config.keysManager) {
        config.keysManager = {};
        shouldUpdate = true;
    }
    if (shouldUpdate) {
        (0, transloco_1.updateConfig)(host, config);
    }
}
function default_1(options) {
    // @ts-ignore
    return (host) => {
        // First install dependencies via command line to get the latest versions.
        return (0, rxjs_1.from)(installKeysManager()).pipe((0, rxjs_1.map)(() => {
            updateTranslocoConfig(host, options);
            if (['Webpack Plugin', 'Both'].includes(options.strategy)) {
                createWebpackConfig(host);
                updateAngularJson(host, options);
            }
            addKeysDetectiveScript(host, options.strategy);
            return host;
        }));
    };
}
exports.default = default_1;
//# sourceMappingURL=index.js.map