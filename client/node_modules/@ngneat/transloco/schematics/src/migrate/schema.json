{"$schema": "http://json-schema.org/schema", "$id": "transloco-migrate", "title": "Run a migration script from @ngx-translate.", "type": "object", "properties": {"from": {"description": "Where is the migration from", "x-prompt": "✍ Which library did you use?", "enum": ["NgxTranslate", "Angular"], "default": "NgxTranslate", "type": "string"}, "path": {"description": "The path to the source root directory.", "x-prompt": "✍ Which folder will contain the translation files?", "type": "string", "default": "./src/app", "alias": "p"}}, "required": []}