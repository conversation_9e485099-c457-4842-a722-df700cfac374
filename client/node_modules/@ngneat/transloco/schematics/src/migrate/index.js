"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const schematics_1 = require("@angular-devkit/schematics");
const rxjs_1 = require("rxjs");
const ngx_translate_migration_1 = require("./ngx-translate-migration");
function default_1(options) {
    // @ts-ignore
    return (tree) => {
        if (!options.path) {
            throw new schematics_1.SchematicsException(`
        Please specify the root source of your project.
        (e.g. --path ./src/app)
      `);
        }
        return options.from === 'Angular'
            ? (0, schematics_1.schematic)('ng-migrate', options)
            : (0, rxjs_1.from)((0, ngx_translate_migration_1.run)(options.path)).pipe((0, rxjs_1.map)(() => tree));
    };
}
exports.default = default_1;
//# sourceMappingURL=index.js.map