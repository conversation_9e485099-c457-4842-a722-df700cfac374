{"$schema": "http://json-schema.org/schema", "$id": "transloco-join", "title": "Merge all of the project's translation files into one.", "type": "object", "properties": {"translationPath": {"type": "string", "description": "The folder that contain the root translation files.", "alias": "root"}, "outDir": {"type": "string", "description": "The output directory path.", "default": "dist-i18n", "alias": "o"}, "defaultLang": {"type": "string", "description": "The default language of the project"}, "includeDefaultLang": {"type": "boolean", "description": "Determine rather join also the default language", "default": false}, "format": {"description": "The output translation files format.", "type": "string", "default": "JSON", "enum": ["JSON", "xliff", "po"], "alias": "f"}, "project": {"type": "string", "description": "The root project name.", "$default": {"$source": "projectName"}}}, "required": []}