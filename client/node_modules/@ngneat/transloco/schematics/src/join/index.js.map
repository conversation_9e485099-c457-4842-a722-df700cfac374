{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../../libs/transloco-schematics/src/join/index.ts"], "names": [], "mappings": ";;AAAA,2DAKoC;AACpC,+CAAiD;AACjD,uCAAkD;AAElD,oCAAiD;AACjD,kDAS4B;AAU5B,SAAS,kBAAkB,CACzB,IAAU,EACV,OAAe,EACf,eAAe,EACf,IAAY,EACZ,GAAG,GAAG,EAAE;IAER,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC,IAAA,oBAAQ,EAAC,GAAG,CAAC,IAAI,CAAC,IAAA,sBAAU,EAAC,GAAG,CAAC;QAAE,OAAO,eAAe,CAAC;IAC/D,GAAG,CAAC,QAAQ;SACT,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC;SACvD,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;QACpB,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,IAAI,gCAAmB,CAC3B,QAAQ,GAAG,6EAA6E,CACzF,CAAC;SACH;QACD,eAAe,CAAC,GAAG,CAAC,GAAG,IAAA,8BAAkB,EAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IACL,IAAI,IAAA,sBAAU,EAAC,GAAG,CAAC,EAAE;QACnB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACjC,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,SAAS,GAAG,IAAA,6BAAiB,EAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YACrD,kBAAkB,CAChB,IAAI,EACJ,IAAA,gBAAS,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAChC,eAAe,EACf,IAAI,EACJ,SAAS,CACV,CAAC;QACJ,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,eAAe,CAAC,IAAU,EAAE,OAAsB;IACzD,IAAI,IAAA,qBAAU,EAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC9B,IAAA,qBAAU,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAC5B;AACH,CAAC;AAED,SAAS,WAAW,CAClB,IAAU,EACV,IAAY,EACZ,OAAgC;IAEhC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC;AAED,SAAS,cAAc,CAAC,MAA6B;IACnD,QAAQ,MAAM,EAAE;QACd,KAAK,6BAAqB,CAAC,IAAI;YAC7B,OAAO,WAAW,CAAC;QACrB,KAAK,6BAAqB,CAAC,EAAE;YAC3B,QAAQ;YACR,OAAO,WAAW,CAAC;QACrB,KAAK,6BAAqB,CAAC,KAAK;YAC9B,QAAQ;YACR,OAAO,WAAW,CAAC;QACrB;YACE,OAAO,WAAW,CAAC;KACtB;AACH,CAAC;AAED,mBAAyB,OAAsB;IAC7C,OAAO,CAAC,IAAU,EAAE,EAAE;QACpB,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/B,MAAM,IAAI,GAAG,IAAA,+BAAmB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,MAAM,WAAW,GAAG,IAAA,0BAAc,EAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,OAAO,CAAC,kBAAkB,IAAI,CAAC,WAAW,EAAE;YAC9C,MAAM,IAAI,gCAAmB,CAC3B,oGAAoG,CACrG,CAAC;SACH;QACD,IAAI,gBAAgB,GAAG,IAAA,+BAAmB,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvD,MAAM,qBAAqB,GAAG,IAAA,oCAAwB,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEnE,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;YAC/B,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;SAC3E;QAED,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1C,IAAI,EAAE,CAAC,CAAC,IAAI;YACZ,WAAW,EAAE,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE;gBAC3D,OAAO,kBAAkB,CACvB,IAAI,EACJ,SAAS,CAAC,IAAI,EACd,CAAC,CAAC,WAAW,EACb,CAAC,CAAC,IAAI,EACN,SAAS,CAAC,KAAK,CAChB,CAAC;YACJ,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC;SAClB,CAAC,CAAC,CAAC;QAEJ,MAAM,UAAU,GAAG,IAAI,sBAAS,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACnB,OAAO,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AAtCD,4BAsCC"}