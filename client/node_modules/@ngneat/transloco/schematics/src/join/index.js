"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const schematics_1 = require("@angular-devkit/schematics");
const core_1 = require("@angular-devkit/core");
const fs_extra_1 = require("fs-extra");
const types_1 = require("../types");
const transloco_1 = require("../utils/transloco");
function reduceTranslations(host, dirPath, translationJson, lang, key = '') {
    const dir = host.getDir(dirPath);
    if (!(0, transloco_1.hasFiles)(dir) && !(0, transloco_1.hasSubdirs)(dir))
        return translationJson;
    dir.subfiles
        .filter((fileName) => fileName.includes(`${lang}.json`))
        .forEach((fileName) => {
        if (translationJson[key]) {
            throw new schematics_1.SchematicsException(`key: ${key} already exist in translation file, please rename it and rerun the command.`);
        }
        translationJson[key] = (0, transloco_1.getJsonFileContent)(fileName, dir);
    });
    if ((0, transloco_1.hasSubdirs)(dir)) {
        dir.subdirs.forEach((subDirName) => {
            const subDir = dir.dir(subDirName);
            const nestedKey = (0, transloco_1.getTranslationKey)(key, subDirName);
            reduceTranslations(host, (0, core_1.normalize)(subDir.path).substr(1), translationJson, lang, nestedKey);
        });
    }
    return translationJson;
}
function deletePrevFiles(host, options) {
    if ((0, fs_extra_1.existsSync)(options.outDir)) {
        (0, fs_extra_1.removeSync)(options.outDir);
    }
}
function jsonBuilder(tree, path, content) {
    tree.create(`${path}.json`, JSON.stringify(content, null, 2));
}
function builderFactory(format) {
    switch (format) {
        case types_1.TranslationFileFormat.JSON:
            return jsonBuilder;
        case types_1.TranslationFileFormat.PO:
            // TODO:
            return jsonBuilder;
        case types_1.TranslationFileFormat.XLIFF:
            // TODO:
            return jsonBuilder;
        default:
            return jsonBuilder;
    }
}
function default_1(options) {
    return (host) => {
        deletePrevFiles(host, options);
        const root = (0, transloco_1.getTranslationsRoot)(host, options);
        const defaultLang = (0, transloco_1.getDefaultLang)(options);
        if (options.includeDefaultLang && !defaultLang) {
            throw new schematics_1.SchematicsException(`Please specify the default project's language using --default-Lang or in transloco.config.js file.`);
        }
        let rootTranslations = (0, transloco_1.getTranslationFiles)(host, root);
        const translationEntryPaths = (0, transloco_1.getTranslationEntryPaths)(host, root);
        if (!options.includeDefaultLang) {
            rootTranslations = rootTranslations.filter((t) => t.lang !== defaultLang);
        }
        const output = rootTranslations.map((t) => ({
            lang: t.lang,
            translation: translationEntryPaths.reduce((acc, entryPath) => {
                return reduceTranslations(host, entryPath.path, t.translation, t.lang, entryPath.scope);
            }, t.translation),
        }));
        const treeSource = new schematics_1.EmptyTree();
        const builder = builderFactory(options.format);
        output.forEach((o) => {
            builder(treeSource, `${options.outDir}/${o.lang}`, o.translation);
        });
        return treeSource;
    };
}
exports.default = default_1;
//# sourceMappingURL=index.js.map