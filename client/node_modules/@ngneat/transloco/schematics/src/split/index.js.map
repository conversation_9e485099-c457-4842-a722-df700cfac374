{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../../libs/transloco-schematics/src/split/index.ts"], "names": [], "mappings": ";;AAGA,oCAAiD;AACjD,kDAQ4B;AAM5B,SAAS,kBAAkB,CACzB,IAAU,EACV,OAAe,EACf,eAAe,EACf,IAAY,EACZ,GAAG,GAAG,EAAE;IAER,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC,IAAA,oBAAQ,EAAC,GAAG,CAAC;QAAE,OAAO,eAAe,CAAC;IAE3C,IAAI,IAAA,sBAAU,EAAC,GAAG,CAAC,EAAE;QACnB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACjC,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,aAAa,GAAG,IAAA,6BAAiB,EAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YACzD,MAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,kBAAkB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,kBAAkB,EAAE;gBACtB,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC3E,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAA;aACvC;QACH,CAAC,CAAC,CAAC;KACJ;IAED,GAAG,CAAC,QAAQ;SACT,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC;SACvD,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;QACpB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE;YACzB,OAAO,eAAe,CAAC;SACxB;QACD,IAAA,0BAAc,EAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEL,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,aAAa,CAAC,MAA6B;IAClD,QAAQ,MAAM,EAAE;QACd,KAAK,6BAAqB,CAAC,IAAI;YAC7B,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,KAAK,6BAAqB,CAAC,EAAE;YAC3B,QAAQ;YACR,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,KAAK,6BAAqB,CAAC,KAAK;YAC9B,QAAQ;YACR,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB;YACE,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;AACH,CAAC;AAED,mBAAyB,OAAsB;IAC7C,OAAO,CAAC,IAAU,EAAE,EAAE;QACpB,MAAM,IAAI,GAAG,IAAA,+BAAmB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChD,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAE7C,MAAM,eAAe,GAAG,IAAA,+BAAmB,EAAC,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC1E,MAAM,qBAAqB,GAAG,IAAA,oCAAwB,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEnE,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,KAAK,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,eAAe,EAAE;YACnD,cAAc,CAAC,IAAI,CAAC,GAAG,qBAAqB,CAAC,MAAM,CACjD,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE;gBACvB,OAAO,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC,EACD,WAAW,CACZ,CAAC;SACH;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC9C,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,IAAA,0BAAc,EAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAzBD,4BAyBC"}