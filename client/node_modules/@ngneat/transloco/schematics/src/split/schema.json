{"$schema": "http://json-schema.org/schema", "$id": "transloco-split", "title": "Split translated files between the project's translation files.", "type": "object", "properties": {"translationPath": {"type": "string", "description": "The folder that contain the root translation files.", "default": "src/assets/i18n", "alias": "root"}, "source": {"type": "string", "description": "The path of the source directory that contain the translated files.", "default": "dist-i18n", "alias": "o"}, "format": {"description": "The translated files format.", "type": "string", "default": "JSON", "enum": ["JSON", "xliff", "po"], "alias": "f"}, "project": {"type": "string", "description": "The root project name.", "$default": {"$source": "projectName"}}}, "required": []}