"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const types_1 = require("../types");
const transloco_1 = require("../utils/transloco");
function reduceTranslations(host, dirPath, translationJson, lang, key = '') {
    const dir = host.getDir(dirPath);
    if (!(0, transloco_1.hasFiles)(dir))
        return translationJson;
    if ((0, transloco_1.hasSubdirs)(dir)) {
        dir.subdirs.forEach((subDirName) => {
            const subDir = dir.dir(subDirName);
            const nestedKeyPath = (0, transloco_1.getTranslationKey)(key, subDirName);
            const nestedKey = nestedKeyPath.split('.').at(-1);
            const subTranslationJson = translationJson[key];
            if (subTranslationJson) {
                reduceTranslations(host, subDir.path, subTranslationJson, lang, nestedKey);
                delete translationJson[key][nestedKey];
            }
        });
    }
    dir.subfiles
        .filter((fileName) => fileName.includes(`${lang}.json`))
        .forEach((fileName) => {
        if (!translationJson[key]) {
            return translationJson;
        }
        (0, transloco_1.setFileContent)(host, dir.path, fileName, translationJson[key]);
    });
    delete translationJson[key];
    return translationJson;
}
function parserFactory(format) {
    switch (format) {
        case types_1.TranslationFileFormat.JSON:
            return JSON.parse;
        case types_1.TranslationFileFormat.PO:
            // TODO:
            return JSON.parse;
        case types_1.TranslationFileFormat.XLIFF:
            // TODO:
            return JSON.parse;
        default:
            return JSON.parse;
    }
}
function default_1(options) {
    return (host) => {
        const root = (0, transloco_1.getTranslationsRoot)(host, options);
        const parser = parserFactory(options.format);
        const translatedFiles = (0, transloco_1.getTranslationFiles)(host, options.source, parser);
        const translationEntryPaths = (0, transloco_1.getTranslationEntryPaths)(host, root);
        const newTranslation = {};
        for (const { lang, translation } of translatedFiles) {
            newTranslation[lang] = translationEntryPaths.reduce((acc, { scope, path }) => {
                return reduceTranslations(host, path, translation, lang, scope);
            }, translation);
        }
        host.getDir(root).subfiles.forEach((fileName) => {
            const lang = fileName.split('.')[0];
            (0, transloco_1.setFileContent)(host, root, fileName, newTranslation[lang]);
        });
        return host;
    };
}
exports.default = default_1;
//# sourceMappingURL=index.js.map