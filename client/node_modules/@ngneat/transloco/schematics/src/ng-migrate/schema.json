{"$schema": "http://json-schema.org/schema", "$id": "transloco-ng-migrate", "title": "Run a migration script from Angular i18n", "type": "object", "properties": {"langs": {"description": "The languages of the project.", "x-prompt": "Which languages do you use?", "type": "string", "default": "en, es"}, "translationFilesPath": {"description": "The location of the translation files.", "x-prompt": "Which folder will contain the translation files?", "type": "string", "default": "src/assets/i18n"}, "path": {"description": "The path to the source root directory.", "x-prompt": "Which folder will contain the translation files?", "type": "string", "default": "./src/app"}}, "required": []}