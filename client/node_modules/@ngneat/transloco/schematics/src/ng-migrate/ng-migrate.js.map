{"version": 3, "file": "ng-migrate.js", "sourceRoot": "", "sources": ["../../../../../../libs/transloco-schematics/src/ng-migrate/ng-migrate.ts"], "names": [], "mappings": ";;;AAAA,8CAA8C;AAC9C,+BAAwC;AACxC,uCAAwE;AAExE,MAAM,KAAK,GACT,yKAAyK,CAAC;AAE5K,SAAgB,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;IAC1C,MAAM,KAAK,GAAG,IAAA,WAAQ,EAAC,GAAG,OAAO,CAAC,GAAG,EAAE,IAAI,KAAK,YAAY,CAAC,CAAC;IAC9D,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;QAC5B,MAAM,GAAG,GAAG,IAAA,uBAAY,EAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1D,WAAW,GAAG,EAAE,GAAG,WAAW,EAAE,GAAG,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;QAEzD,MAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;QACnC,IAAA,yBAAc,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;KAClC;IAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;aACpC,IAAI,EAAE;aACN,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACnB,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QACT,IAAA,yBAAc,EAAC,GAAG,OAAO,CAAC,GAAG,EAAE,IAAI,MAAM,IAAI,IAAI,OAAO,EAAE,MAAM,EAAE;YAChE,MAAM,EAAE,CAAC;SACV,CAAC,CAAC;KACJ;IAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC7D,OAAO,CAAC,GAAG,CACT,sHAAsH,CACvH,CAAC;AACJ,CAAC;AA5BD,kBA4BC;AAED,SAAS,UAAU,CAAC,SAAS,EAAE,KAAK;IAClC,IAAI,GAAG,GAAG,KAAK,CAAC;IAChB,IAAI,CAAC,SAAS,EAAE;QACd,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;KACzB;IAED,IAAI,SAAS,EAAE;QACb,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC;QAC/C,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;KAC5C;IAED,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IACrB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,cAAc,CAAC,QAAQ;IAC9B,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClC,MAAM,WAAW,GAAG,EAAE,CAAC;IAEvB,OAAO,MAAM,EAAE;QACb,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1D,IAAI,OAAO,CAAC;QACZ,IAAI,OAAO,CAAC;QACZ,IAAI,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;QACjD,IAAI,GAAG,GAAG,QAAQ,CAAC;QAEnB,IAAI,SAAS,EAAE;YACb,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC;YAC/C,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAE3C,MAAM,uBAAuB,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACrD,kBAAkB;YAClB,IAAI,uBAAuB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxC,OAAO,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;gBACrC,OAAO,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACrD;iBAAM;gBACL,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;oBACzD,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;iBACpC;aACF;SACF;QAED,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QACrB,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAEzD,IAAI,OAAO,EAAE;YACX,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClD,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;YACrC,IAAI,OAAO,EAAE;gBACX,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,OAAO,CAAC;aAClD;SACF;aAAM;YACL,WAAW,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;YAC5B,IAAI,OAAO,EAAE;gBACX,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,OAAO,CAAC;aACzC;SACF;QAED,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,cAAc,CAAC,QAAQ;IAC9B,OAAO,QAAQ,CAAC,OAAO,CACrB,KAAK,EACL,UACE,KAAK,EACL,GAAG,EACH,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS;QAET,IAAI,OAAO,GAAG,OAAO,CAAC;QACtB,MAAM,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,IAAI,SAAS,CAAC,CAAC;QAC1D,IAAI,KAAK,GAAG,SAAS,CAAC;QACtB,MAAM,QAAQ,GAAG,OAAO,GAAG,kBAAkB,CAAC;QAE9C,IAAI,SAAS,EAAE;YACb,OAAO,GAAG,SAAS,IAAI,GAAG,SAAS,GAAG,IAAI,EAAE,CAAC;SAC9C;QAED,IAAI,QAAQ,EAAE;YACZ,OAAO,GAAG,SAAS,QAAQ,EAAE,CAAC;YAC9B,KAAK,GAAG,SAAS,CAAC;SACnB;QAED,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC,CACF,CAAC;AACJ,CAAC"}