"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const process_1 = require("process");
const ng_migrate_1 = require("./ng-migrate");
function default_1(options) {
    return () => {
        const langs = options.langs.split(',').map((l) => l.trim());
        (0, ng_migrate_1.run)({ input: options.path, output: options.translationFilesPath, langs });
        // prevent "nothing to be done".
        (0, process_1.exit)();
    };
}
exports.default = default_1;
//# sourceMappingURL=index.js.map