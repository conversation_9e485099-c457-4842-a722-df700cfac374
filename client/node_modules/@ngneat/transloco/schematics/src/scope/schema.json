{"$schema": "http://json-schema.org/schema", "$id": "transloco-scope", "title": "Add new transloco scope module, along with translation files.", "type": "object", "properties": {"name": {"type": "string", "description": "The scope name.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What scope name would you like?"}, "module": {"type": "string", "description": "The path at which module to add scope to, relative to the workspace root."}, "inlineLoader": {"description": "Should create scope with inline loader.", "type": "boolean", "alias": "il"}, "langs": {"description": "The languages of the project.", "type": "string", "alias": "la"}, "skipCreation": {"type": "boolean", "description": "Skip the translation files creation."}, "translationPath": {"description": "The to the translation files, relative to the project's root.", "type": "string"}, "lintFix": {"type": "boolean", "default": false, "description": "When true, applies lint fixes after generating the module."}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the NgModule, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "routing": {"type": "boolean", "description": "Create a routing module.", "default": false}, "routingScope": {"enum": ["Child", "Root"], "type": "string", "description": "The scope for the new routing module.", "default": "Child"}, "flat": {"type": "boolean", "description": "Create the new files at the top level of the current project root. ", "default": false}, "commonModule": {"type": "boolean", "description": "The new NgModule imports \"CommonModule\". ", "default": true, "visible": false}}, "required": []}