"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = require("node:path");
const strings_1 = require("@angular-devkit/core/src/utils/strings");
const schematics_1 = require("@angular-devkit/schematics");
const typescript_1 = require("typescript");
const ast_utils_1 = require("@schematics/angular/utility/ast-utils");
const util_1 = require("@schematics/angular/utility/standalone/util");
const schematics_consts_1 = require("../schematics.consts");
const array_1 = require("../utils/array");
const find_module_1 = require("../utils/find-module");
const projects_1 = require("../utils/projects");
const translations_1 = require("../utils/translations");
const config_1 = require("../utils/config");
function getProviderValue(options) {
    const name = (0, strings_1.dasherize)(options.name);
    if (!options.inlineLoader)
        return `'${name}'`;
    return `{ scope: '${name}', loader }`;
}
function addScopeToModule(tree, modulePath, options) {
    const module = tree.read(modulePath);
    const moduleSource = (0, typescript_1.createSourceFile)(modulePath, module.toString('utf-8'), typescript_1.ScriptTarget.Latest, true);
    const provider = `provideTranslocoScope(${getProviderValue(options)})`;
    const changes = [];
    changes.push((0, ast_utils_1.addProviderToModule)(moduleSource, modulePath, provider, schematics_consts_1.LIB_NAME)[0]);
    changes.push((0, ast_utils_1.addImportToModule)(moduleSource, modulePath, 'TranslocoModule', schematics_consts_1.LIB_NAME)[0]);
    changes.push((0, ast_utils_1.insertImport)(moduleSource, modulePath, 'provideTranslocoScope, TranslocoModule', schematics_consts_1.LIB_NAME));
    if (options.inlineLoader) {
        changes.push((0, ast_utils_1.insertImport)(moduleSource, modulePath, 'loader', './transloco.loader'));
    }
    (0, util_1.applyChangesToFile)(tree, modulePath, changes);
}
function getTranslationFilesFromAssets(host, translationsPath) {
    const langFiles = host.root.dir(translationsPath).subfiles;
    return Array.from(new Set(langFiles.map((file) => file.split('.')[0])));
}
function getTranslationFiles(options, host, translationsPath) {
    return (options.langs ||
        (0, config_1.getConfig)().langs ||
        getTranslationFilesFromAssets(host, translationsPath));
}
function addInlineLoader(tree, modulePath, name, langs) {
    const loader = `export const loader = [${(0, array_1.stringifyList)((0, array_1.coerceArray)(langs))}].reduce((acc: any, lang: string) => {
  acc[lang] = () => import(\`./i18n/\${lang}.json\`);
  return acc;
}, {});

`;
    const path = (0, node_path_1.join)((0, node_path_1.dirname)(modulePath), 'transloco.loader.ts');
    tree.create(path, loader);
}
function createTranslationFiles(options, rootPath, modulePath, host) {
    if (options.skipCreation) {
        return (0, schematics_1.empty)();
    }
    const defaultPath = options.inlineLoader
        ? (0, node_path_1.join)((0, node_path_1.dirname)(modulePath), 'i18n')
        : (0, node_path_1.join)(rootPath, 'assets', 'i18n', (0, strings_1.dasherize)(options.name));
    const translationsPath = options.translationPath
        ? (0, node_path_1.join)(rootPath, options.translationPath)
        : defaultPath;
    return (0, translations_1.createTranslateFilesFromOptions)(host, options, translationsPath);
}
function extractModuleOptions({ path, project, routing, flat, commonModule, }) {
    return { path, project, routing, flat, commonModule };
}
function default_1(options) {
    return (host, context) => {
        const project = (0, projects_1.getProject)(host, options.project);
        const rootPath = project?.sourceRoot ?? 'src';
        const assetsPath = (0, node_path_1.join)(rootPath, 'assets', 'i18n');
        options.langs = getTranslationFiles(options, host, assetsPath);
        if (options.module) {
            const projectPath = (0, projects_1.getProjectPath)(host, project, options);
            const modulePath = (0, find_module_1.findModuleFromOptions)(host, options, projectPath);
            if (options.inlineLoader) {
                addInlineLoader(host, modulePath, options.name, options.langs);
            }
            if (modulePath) {
                addScopeToModule(host, modulePath, options);
                return (0, schematics_1.mergeWith)(createTranslationFiles(options, rootPath, modulePath, host))(host, context);
            }
        }
        return (0, schematics_1.chain)([
            (0, schematics_1.externalSchematic)('@schematics/angular', 'module', extractModuleOptions(options)),
            (tree) => {
                const modulePath = tree.actions.find((action) => !!action.path.match(/\.module\.ts/) &&
                    !action.path.match(/-routing\.module\.ts/)).path;
                addScopeToModule(tree, modulePath, options);
                if (options.inlineLoader) {
                    addInlineLoader(tree, modulePath, options.name, options.langs);
                }
                const translationRule = createTranslationFiles(options, rootPath, modulePath, host);
                return (0, schematics_1.mergeWith)(translationRule);
            },
        ]);
    };
}
exports.default = default_1;
//# sourceMappingURL=index.js.map