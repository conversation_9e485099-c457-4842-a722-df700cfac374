{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../../libs/transloco-schematics/src/scope/index.ts"], "names": [], "mappings": ";;AAAA,yCAA0C;AAE1C,oEAAmE;AACnE,2DAQoC;AACpC,2CAA4D;AAC5D,qEAI+C;AAC/C,sEAAiF;AAGjF,4DAAgD;AAChD,0CAA4D;AAC5D,sDAA6D;AAC7D,gDAA+D;AAC/D,wDAAwE;AACxE,4CAA4C;AAI5C,SAAS,gBAAgB,CAAC,OAAsB;IAC9C,MAAM,IAAI,GAAG,IAAA,mBAAS,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrC,IAAI,CAAC,OAAO,CAAC,YAAY;QAAE,OAAO,IAAI,IAAI,GAAG,CAAC;IAC9C,OAAO,aAAa,IAAI,aAAa,CAAC;AACxC,CAAC;AAED,SAAS,gBAAgB,CACvB,IAAU,EACV,UAAkB,EAClB,OAAsB;IAEtB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAErC,MAAM,YAAY,GAAG,IAAA,6BAAgB,EACnC,UAAU,EACV,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EACxB,yBAAY,CAAC,MAAM,EACnB,IAAI,CACL,CAAC;IACF,MAAM,QAAQ,GAAG,yBAAyB,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC;IACvE,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,OAAO,CAAC,IAAI,CACV,IAAA,+BAAmB,EAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,4BAAQ,CAAC,CAAC,CAAC,CAAC,CACrE,CAAC;IACF,OAAO,CAAC,IAAI,CACV,IAAA,6BAAiB,EAAC,YAAY,EAAE,UAAU,EAAE,iBAAiB,EAAE,4BAAQ,CAAC,CAAC,CAAC,CAAC,CAC5E,CAAC;IACF,OAAO,CAAC,IAAI,CACV,IAAA,wBAAY,EACV,YAAY,EACZ,UAAU,EACV,wCAAwC,EACxC,4BAAQ,CACT,CACF,CAAC;IACF,IAAI,OAAO,CAAC,YAAY,EAAE;QACxB,OAAO,CAAC,IAAI,CACV,IAAA,wBAAY,EAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,oBAAoB,CAAC,CACvE,CAAC;KACH;IAED,IAAA,yBAAkB,EAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,6BAA6B,CAAC,IAAI,EAAE,gBAAgB;IAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAuB,CAAC,CAAC,QAAQ,CAAC;IAClE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,gBAAgB;IAC1D,OAAO,CACL,OAAO,CAAC,KAAK;QACb,IAAA,kBAAS,GAAE,CAAC,KAAK;QACjB,6BAA6B,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACtD,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,IAAU,EACV,UAAkB,EAClB,IAAY,EACZ,KAAwB;IAExB,MAAM,MAAM,GAAG,0BAA0B,IAAA,qBAAa,EACpD,IAAA,mBAAW,EAAC,KAAK,CAAC,CACnB;;;;;CAKF,CAAC;IACA,MAAM,IAAI,GAAG,IAAA,gBAAI,EAAC,IAAA,mBAAO,EAAC,UAAU,CAAC,EAAE,qBAAqB,CAAC,CAAC;IAC9D,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,sBAAsB,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAU;IACvE,IAAI,OAAO,CAAC,YAAY,EAAE;QACxB,OAAO,IAAA,kBAAK,GAAE,CAAC;KAChB;IACD,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY;QACtC,CAAC,CAAC,IAAA,gBAAI,EAAC,IAAA,mBAAO,EAAC,UAAU,CAAC,EAAE,MAAM,CAAC;QACnC,CAAC,CAAC,IAAA,gBAAI,EAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAA,mBAAS,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,MAAM,gBAAgB,GAAG,OAAO,CAAC,eAAe;QAC9C,CAAC,CAAC,IAAA,gBAAI,EAAC,QAAQ,EAAE,OAAO,CAAC,eAAe,CAAC;QACzC,CAAC,CAAC,WAAW,CAAC;IAEhB,OAAO,IAAA,8CAA+B,EAAC,IAAI,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;AAC1E,CAAC;AAED,SAAS,oBAAoB,CAAC,EAC5B,IAAI,EACJ,OAAO,EACP,OAAO,EACP,IAAI,EACJ,YAAY,GACE;IACd,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;AACxD,CAAC;AAED,mBAAyB,OAAsB;IAC7C,OAAO,CAAC,IAAU,EAAE,OAAyB,EAAE,EAAE;QAC/C,MAAM,OAAO,GAAG,IAAA,qBAAU,EAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAG,OAAO,EAAE,UAAU,IAAI,KAAK,CAAC;QAC9C,MAAM,UAAU,GAAG,IAAA,gBAAI,EAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QACpD,OAAO,CAAC,KAAK,GAAG,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAC/D,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,MAAM,WAAW,GAAG,IAAA,yBAAc,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC3D,MAAM,UAAU,GAAG,IAAA,mCAAqB,EAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YACrE,IAAI,OAAO,CAAC,YAAY,EAAE;gBACxB,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;aAChE;YACD,IAAI,UAAU,EAAE;gBACd,gBAAgB,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;gBAC5C,OAAO,IAAA,sBAAS,EACd,sBAAsB,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,CAC5D,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aAClB;SACF;QAED,OAAO,IAAA,kBAAK,EAAC;YACX,IAAA,8BAAiB,EACf,qBAAqB,EACrB,QAAQ,EACR,oBAAoB,CAAC,OAAO,CAAC,CAC9B;YACD,CAAC,IAAI,EAAE,EAAE;gBACP,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAClC,CAAC,MAAM,EAAE,EAAE,CACT,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;oBACnC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAC7C,CAAC,IAAI,CAAC;gBACP,gBAAgB,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;gBAC5C,IAAI,OAAO,CAAC,YAAY,EAAE;oBACxB,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;iBAChE;gBACD,MAAM,eAAe,GAAG,sBAAsB,CAC5C,OAAO,EACP,QAAQ,EACR,UAAU,EACV,IAAI,CACL,CAAC;gBAEF,OAAO,IAAA,sBAAS,EAAC,eAAe,CAAC,CAAC;YACpC,CAAC;SACF,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AA/CD,4BA+CC"}