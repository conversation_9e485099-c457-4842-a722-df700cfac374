import {
  provideTransloco,
  TranslocoModule
} from '@ngneat/transloco';
import { <% if (!importEnv) {%>isDevMode, <% } %>NgModule } from '@angular/core';
import { TranslocoHttpLoader } from './transloco-loader';
<% if (importEnv) {%>import { environment } from '<%= envPath %>';<% } %>

@NgModule({
  exports: [ TranslocoModule ],
  providers: [
      provideTransloco({
        config: {
          availableLangs: [<%=stringifyList(langs)%>],
          defaultLang: '<%=langs[0]%>',
          // Remove this option if your application doesn't support changing language in runtime.
          reRenderOnLangChange: true,
          prodMode: <%=prodMode%>,
        },
        loader: TranslocoHttpLoader
      }),
  ],
})
export class TranslocoRootModule {}
