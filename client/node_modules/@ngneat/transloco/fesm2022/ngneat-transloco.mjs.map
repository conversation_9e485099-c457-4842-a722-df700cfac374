{"version": 3, "file": "ngneat-transloco.mjs", "sources": ["../../../../libs/transloco/src/lib/transloco.loader.ts", "../../../../libs/transloco/src/lib/helpers.ts", "../../../../libs/transloco/src/lib/transloco.config.ts", "../../../../libs/transloco/src/lib/transloco.transpiler.ts", "../../../../libs/transloco/src/lib/transloco-missing-handler.ts", "../../../../libs/transloco/src/lib/transloco.interceptor.ts", "../../../../libs/transloco/src/lib/transloco-fallback-strategy.ts", "../../../../libs/transloco/src/lib/shared.ts", "../../../../libs/transloco/src/lib/resolve-loader.ts", "../../../../libs/transloco/src/lib/get-fallbacks-loaders.ts", "../../../../libs/transloco/src/lib/transloco.service.ts", "../../../../libs/transloco/src/lib/loader-component.component.ts", "../../../../libs/transloco/src/lib/template-handler.ts", "../../../../libs/transloco/src/lib/transloco-lang.ts", "../../../../libs/transloco/src/lib/transloco-loading-template.ts", "../../../../libs/transloco/src/lib/transloco-scope.ts", "../../../../libs/transloco/src/lib/lang-resolver.ts", "../../../../libs/transloco/src/lib/scope-resolver.ts", "../../../../libs/transloco/src/lib/transloco.directive.ts", "../../../../libs/transloco/src/lib/transloco.pipe.ts", "../../../../libs/transloco/src/lib/transloco.module.ts", "../../../../libs/transloco/src/lib/transloco.providers.ts", "../../../../libs/transloco/src/lib/transloco-testing.module.ts", "../../../../libs/transloco/src/lib/browser-lang.ts", "../../../../libs/transloco/src/ngneat-transloco.ts"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\nimport { Observable, of } from 'rxjs';\n\nimport { Translation } from './types';\n\nexport interface TranslocoLoader {\n  getTranslation(\n    lang: string,\n    data?: TranslocoLoaderData\n  ): Observable<Translation> | Promise<Translation>;\n}\n\nexport type TranslocoLoaderData = {\n  scope: string;\n};\n\nexport class DefaultLoader implements TranslocoLoader {\n  constructor(private translations: Map<string, Translation>) {}\n\n  getTranslation(lang: string): Observable<Translation> {\n    return of(this.translations.get(lang) || {});\n  }\n}\n\nexport const TRANSLOCO_LOADER = new InjectionToken<TranslocoLoader>(\n  'TRANSLOCO_LOADER'\n);\n", "import { flatten as _flatten, unflatten as _unflatten } from 'flat';\n\nimport { ProviderScope, Translation } from './types';\n\nexport function getValue<T>(obj: T, path: keyof T) {\n  if (!obj) {\n    return obj;\n  }\n\n  /* For cases where the key is like: 'general.something.thing' */\n  if (Object.prototype.hasOwnProperty.call(obj, path)) {\n    return obj[path];\n  }\n\n  return (path as string).split('.').reduce((p, c) => p?.[c], obj as any);\n}\n\nexport function setValue(obj: any, prop: string, val: any) {\n  obj = { ...obj };\n\n  const split = prop.split('.');\n  const lastIndex = split.length - 1;\n\n  split.reduce((acc, part, index) => {\n    if (index === lastIndex) {\n      acc[part] = val;\n    } else {\n      acc[part] = Array.isArray(acc[part])\n        ? acc[part].slice()\n        : { ...acc[part] };\n    }\n\n    return acc && acc[part];\n  }, obj);\n\n  return obj;\n}\n\nexport function size(collection: any): number {\n  if (!collection) {\n    return 0;\n  }\n\n  if (Array.isArray(collection)) {\n    return collection.length;\n  }\n\n  if (isObject(collection)) {\n    return Object.keys(collection).length;\n  }\n\n  return collection ? collection.length : 0;\n}\n\nexport function isEmpty(collection: any): boolean {\n  return size(collection) === 0;\n}\n\nexport function isFunction(val: unknown): val is CallableFunction {\n  return typeof val === 'function';\n}\n\nexport function isString(val: unknown): val is string {\n  return typeof val === 'string';\n}\n\nexport function isNumber(val: unknown): val is number {\n  return typeof val === 'number';\n}\n\nexport function isObject(item: unknown): boolean {\n  return !!item && typeof item === 'object' && !Array.isArray(item);\n}\n\nexport function coerceArray<T>(value: T | T[]): T[];\nexport function coerceArray<T>(value: T | readonly T[]): readonly T[];\nexport function coerceArray<T>(value: T | T[]): T[] {\n  return Array.isArray(value) ? value : [value];\n}\n\n/*\n * @example\n *\n * given: path-to-happiness => pathToHappiness\n * given: path_to_happiness => pathToHappiness\n * given: path-to_happiness => pathToHappiness\n *\n */\nexport function toCamelCase(str: string): string {\n  return str\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) =>\n      index == 0 ? word.toLowerCase() : word.toUpperCase()\n    )\n    .replace(/\\s+|_|-|\\//g, '');\n}\n\nexport function isBrowser() {\n  return typeof window !== 'undefined';\n}\n\nexport function isNil(value: unknown): value is null | undefined {\n  return value === null || value === undefined;\n}\n\nexport function isDefined(value: unknown) {\n  return isNil(value) === false;\n}\n\nexport function toNumber(value: number | string): number | null {\n  if (isNumber(value)) return value;\n\n  if (isString(value) && !isNaN(Number(value) - parseFloat(value))) {\n    return Number(value);\n  }\n\n  return null;\n}\n\nexport function isScopeObject(item: any): item is ProviderScope {\n  return item && typeof item.scope === 'string';\n}\n\nexport function isScopeArray(item: any): item is ProviderScope[] {\n  return Array.isArray(item) && item.every(isScopeObject);\n}\n\nexport function hasInlineLoader(item: any): item is ProviderScope {\n  return item && isObject(item.loader);\n}\n\nexport function unflatten(obj: Translation): Translation {\n  return _unflatten(obj);\n}\n\nexport function flatten(obj: Translation): Translation {\n  return _flatten(obj, { safe: true });\n}\n", "import { InjectionToken } from '@angular/core';\n\nimport { AvailableLangs } from './types';\n\nexport interface TranslocoConfig {\n  defaultLang: string;\n  reRenderOnLangChange: boolean;\n  prodMode: boolean;\n  fallbackLang?: string | string[];\n  failedRetries: number;\n  availableLangs: AvailableLangs;\n  flatten: {\n    aot: boolean;\n  };\n  missingHandler: {\n    logMissingKey: boolean;\n    useFallbackTranslation: boolean;\n    allowEmpty: boolean;\n  };\n  interpolation: [string, string];\n}\n\nexport const TRANSLOCO_CONFIG = new InjectionToken<TranslocoConfig>(\n  'TRANSLOCO_CONFIG',\n  {\n    providedIn: 'root',\n    factory: () => defaultConfig,\n  }\n);\n\nexport const defaultConfig: TranslocoConfig = {\n  defaultLang: 'en',\n  reRenderOnLangChange: false,\n  prodMode: false,\n  failedRetries: 2,\n  fallbackLang: [],\n  availableLangs: [],\n  missingHandler: {\n    logMissingKey: true,\n    useFallbackTranslation: false,\n    allowEmpty: false,\n  },\n  flatten: {\n    aot: false,\n  },\n  interpolation: ['{{', '}}'],\n};\n\ntype DeepPartial<T> = T extends Array<any>\n  ? T\n  : T extends object\n  ? { [P in keyof T]?: DeepPartial<T[P]> }\n  : T;\n\nexport type PartialTranslocoConfig = DeepPartial<TranslocoConfig>;\n\nexport function translocoConfig(\n  config: PartialTranslocoConfig = {}\n): TranslocoConfig {\n  return {\n    ...defaultConfig,\n    ...config,\n    missingHandler: {\n      ...defaultConfig.missingHandler,\n      ...config.missingHandler,\n    },\n    flatten: {\n      ...defaultConfig.flatten,\n      ...config.flatten,\n    },\n  };\n}\n", "import {\n  Inject,\n  inject,\n  Injectable,\n  InjectionToken,\n  Injector,\n  Optional,\n} from '@angular/core';\n\nimport { HashMap, Translation } from './types';\nimport { getValue, isDefined, isObject, isString, setValue } from './helpers';\nimport {\n  defaultConfig,\n  TRANSLOCO_CONFIG,\n  TranslocoConfig,\n} from './transloco.config';\n\nexport const TRANSLOCO_TRANSPILER = new InjectionToken<TranslocoTranspiler>(\n  'TRANSLOCO_TRANSPILER'\n);\n\nexport interface TranslocoTranspiler {\n  // TODO: Change parameters to object in the next major release\n  transpile(\n    value: any,\n    params: HashMap,\n    translation: Translation,\n    key: string\n  ): any;\n\n  onLangChanged?(lang: string): void;\n}\n\n@Injectable()\nexport class DefaultTranspiler implements TranslocoTranspiler {\n  protected interpolationMatcher: RegExp;\n\n  // TODO use inject in Transloco v7\n  constructor(@Optional() @Inject(TRANSLOCO_CONFIG) config?: TranslocoConfig) {\n    this.interpolationMatcher = resolveMatcher(config ?? defaultConfig);\n  }\n\n  transpile(\n    value: any,\n    params: HashMap = {},\n    translation: Translation,\n    key: string\n  ): any {\n    if (isString(value)) {\n      return value.replace(this.interpolationMatcher, (_, match) => {\n        match = match.trim();\n        if (isDefined(params[match])) {\n          return params[match];\n        }\n\n        return isDefined(translation[match])\n          ? this.transpile(translation[match], params, translation, key)\n          : '';\n      });\n    } else if (params) {\n      if (isObject(value)) {\n        value = this.handleObject(value, params, translation, key);\n      } else if (Array.isArray(value)) {\n        value = this.handleArray(value, params, translation, key);\n      }\n    }\n\n    return value;\n  }\n\n  /**\n   *\n   * @example\n   *\n   * const en = {\n   *  a: {\n   *    b: {\n   *      c: \"Hello {{ value }}\"\n   *    }\n   *  }\n   * }\n   *\n   * const params =  {\n   *  \"b.c\": { value: \"Transloco \"}\n   * }\n   *\n   * service.selectTranslate('a', params);\n   *\n   * // the first param will be the result of `en.a`.\n   * // the second param will be `params`.\n   * parser.transpile(value, params, {});\n   *\n   *\n   */\n  protected handleObject(\n    value: any,\n    params: HashMap = {},\n    translation: Translation,\n    key: string\n  ) {\n    let result = value;\n\n    Object.keys(params).forEach((p) => {\n      // get the value of \"b.c\" inside \"a\" => \"Hello {{ value }}\"\n      const v = getValue(result, p);\n      // get the params of \"b.c\" => { value: \"Transloco\" }\n      const getParams = getValue(params, p);\n\n      // transpile the value => \"Hello Transloco\"\n      const transpiled = this.transpile(v, getParams, translation, key);\n\n      // set \"b.c\" to `transpiled`\n      result = setValue(result, p, transpiled);\n    });\n\n    return result;\n  }\n\n  protected handleArray(\n    value: string[],\n    params: HashMap = {},\n    translation: Translation,\n    key: string\n  ) {\n    return value.map((v) => this.transpile(v, params, translation, key));\n  }\n}\n\nfunction resolveMatcher(config: TranslocoConfig): RegExp {\n  const [start, end] = config.interpolation;\n\n  return new RegExp(`${start}(.*?)${end}`, 'g');\n}\n\nexport interface TranslocoTranspilerFunction {\n  transpile(...args: string[]): any;\n}\n\nexport function getFunctionArgs(argsString: string): string[] {\n  const splitted = argsString ? argsString.split(',') : [];\n  const args = [];\n  for (let i = 0; i < splitted.length; i++) {\n    let value = splitted[i].trim();\n    while (value[value.length - 1] === '\\\\') {\n      i++;\n      value = value.replace('\\\\', ',') + splitted[i];\n    }\n    args.push(value);\n  }\n\n  return args;\n}\n\n@Injectable()\nexport class FunctionalTranspiler\n  extends DefaultTranspiler\n  implements TranslocoTranspiler\n{\n  protected injector = inject(Injector);\n\n  transpile(\n    value: any,\n    params: HashMap = {},\n    translation: Translation,\n    key: string\n  ): any {\n    let transpiled = value;\n    if (isString(value)) {\n      transpiled = value.replace(\n        /\\[\\[\\s*(\\w+)\\((.*?)\\)\\s*]]/g,\n        (match: string, functionName: string, args: string) => {\n          try {\n            const func: TranslocoTranspilerFunction =\n              this.injector.get(functionName);\n\n            return func.transpile(...getFunctionArgs(args));\n          } catch (e: any) {\n            let message = `There is an error in: '${value}'. \n                          Check that the you used the right syntax in your translation and that the implementation of ${functionName} is correct.`;\n            if (e.message.includes('NullInjectorError')) {\n              message = `You are using the '${functionName}' function in your translation but no provider was found!`;\n            }\n            throw new Error(message);\n          }\n        }\n      );\n    }\n\n    return super.transpile(transpiled, params, translation, key);\n  }\n}\n", "import { InjectionToken, Injectable } from '@angular/core';\n\nimport { TranslocoConfig } from './transloco.config';\nimport { HashMap } from './types';\n\nexport const TRANSLOCO_MISSING_HANDLER =\n  new InjectionToken<TranslocoMissingHandlerData>('TRANSLOCO_MISSING_HANDLER');\n\nexport interface TranslocoMissingHandlerData extends TranslocoConfig {\n  activeLang: string;\n}\n\nexport interface TranslocoMissingHandler {\n  handle(key: string, data: TranslocoMissingHandlerData, params?: HashMap): any;\n}\n\n@Injectable()\nexport class Default<PERSON>andler implements TranslocoMissingHandler {\n  handle(key: string, config: TranslocoConfig) {\n    if (config.missingHandler.logMissingKey && !config.prodMode) {\n      const msg = `Missing translation for '${key}'`;\n      console.warn(`%c ${msg}`, 'font-size: 12px; color: red');\n    }\n\n    return key;\n  }\n}\n", "import { InjectionToken, Injectable } from '@angular/core';\n\nimport { Translation } from './types';\n\nexport const TRANSLOCO_INTERCEPTOR = new InjectionToken<TranslocoInterceptor>(\n  'TRANSLOCO_INTERCEPTOR'\n);\n\nexport interface TranslocoInterceptor {\n  preSaveTranslation(translation: Translation, lang: string): Translation;\n\n  preSaveTranslationKey(key: string, value: string, lang: string): string;\n}\n\n@Injectable()\nexport class DefaultInterceptor implements TranslocoInterceptor {\n  preSaveTranslation(translation: Translation): Translation {\n    return translation;\n  }\n\n  preSaveTranslationKey(_: string, value: string): string {\n    return value;\n  }\n}\n", "import { Inject, Injectable, InjectionToken } from '@angular/core';\n\nimport { TRANSLOCO_CONFIG, TranslocoConfig } from './transloco.config';\n\nexport const TRANSLOCO_FALLBACK_STRATEGY =\n  new InjectionToken<TranslocoFallbackStrategy>('TRANSLOCO_FALLBACK_STRATEGY');\n\nexport interface TranslocoFallbackStrategy {\n  getNextLangs(failedLang: string): string[];\n}\n\n@Injectable()\nexport class DefaultFallbackStrategy implements TranslocoFallbackStrategy {\n  constructor(@Inject(TRANSLOCO_CONFIG) private userConfig: TranslocoConfig) {}\n\n  getNextLangs() {\n    const fallbackLang = this.userConfig.fallbackLang;\n    if (!fallbackLang) {\n      throw new Error(\n        'When using the default fallback, a fallback language must be provided in the config!'\n      );\n    }\n\n    return Array.isArray(fallbackLang) ? fallbackLang : [fallbackLang];\n  }\n}\n", "import { Observable, OperatorFunction, take } from 'rxjs';\n\nimport { TranslocoService } from './transloco.service';\nimport { hasInlineLoader, isString } from './helpers';\nimport { InlineLoader, LoadedEvent, TranslocoScope } from './types';\n\n/*\n * @example\n *\n * given: lazy-page/en => lazy-page\n *\n */\nexport function getScopeFromLang(lang: string): string {\n  if (!lang) {\n    return '';\n  }\n\n  const split = lang.split('/');\n  split.pop();\n\n  return split.join('/');\n}\n\n/*\n * @example\n *\n * given: lazy-page/en => en\n *\n */\nexport function getLangFromScope(lang: string): string {\n  if (!lang) {\n    return '';\n  }\n\n  return lang.split('/').pop()!;\n}\n\n/**\n * @example\n *\n * getPipeValue('todos|scoped', 'scoped') [true, 'todos']\n * getPipeValue('en|static', 'static') [true, 'en']\n * getPipeValue('en', 'static') [false, 'en']\n */\nexport function getPipeValue(\n  str: string | undefined,\n  value: string,\n  char = '|'\n): [boolean, string] {\n  if (isString(str)) {\n    const splitted = str.split(char);\n    const lastItem = splitted.pop()!;\n\n    return lastItem === value ? [true, splitted.toString()] : [false, lastItem];\n  }\n\n  return [false, ''];\n}\n\nexport function shouldListenToLangChanges(\n  service: TranslocoService,\n  lang?: string\n) {\n  const [hasStatic] = getPipeValue(lang, 'static');\n  if (!hasStatic) {\n    // If we didn't get 'lang|static' check if it's set in the global level\n    return !!service.config.reRenderOnLangChange;\n  }\n\n  // We have 'lang|static' so don't listen to lang changes\n  return false;\n}\n\nexport function listenOrNotOperator<T>(\n  listenToLangChange?: boolean\n): OperatorFunction<T, T> {\n  return listenToLangChange ? (source: Observable<T>) => source : take<T>(1);\n}\n\nfunction prependScope(inlineLoader: InlineLoader, scope: string) {\n  return Object.keys(inlineLoader).reduce((acc, lang) => {\n    acc[`${scope}/${lang}`] = inlineLoader[lang];\n\n    return acc;\n  }, {} as Record<string, InlineLoader[keyof InlineLoader]>);\n}\n\nexport function resolveInlineLoader(\n  providerScope: TranslocoScope | null,\n  scope?: string\n): InlineLoader | undefined {\n  return hasInlineLoader(providerScope)\n    ? prependScope(providerScope.loader!, scope!)\n    : undefined;\n}\n\nexport function getEventPayload(lang: string): LoadedEvent['payload'] {\n  return {\n    scope: getScopeFromLang(lang) || null,\n    langName: getLangFromScope(lang),\n  };\n}\n", "import { TranslocoLoader, TranslocoLoaderData } from './transloco.loader';\nimport { InlineLoader } from './types';\nimport { isFunction } from './helpers';\n\ninterface Options {\n  inlineLoader?: InlineLoader;\n  path: string;\n  mainLoader: TranslocoLoader;\n  data?: TranslocoLoaderData;\n}\n\nexport function resolveLoader(options: Options) {\n  const { path, inlineLoader, mainLoader, data } = options;\n\n  if (inlineLoader) {\n    const pathLoader = inlineLoader[path];\n    if (isFunction(pathLoader) === false) {\n      throw `You're using an inline loader but didn't provide a loader for ${path}`;\n    }\n\n    return inlineLoader[path]().then((res) =>\n      res.default ? res.default : res\n    );\n  }\n\n  return mainLoader.getTranslation(path, data);\n}\n", "import { from, map } from 'rxjs';\n\nimport { resolveLoader } from './resolve-loader';\nimport { TranslocoLoader, TranslocoLoaderData } from './transloco.loader';\nimport { InlineLoader } from './types';\n\ninterface Options {\n  path: string;\n  fallbackPath?: string;\n  inlineLoader?: InlineLoader;\n  mainLoader: TranslocoLoader;\n  data?: TranslocoLoaderData;\n}\n\nexport function getFallbacksLoaders({\n  mainLoader,\n  path,\n  data,\n  fallbackPath,\n  inlineLoader,\n}: Options) {\n  const paths = fallbackPath ? [path, fallbackPath] : [path];\n\n  return paths.map((path) => {\n    const loader = resolveLoader({ path, mainLoader, inlineLoader, data });\n\n    return from(loader).pipe(\n      map((translation) => ({\n        translation,\n        lang: path,\n      }))\n    );\n  });\n}\n", "import { Inject, Injectable, OnDestroy, Optional } from '@angular/core';\nimport {\n  BehaviorSubject,\n  catchError,\n  combineLatest,\n  EMPTY,\n  forkJoin,\n  from,\n  map,\n  Observable,\n  of,\n  retry,\n  shareReplay,\n  Subject,\n  Subscription,\n  switchMap,\n  tap,\n} from 'rxjs';\n\nimport {\n  DefaultLoader,\n  TRANSLOCO_LOADER,\n  TranslocoLoader,\n} from './transloco.loader';\nimport {\n  TRANSLOCO_TRANSPILER,\n  TranslocoTranspiler,\n} from './transloco.transpiler';\nimport {\n  AvailableLangs,\n  HashMap,\n  InlineLoader,\n  LangDefinition,\n  LoadOptions,\n  SetTranslationOptions,\n  TranslateObjectParams,\n  TranslateParams,\n  Translation,\n  TranslocoEvents,\n  TranslocoScope,\n} from './types';\nimport {\n  flatten,\n  isEmpty,\n  isNil,\n  isScopeArray,\n  isScopeObject,\n  isString,\n  size,\n  toCamelCase,\n  unflatten,\n} from './helpers';\nimport { TRANSLOCO_CONFIG, TranslocoConfig } from './transloco.config';\nimport {\n  TRANSLOCO_MISSING_HANDLER,\n  TranslocoMissingHandler,\n  TranslocoMissingHandlerData,\n} from './transloco-missing-handler';\nimport {\n  TRANSLOCO_INTERCEPTOR,\n  TranslocoInterceptor,\n} from './transloco.interceptor';\nimport {\n  TRANSLOCO_FALLBACK_STRATEGY,\n  TranslocoFallbackStrategy,\n} from './transloco-fallback-strategy';\nimport {\n  getEventPayload,\n  getLangFromScope,\n  getScopeFromLang,\n  resolveInlineLoader,\n} from './shared';\nimport { getFallbacksLoaders } from './get-fallbacks-loaders';\nimport { resolveLoader } from './resolve-loader';\n\nlet service: TranslocoService;\n\nexport function translate<T = string>(\n  key: TranslateParams,\n  params: HashMap = {},\n  lang?: string\n): T {\n  return service.translate<T>(key, params, lang);\n}\n\nexport function translateObject<T>(\n  key: TranslateParams,\n  params: HashMap = {},\n  lang?: string\n): T | T[] {\n  return service.translateObject<T>(key, params, lang);\n}\n\n@Injectable({ providedIn: 'root' })\nexport class TranslocoService implements OnDestroy {\n  langChanges$: Observable<string>;\n\n  private subscription: Subscription | null = null;\n  private translations = new Map<string, Translation>();\n  private cache = new Map<string, Observable<Translation>>();\n  private firstFallbackLang: string | undefined;\n  private defaultLang = '';\n  private availableLangs: AvailableLangs = [];\n  private isResolvedMissingOnce = false;\n  private lang: BehaviorSubject<string>;\n  private failedLangs = new Set<string>();\n  private events = new Subject<TranslocoEvents>();\n\n  events$ = this.events.asObservable();\n  readonly config: TranslocoConfig & {\n    scopeMapping?: HashMap<string>;\n  };\n\n  constructor(\n    @Optional() @Inject(TRANSLOCO_LOADER) private loader: TranslocoLoader,\n    @Inject(TRANSLOCO_TRANSPILER) private parser: TranslocoTranspiler,\n    @Inject(TRANSLOCO_MISSING_HANDLER)\n    private missingHandler: TranslocoMissingHandler,\n    @Inject(TRANSLOCO_INTERCEPTOR) private interceptor: TranslocoInterceptor,\n    @Inject(TRANSLOCO_CONFIG) userConfig: TranslocoConfig,\n    @Inject(TRANSLOCO_FALLBACK_STRATEGY)\n    private fallbackStrategy: TranslocoFallbackStrategy\n  ) {\n    if (!this.loader) {\n      this.loader = new DefaultLoader(this.translations);\n    }\n    service = this;\n    this.config = JSON.parse(JSON.stringify(userConfig));\n\n    this.setAvailableLangs(this.config.availableLangs || []);\n    this.setFallbackLangForMissingTranslation(this.config);\n    this.setDefaultLang(this.config.defaultLang);\n    this.lang = new BehaviorSubject<string>(this.getDefaultLang());\n    // Don't use distinctUntilChanged as we need the ability to update\n    // the value when using setTranslation or setTranslationKeys\n    this.langChanges$ = this.lang.asObservable();\n\n    /**\n     * When we have a failure, we want to define the next language that succeeded as the active\n     */\n    this.subscription = this.events$.subscribe((e) => {\n      if (e.type === 'translationLoadSuccess' && e.wasFailure) {\n        this.setActiveLang(e.payload.langName);\n      }\n    });\n  }\n\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n\n  setDefaultLang(lang: string) {\n    this.defaultLang = lang;\n  }\n\n  getActiveLang() {\n    return this.lang.getValue();\n  }\n\n  setActiveLang(lang: string) {\n    this.parser.onLangChanged?.(lang);\n    this.lang.next(lang);\n    this.events.next({\n      type: 'langChanged',\n      payload: getEventPayload(lang),\n    });\n    return this;\n  }\n\n  setAvailableLangs(langs: AvailableLangs) {\n    this.availableLangs = langs;\n  }\n\n  /**\n   * Gets the available languages.\n   *\n   * @returns\n   * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`\n   * depending on how the available languages are set in your module.\n   */\n  getAvailableLangs() {\n    return this.availableLangs;\n  }\n\n  load(path: string, options: LoadOptions = {}): Observable<Translation> {\n    const cached = this.cache.get(path);\n    if (cached) {\n      return cached;\n    }\n\n    let loadTranslation: Observable<\n      Translation | { translation: Translation; lang: string }[]\n    >;\n    const isScope = this._isLangScoped(path);\n    let scope: string;\n    if (isScope) {\n      scope = getScopeFromLang(path);\n    }\n\n    const loadersOptions = {\n      path,\n      mainLoader: this.loader,\n      inlineLoader: options.inlineLoader,\n      data: isScope ? { scope: scope! } : undefined,\n    };\n\n    if (this.useFallbackTranslation(path)) {\n      // if the path is scope the fallback should be `scope/fallbackLang`;\n      const fallback = isScope\n        ? `${scope!}/${this.firstFallbackLang}`\n        : this.firstFallbackLang;\n\n      const loaders = getFallbacksLoaders({\n        ...loadersOptions,\n        fallbackPath: fallback!,\n      });\n      loadTranslation = forkJoin(loaders);\n    } else {\n      const loader = resolveLoader(loadersOptions);\n      loadTranslation = from(loader);\n    }\n\n    const load$ = loadTranslation.pipe(\n      retry(this.config.failedRetries),\n      tap((translation) => {\n        if (Array.isArray(translation)) {\n          translation.forEach((t) => {\n            this.handleSuccess(t.lang, t.translation);\n            // Save the fallback in cache so we'll not create a redundant request\n            if (t.lang !== path) {\n              this.cache.set(t.lang, of({}));\n            }\n          });\n          return;\n        }\n        this.handleSuccess(path, translation);\n      }),\n      catchError((error) => {\n        if (!this.config.prodMode) {\n          console.error(`Error while trying to load \"${path}\"`, error);\n        }\n\n        return this.handleFailure(path, options);\n      }),\n      shareReplay(1)\n    );\n\n    this.cache.set(path, load$);\n\n    return load$;\n  }\n\n  /**\n   * Gets the instant translated value of a key\n   *\n   * @example\n   *\n   * translate<string>('hello')\n   * translate('hello', { value: 'value' })\n   * translate<string[]>(['hello', 'key'])\n   * translate('hello', { }, 'en')\n   * translate('scope.someKey', { }, 'en')\n   */\n  translate<T = string>(\n    key: TranslateParams,\n    params: HashMap = {},\n    lang = this.getActiveLang()\n  ): T {\n    if (!key) return key as any;\n\n    const { scope, resolveLang } = this.resolveLangAndScope(lang);\n\n    if (Array.isArray(key)) {\n      return key.map((k) =>\n        this.translate(scope ? `${scope}.${k}` : k, params, resolveLang)\n      ) as any;\n    }\n\n    key = scope ? `${scope}.${key}` : key;\n\n    const translation = this.getTranslation(resolveLang);\n    const value = translation[key];\n\n    if (!value) {\n      return this._handleMissingKey(key, value, params);\n    }\n\n    return this.parser.transpile(value, params, translation, key);\n  }\n\n  /**\n   * Gets the translated value of a key as observable\n   *\n   * @example\n   *\n   * selectTranslate<string>('hello').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)\n   *\n   */\n  selectTranslate<T = any>(\n    key: TranslateParams,\n    params?: HashMap,\n    lang?: string | TranslocoScope | TranslocoScope[],\n    _isObject = false\n  ): Observable<T> {\n    let inlineLoader: InlineLoader | undefined;\n    const load = (lang: string, options?: LoadOptions) =>\n      this.load(lang, options).pipe(\n        map(() =>\n          _isObject\n            ? this.translateObject(key, params, lang)\n            : this.translate(key, params, lang)\n        )\n      );\n    if (isNil(lang)) {\n      return this.langChanges$.pipe(switchMap((lang) => load(lang)));\n    }\n\n    if (isScopeArray(lang) || isScopeObject(lang)) {\n      // it's a scope object.\n      const providerScope = Array.isArray(lang) ? lang[0] : lang;\n      lang = providerScope.scope;\n      inlineLoader = resolveInlineLoader(providerScope, providerScope.scope);\n    }\n\n    lang = lang as string;\n    if (this.isLang(lang) || this.isScopeWithLang(lang)) {\n      return load(lang);\n    }\n    // it's a scope\n    const scope = lang;\n    return this.langChanges$.pipe(\n      switchMap((lang) => load(`${scope}/${lang}`, { inlineLoader }))\n    );\n  }\n\n  /**\n   * Whether the scope with lang\n   *\n   * @example\n   *\n   * todos/en => true\n   * todos => false\n   */\n  private isScopeWithLang(lang: string) {\n    return this.isLang(getLangFromScope(lang));\n  }\n\n  /**\n   * Translate the given path that returns an object\n   *\n   * @example\n   *\n   * service.translateObject('path.to.object', {'subpath': { value: 'someValue'}}) => returns translated object\n   *\n   */\n  translateObject<T = any>(key: string, params?: HashMap, lang?: string): T;\n  translateObject<T = any>(key: string[], params?: HashMap, lang?: string): T[];\n  translateObject<T = any>(\n    key: TranslateParams,\n    params?: HashMap,\n    lang?: string\n  ): T | T[];\n  translateObject<T = any>(\n    key: HashMap | Map<string, HashMap>,\n    params?: null,\n    lang?: string\n  ): T[];\n  translateObject<T = any>(\n    key: TranslateObjectParams,\n    params: HashMap | null = {},\n    lang = this.getActiveLang()\n  ): T | T[] {\n    if (isString(key) || Array.isArray(key)) {\n      const { resolveLang, scope } = this.resolveLangAndScope(lang);\n      if (Array.isArray(key)) {\n        return key.map((k) =>\n          this.translateObject(\n            scope ? `${scope}.${k}` : k,\n            params!,\n            resolveLang\n          )\n        ) as any;\n      }\n\n      const translation = this.getTranslation(resolveLang);\n      key = scope ? `${scope}.${key}` : key;\n\n      const value = unflatten(this.getObjectByKey(translation, key));\n      /* If an empty object was returned we want to try and translate the key as a string and not an object */\n      return isEmpty(value)\n        ? this.translate(key, params!, lang)\n        : this.parser.transpile(value, params!, translation, key);\n    }\n\n    const translations: T[] = [];\n    for (const [_key, _params] of this.getEntries(key)) {\n      translations.push(this.translateObject(_key, _params, lang));\n    }\n\n    return translations;\n  }\n\n  selectTranslateObject<T = any>(\n    key: string,\n    params?: HashMap,\n    lang?: string\n  ): Observable<T>;\n  selectTranslateObject<T = any>(\n    key: string[],\n    params?: HashMap,\n    lang?: string\n  ): Observable<T[]>;\n  selectTranslateObject<T = any>(\n    key: TranslateParams,\n    params?: HashMap,\n    lang?: string\n  ): Observable<T> | Observable<T[]>;\n  selectTranslateObject<T = any>(\n    key: HashMap | Map<string, HashMap>,\n    params?: null,\n    lang?: string\n  ): Observable<T[]>;\n  selectTranslateObject<T = any>(\n    key: TranslateObjectParams,\n    params?: HashMap | null,\n    lang?: string\n  ): Observable<T> | Observable<T[]> {\n    if (isString(key) || Array.isArray(key)) {\n      return this.selectTranslate<T>(key, params!, lang, true);\n    }\n\n    const [[firstKey, firstParams], ...rest] = this.getEntries(key);\n\n    /* In order to avoid subscribing multiple times to the load language event by calling selectTranslateObject for each pair,\n     * we listen to when the first key has been translated (the language is loaded) and translate the rest synchronously */\n    return this.selectTranslateObject<T>(firstKey, firstParams, lang).pipe(\n      map((value) => {\n        const translations = [value];\n        for (const [_key, _params] of rest) {\n          translations.push(this.translateObject<T>(_key, _params, lang));\n        }\n\n        return translations;\n      })\n    );\n  }\n\n  /**\n   * Gets an object of translations for a given language\n   *\n   * @example\n   *\n   * getTranslation()\n   * getTranslation('en')\n   * getTranslation('admin-page/en')\n   */\n  getTranslation(): Map<string, Translation>;\n  getTranslation(langOrScope: string): Translation;\n  getTranslation(langOrScope?: string): Map<string, Translation> | Translation {\n    if (langOrScope) {\n      if (this.isLang(langOrScope)) {\n        return this.translations.get(langOrScope) || {};\n      } else {\n        // This is a scope, build the scope value from the translation object\n        const { scope, resolveLang } = this.resolveLangAndScope(langOrScope);\n        const translation = this.translations.get(resolveLang) || {};\n\n        return this.getObjectByKey(translation, scope);\n      }\n    }\n\n    return this.translations;\n  }\n\n  /**\n   * Gets an object of translations for a given language\n   *\n   * @example\n   *\n   * selectTranslation().subscribe() - will return the current lang translation\n   * selectTranslation('es').subscribe()\n   * selectTranslation('admin-page').subscribe() - will return the current lang scope translation\n   * selectTranslation('admin-page/es').subscribe()\n   */\n  selectTranslation(lang?: string): Observable<Translation> {\n    let language$ = this.langChanges$;\n    if (lang) {\n      const scopeLangSpecified = getLangFromScope(lang) !== lang;\n      if (this.isLang(lang) || scopeLangSpecified) {\n        language$ = of(lang);\n      } else {\n        language$ = this.langChanges$.pipe(\n          map((currentLang) => `${lang}/${currentLang}`)\n        );\n      }\n    }\n\n    return language$.pipe(\n      switchMap((language) =>\n        this.load(language).pipe(map(() => this.getTranslation(language)))\n      )\n    );\n  }\n\n  /**\n   * Sets or merge a given translation object to current lang\n   *\n   * @example\n   *\n   * setTranslation({ ... })\n   * setTranslation({ ... }, 'en')\n   * setTranslation({ ... }, 'es', { merge: false } )\n   * setTranslation({ ... }, 'todos/en', { merge: false } )\n   */\n  setTranslation(\n    translation: Translation,\n    lang = this.getActiveLang(),\n    options: SetTranslationOptions = {}\n  ) {\n    const defaults = { merge: true, emitChange: true };\n    const mergedOptions = { ...defaults, ...options };\n    const scope = getScopeFromLang(lang);\n\n    /**\n     * If this isn't a scope we use the whole translation as is\n     * otherwise we need to flat the scope and use it\n     */\n    let flattenScopeOrTranslation = translation;\n\n    // Merged the scoped language into the active language\n    if (scope) {\n      const key = this.getMappedScope(scope);\n      flattenScopeOrTranslation = flatten({ [key]: translation });\n    }\n\n    const currentLang = scope ? getLangFromScope(lang) : lang;\n\n    const mergedTranslation = {\n      ...(mergedOptions.merge && this.getTranslation(currentLang)),\n      ...flattenScopeOrTranslation,\n    };\n\n    const flattenTranslation = this.config.flatten!.aot\n      ? mergedTranslation\n      : flatten(mergedTranslation);\n    const withHook = this.interceptor.preSaveTranslation(\n      flattenTranslation,\n      currentLang\n    );\n    this.translations.set(currentLang, withHook);\n    mergedOptions.emitChange && this.setActiveLang(this.getActiveLang());\n  }\n\n  /**\n   * Sets translation key with given value\n   *\n   * @example\n   *\n   * setTranslationKey('key', 'value')\n   * setTranslationKey('key.nested', 'value')\n   * setTranslationKey('key.nested', 'value', 'en')\n   * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )\n   */\n  setTranslationKey(\n    key: string,\n    value: string,\n    // Todo: Add the lang to the options in v3\n    lang = this.getActiveLang(),\n    options: Omit<SetTranslationOptions, 'merge'> = {}\n  ) {\n    const withHook = this.interceptor.preSaveTranslationKey(key, value, lang);\n    const newValue = {\n      [key]: withHook,\n    };\n\n    this.setTranslation(newValue, lang, { ...options, merge: true });\n  }\n\n  /**\n   * Sets the fallback lang for the currently active language\n   * @param fallbackLang\n   */\n  setFallbackLangForMissingTranslation({\n    fallbackLang,\n  }: Pick<TranslocoConfig, 'fallbackLang'>) {\n    const lang = Array.isArray(fallbackLang) ? fallbackLang[0] : fallbackLang;\n    if (fallbackLang && this.useFallbackTranslation(lang)) {\n      this.firstFallbackLang = lang!;\n    }\n  }\n\n  /**\n   * @internal\n   */\n  _handleMissingKey(key: string, value: any, params?: HashMap) {\n    if (this.config.missingHandler!.allowEmpty && value === '') {\n      return '';\n    }\n\n    if (!this.isResolvedMissingOnce && this.useFallbackTranslation()) {\n      // We need to set it to true to prevent a loop\n      this.isResolvedMissingOnce = true;\n      const fallbackValue = this.translate(\n        key,\n        params,\n        this.firstFallbackLang!\n      );\n      this.isResolvedMissingOnce = false;\n\n      return fallbackValue;\n    }\n\n    return this.missingHandler.handle(\n      key,\n      this.getMissingHandlerData(),\n      params\n    );\n  }\n\n  /**\n   * @internal\n   */\n  _isLangScoped(lang: string) {\n    return this.getAvailableLangsIds().indexOf(lang) === -1;\n  }\n\n  /**\n   * Checks if a given string is one of the specified available languages.\n   * @returns\n   * True if the given string is an available language.\n   * False if the given string is not an available language.\n   */\n  isLang(lang: string): boolean {\n    return this.getAvailableLangsIds().indexOf(lang) !== -1;\n  }\n\n  /**\n   * @internal\n   *\n   * We always want to make sure the global lang is loaded\n   * before loading the scope since you can access both via the pipe/directive.\n   */\n  _loadDependencies(\n    path: string,\n    inlineLoader?: InlineLoader\n  ): Observable<Translation | Translation[]> {\n    const mainLang = getLangFromScope(path);\n\n    if (this._isLangScoped(path) && !this.isLoadedTranslation(mainLang)) {\n      return combineLatest([\n        this.load(mainLang),\n        this.load(path, { inlineLoader }),\n      ]);\n    }\n    return this.load(path, { inlineLoader });\n  }\n\n  /**\n   * @internal\n   */\n  _completeScopeWithLang(langOrScope: string) {\n    if (\n      this._isLangScoped(langOrScope) &&\n      !this.isLang(getLangFromScope(langOrScope))\n    ) {\n      return `${langOrScope}/${this.getActiveLang()}`;\n    }\n    return langOrScope;\n  }\n\n  /**\n   * @internal\n   */\n  _setScopeAlias(scope: string, alias: string) {\n    if (!this.config.scopeMapping) {\n      this.config.scopeMapping = {};\n    }\n    this.config.scopeMapping[scope] = alias;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n      // Caretaker note: it's important to clean up references to subscriptions since they save the `next`\n      // callback within its `destination` property, preventing classes from being GC'd.\n      this.subscription = null;\n    }\n    // Caretaker note: since this is the root provider, it'll be destroyed when the `NgModuleRef.destroy()` is run.\n    // Cached values capture `this`, thus leading to a circular reference and preventing the `TranslocoService` from\n    // being GC'd. This would lead to a memory leak when server-side rendering is used since the service is created\n    // and destroyed per each HTTP request, but any service is not getting GC'd.\n    this.cache.clear();\n  }\n\n  private isLoadedTranslation(lang: string) {\n    return size(this.getTranslation(lang));\n  }\n\n  private getAvailableLangsIds(): string[] {\n    const first = this.getAvailableLangs()[0];\n\n    if (isString(first)) {\n      return this.getAvailableLangs() as string[];\n    }\n\n    return (this.getAvailableLangs() as LangDefinition[]).map((l) => l.id);\n  }\n\n  private getMissingHandlerData(): TranslocoMissingHandlerData {\n    return {\n      ...this.config,\n      activeLang: this.getActiveLang(),\n      availableLangs: this.availableLangs,\n      defaultLang: this.defaultLang,\n    };\n  }\n\n  /**\n   * Use a fallback translation set for missing keys of the primary language\n   * This is unrelated to the fallback language (which changes the active language)\n   */\n  private useFallbackTranslation(lang?: string) {\n    return (\n      this.config.missingHandler!.useFallbackTranslation &&\n      lang !== this.firstFallbackLang\n    );\n  }\n\n  private handleSuccess(lang: string, translation: Translation) {\n    this.setTranslation(translation, lang, { emitChange: false });\n    this.events.next({\n      wasFailure: !!this.failedLangs.size,\n      type: 'translationLoadSuccess',\n      payload: getEventPayload(lang),\n    });\n    this.failedLangs.forEach((l) => this.cache.delete(l));\n    this.failedLangs.clear();\n  }\n\n  private handleFailure(lang: string, loadOptions: LoadOptions) {\n    // When starting to load a first choice language, initialize\n    // the failed counter and resolve the fallback langs.\n    if (isNil(loadOptions.failedCounter)) {\n      loadOptions.failedCounter = 0;\n\n      if (!loadOptions.fallbackLangs) {\n        loadOptions.fallbackLangs = this.fallbackStrategy.getNextLangs(lang);\n      }\n    }\n\n    const splitted = lang.split('/');\n    const fallbacks = loadOptions.fallbackLangs;\n    const nextLang = fallbacks![loadOptions.failedCounter!];\n    this.failedLangs.add(lang);\n\n    // This handles the case where a loaded fallback language is requested again\n    if (this.cache.has(nextLang)) {\n      this.handleSuccess(nextLang, this.getTranslation(nextLang));\n      return EMPTY;\n    }\n\n    const isFallbackLang = nextLang === splitted[splitted.length - 1];\n\n    if (!nextLang || isFallbackLang) {\n      let msg = `Unable to load translation and all the fallback languages`;\n      if (splitted.length > 1) {\n        msg += `, did you misspelled the scope name?`;\n      }\n\n      throw new Error(msg);\n    }\n\n    let resolveLang = nextLang;\n    // if it's scoped lang\n    if (splitted.length > 1) {\n      // We need to resolve it to:\n      // todos/langNotExists => todos/nextLang\n      splitted[splitted.length - 1] = nextLang;\n      resolveLang = splitted.join('/');\n    }\n\n    loadOptions.failedCounter!++;\n    this.events.next({\n      type: 'translationLoadFailure',\n      payload: getEventPayload(lang),\n    });\n\n    return this.load(resolveLang, loadOptions);\n  }\n\n  private getMappedScope(scope: string): string {\n    const { scopeMapping = {} } = this.config;\n    return scopeMapping[scope] || toCamelCase(scope);\n  }\n\n  /**\n   * If lang is scope we need to check the following cases:\n   * todos/es => in this case we should take `es` as lang\n   * todos => in this case we should set the active lang as lang\n   */\n  private resolveLangAndScope(lang: string) {\n    let resolveLang = lang;\n    let scope;\n\n    if (this._isLangScoped(lang)) {\n      // en for example\n      const langFromScope = getLangFromScope(lang);\n      // en is lang\n      const hasLang = this.isLang(langFromScope);\n      // take en\n      resolveLang = hasLang ? langFromScope : this.getActiveLang();\n      // find the scope\n      scope = this.getMappedScope(hasLang ? getScopeFromLang(lang) : lang);\n    }\n\n    return { scope, resolveLang };\n  }\n\n  private getObjectByKey(translation: Translation, key?: string) {\n    const result: Translation = {};\n    const prefix = `${key}.`;\n\n    for (const currentKey in translation) {\n      if (currentKey.startsWith(prefix)) {\n        result[currentKey.replace(prefix, '')] = translation[currentKey];\n      }\n    }\n\n    return result;\n  }\n\n  private getEntries(key: HashMap | Map<string, HashMap>) {\n    return key instanceof Map ? key.entries() : Object.entries(key);\n  }\n}\n", "import { Component, Input } from '@angular/core';\n\n@Component({\n  template: `\n    <div class=\"transloco-loader-template\" [innerHTML]=\"html\"></div>\n  `,\n  standalone: true,\n})\nexport class TranslocoLoaderComponent {\n  @Input() html: string | undefined;\n}\n", "import { TemplateRef, Type, ViewContainerRef } from '@angular/core';\n\nimport { isString } from './helpers';\nimport { TranslocoLoaderComponent } from './loader-component.component';\n\nexport type Content = string | TemplateRef<unknown> | Type<unknown>;\n\nexport class TemplateHandler {\n  constructor(private view: Content, private vcr: ViewContainerRef) {}\n\n  attachView() {\n    if (this.view instanceof TemplateRef) {\n      this.vcr.createEmbeddedView(this.view);\n    } else if (isString(this.view)) {\n      const componentRef = this.vcr.createComponent(TranslocoLoaderComponent);\n      componentRef.instance.html = this.view;\n      componentRef.hostView.detectChanges();\n    } else {\n      this.vcr.createComponent(this.view);\n    }\n  }\n\n  detachView() {\n    this.vcr.clear();\n  }\n}\n", "import { InjectionToken } from '@angular/core';\n\nexport const TRANSLOCO_LANG = new InjectionToken<string>('TRANSLOCO_LANG');\n", "import { InjectionToken } from '@angular/core';\n\nimport { Content } from './template-handler';\n\nexport const TRANSLOCO_LOADING_TEMPLATE = new InjectionToken<Content>(\n  'TRANSLOCO_LOADING_TEMPLATE'\n);\n", "import { InjectionToken } from '@angular/core';\n\nimport { TranslocoScope } from './types';\n\nexport const TRANSLOCO_SCOPE = new InjectionToken<TranslocoScope>(\n  'TRANSLOCO_SCOPE'\n);\n", "import { getLangFromScope, getPipeValue, getScopeFromLang } from './shared';\n\ninterface LangResolverParams {\n  inline?: string;\n  provider: string | null | undefined;\n  active: string;\n}\n\nexport class LangResolver {\n  initialized = false;\n\n  // inline => provider => active\n  resolve({ inline, provider, active }: LangResolverParams): string {\n    let lang = active;\n    /**\n     * When the user changes the lang we need to update\n     * the view. Otherwise, the lang will remain the inline/provided lang\n     */\n    if (this.initialized) {\n      lang = active;\n\n      return lang;\n    }\n\n    if (provider) {\n      const [, extracted] = getPipeValue(provider, 'static');\n      lang = extracted;\n    }\n\n    if (inline) {\n      const [, extracted] = getPipeValue(inline, 'static');\n      lang = extracted;\n    }\n\n    this.initialized = true;\n\n    return lang;\n  }\n\n  /**\n   *\n   * Resolve the lang\n   *\n   * @example\n   *\n   * resolveLangBasedOnScope('todos/en') => en\n   * resolveLangBasedOnScope('en') => en\n   *\n   */\n  resolveLangBasedOnScope(lang: string) {\n    const scope = getScopeFromLang(lang);\n\n    return scope ? getLangFromScope(lang) : lang;\n  }\n\n  /**\n   *\n   * Resolve the lang path for loading\n   *\n   * @example\n   *\n   * resolveLangPath('todos', 'en') => todos/en\n   * resolveLangPath('en') => en\n   *\n   */\n  resolveLangPath(lang: string, scope?: string) {\n    return scope ? `${scope}/${lang}` : lang;\n  }\n}\n", "import { TranslocoScope, ProviderScope, OrArray } from './types';\nimport { TranslocoService } from './transloco.service';\nimport { isScopeObject, toCamelCase } from './helpers';\n\ntype ScopeResolverParams = {\n  inline: string | undefined;\n  provider: OrArray<TranslocoScope> | null;\n};\n\nexport class ScopeResolver {\n  constructor(private service: TranslocoService) {}\n\n  // inline => provider\n  resolve(params: ScopeResolverParams): string | undefined {\n    const { inline, provider } = params;\n    if (inline) {\n      return inline;\n    }\n\n    if (provider) {\n      if (isScopeObject(provider)) {\n        const { scope, alias = toCamelCase(scope) } = provider as ProviderScope;\n        this.service._setScopeAlias(scope, alias);\n\n        return scope;\n      }\n\n      return provider as string;\n    }\n\n    return undefined;\n  }\n}\n", "import {\n  ChangeDetectorRef,\n  Directive,\n  ElementRef,\n  EmbeddedViewRef,\n  inject,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Renderer2,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n} from '@angular/core';\nimport { forkJoin, Observable, Subscription, switchMap } from 'rxjs';\n\nimport { Content, TemplateHandler } from './template-handler';\nimport { TRANSLOCO_LANG } from './transloco-lang';\nimport { TRANSLOCO_LOADING_TEMPLATE } from './transloco-loading-template';\nimport { TRANSLOCO_SCOPE } from './transloco-scope';\nimport { TranslocoService } from './transloco.service';\nimport { HashMap, OrArray, Translation, TranslocoScope } from './types';\nimport {\n  listenOrNotOperator,\n  resolveInlineLoader,\n  shouldListenToLangChanges,\n} from './shared';\nimport { LangResolver } from './lang-resolver';\nimport { ScopeResolver } from './scope-resolver';\n\ntype TranslateFn = (key: string, params?: HashMap) => any;\ninterface ViewContext {\n  $implicit: TranslateFn;\n  currentLang: string;\n}\n\n@Directive({\n  selector: '[transloco]',\n  standalone: true,\n})\nexport class TranslocoDirective implements OnInit, OnDestroy, OnChanges {\n  private service = inject(TranslocoService);\n  private tpl = inject<TemplateRef<ViewContext>>(TemplateRef, {\n    optional: true,\n  });\n  private providerLang = inject(TRANSLOCO_LANG, { optional: true });\n  private providerScope: OrArray<TranslocoScope> | null = inject(\n    TRANSLOCO_SCOPE,\n    { optional: true }\n  );\n  private providedLoadingTpl = inject(TRANSLOCO_LOADING_TEMPLATE, {\n    optional: true,\n  });\n  private cdr = inject(ChangeDetectorRef);\n  private host = inject(ElementRef);\n  private vcr = inject(ViewContainerRef);\n  private renderer = inject(Renderer2);\n\n  subscription: Subscription | null = null;\n  view: EmbeddedViewRef<ViewContext> | undefined;\n\n  private translationMemo: Record<string, { value: any; params?: HashMap }> =\n    {};\n\n  @Input('transloco') key: string | undefined;\n  @Input('translocoParams') params: HashMap = {};\n  @Input('translocoScope') inlineScope: string | undefined;\n  @Input('translocoRead') inlineRead: string | undefined;\n  @Input('translocoLang') inlineLang: string | undefined;\n  @Input('translocoLoadingTpl') inlineTpl: Content | undefined;\n\n  private currentLang: string | undefined;\n  private loaderTplHandler: TemplateHandler | undefined;\n  // Whether we already rendered the view once\n  private initialized = false;\n  private path: string | undefined;\n  private langResolver = new LangResolver();\n  private scopeResolver = new ScopeResolver(this.service);\n  private readonly strategy = this.tpl === null ? 'attribute' : 'structural';\n\n  static ngTemplateContextGuard(\n    dir: TranslocoDirective,\n    ctx: unknown\n  ): ctx is ViewContext {\n    return true;\n  }\n\n  ngOnInit() {\n    const listenToLangChange = shouldListenToLangChanges(\n      this.service,\n      this.providerLang || this.inlineLang\n    );\n\n    this.subscription = this.service.langChanges$\n      .pipe(\n        switchMap((activeLang) => {\n          const lang = this.langResolver.resolve({\n            inline: this.inlineLang,\n            provider: this.providerLang,\n            active: activeLang,\n          });\n\n          return Array.isArray(this.providerScope)\n            ? forkJoin(\n                this.providerScope.map((providerScope) =>\n                  this.resolveScope(lang, providerScope)\n                )\n              )\n            : this.resolveScope(lang, this.providerScope);\n        }),\n        listenOrNotOperator(listenToLangChange)\n      )\n      .subscribe(() => {\n        this.currentLang = this.langResolver.resolveLangBasedOnScope(\n          this.path!\n        );\n        this.strategy === 'attribute'\n          ? this.attributeStrategy()\n          : this.structuralStrategy(this.currentLang, this.inlineRead);\n        this.cdr.markForCheck();\n        this.initialized = true;\n      });\n\n    if (!this.initialized) {\n      const loadingContent = this.resolveLoadingContent();\n      if (loadingContent) {\n        this.loaderTplHandler = new TemplateHandler(loadingContent, this.vcr);\n        this.loaderTplHandler.attachView();\n      }\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    // We need to support dynamic keys/params, so if this is not the first change CD cycle\n    // we need to run the function again in order to update the value\n    if (this.strategy === 'attribute') {\n      const notInit = Object.keys(changes).some((v) => !changes[v].firstChange);\n      notInit && this.attributeStrategy();\n    }\n  }\n\n  private attributeStrategy() {\n    this.detachLoader();\n    this.renderer.setProperty(\n      this.host.nativeElement,\n      'innerText',\n      this.service.translate(this.key!, this.params, this.currentLang)\n    );\n  }\n\n  private structuralStrategy(lang: string, read?: string) {\n    this.translationMemo = {};\n\n    if (this.view) {\n      // when the lang changes we need to change the reference so Angular will update the view\n      this.view.context['$implicit'] = this.getTranslateFn(lang, read);\n      this.view.context['currentLang'] = this.currentLang!;\n    } else {\n      this.detachLoader();\n      this.view = this.vcr.createEmbeddedView(this.tpl!, {\n        $implicit: this.getTranslateFn(lang, read),\n        currentLang: this.currentLang!,\n      });\n    }\n  }\n\n  protected getTranslateFn(\n    lang: string,\n    read: string | undefined\n  ): TranslateFn {\n    return (key: string, params?: HashMap) => {\n      const withRead = read ? `${read}.${key}` : key;\n      const withParams = params\n        ? `${withRead}${JSON.stringify(params)}`\n        : withRead;\n\n      if (\n        Object.prototype.hasOwnProperty.call(this.translationMemo, withParams)\n      ) {\n        return this.translationMemo[withParams].value;\n      }\n\n      this.translationMemo[withParams] = {\n        params,\n        value: this.service.translate(withRead, params, lang),\n      };\n\n      return this.translationMemo[withParams].value;\n    };\n  }\n\n  private resolveLoadingContent() {\n    return this.inlineTpl || this.providedLoadingTpl;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n      // Caretaker note: it's important to clean up references to subscriptions since they save the `next`\n      // callback within its `destination` property, preventing classes from being GC'd.\n      this.subscription = null;\n    }\n  }\n\n  private detachLoader() {\n    this.loaderTplHandler?.detachView();\n  }\n\n  private resolveScope(\n    lang: string,\n    providerScope: TranslocoScope | null\n  ): Observable<Translation | Translation[]> {\n    const resolvedScope = this.scopeResolver.resolve({\n      inline: this.inlineScope,\n      provider: providerScope,\n    });\n    this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n    const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n\n    return this.service._loadDependencies(this.path, inlineLoader);\n  }\n}\n", "import {\n  ChangeDetectorRef,\n  Inject,\n  OnDestroy,\n  Optional,\n  Pipe,\n  PipeTransform,\n} from '@angular/core';\nimport { switchMap, forkJoin, Observable, Subscription } from 'rxjs';\n\nimport { TranslocoService } from './transloco.service';\nimport { HashMap, OrArray, Translation, TranslocoScope } from './types';\nimport { TRANSLOCO_SCOPE } from './transloco-scope';\nimport { TRANSLOCO_LANG } from './transloco-lang';\nimport {\n  listenOrNotOperator,\n  resolveInlineLoader,\n  shouldListenToLangChanges,\n} from './shared';\nimport { LangResolver } from './lang-resolver';\nimport { ScopeResolver } from './scope-resolver';\n\n@Pipe({\n  name: 'transloco',\n  pure: false,\n  standalone: true,\n})\nexport class TranslocoPipe implements PipeTransform, OnDestroy {\n  private subscription: Subscription | null = null;\n  private lastValue = '';\n  private lastKey: string | undefined;\n  private path: string | undefined;\n  private langResolver = new LangResolver();\n  private scopeResolver!: ScopeResolver;\n\n  constructor(\n    private service: TranslocoService,\n    @Optional()\n    @Inject(TRANSLOCO_SCOPE)\n    private providerScope: OrArray<TranslocoScope> | undefined,\n    @Optional()\n    @Inject(TRANSLOCO_LANG)\n    private providerLang: string | undefined,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.scopeResolver = new ScopeResolver(this.service);\n  }\n\n  // null is for handling strict mode + async pipe types https://github.com/ngneat/transloco/issues/311\n  // null is for handling strict mode + optional chaining types https://github.com/ngneat/transloco/issues/488\n  transform(\n    key?: string | null,\n    params?: HashMap,\n    inlineLang?: string\n  ): string {\n    if (!key) {\n      return key as any;\n    }\n\n    const keyName = params ? `${key}${JSON.stringify(params)}` : key;\n\n    if (keyName === this.lastKey) {\n      return this.lastValue;\n    }\n\n    this.lastKey = keyName;\n    this.subscription?.unsubscribe();\n\n    const listenToLangChange = shouldListenToLangChanges(\n      this.service,\n      this.providerLang || inlineLang\n    );\n\n    this.subscription = this.service.langChanges$\n      .pipe(\n        switchMap((activeLang) => {\n          const lang = this.langResolver.resolve({\n            inline: inlineLang,\n            provider: this.providerLang,\n            active: activeLang,\n          });\n\n          return Array.isArray(this.providerScope)\n            ? forkJoin(\n                this.providerScope.map((providerScope) =>\n                  this.resolveScope(lang, providerScope)\n                )\n              )\n            : this.resolveScope(lang, this.providerScope);\n        }),\n        listenOrNotOperator(listenToLangChange)\n      )\n      .subscribe(() => this.updateValue(key, params));\n\n    return this.lastValue;\n  }\n\n  ngOnDestroy() {\n    this.subscription?.unsubscribe();\n    // Caretaker note: it's important to clean up references to subscriptions since they save the `next`\n    // callback within its `destination` property, preventing classes from being GC'd.\n    this.subscription = null;\n  }\n\n  private updateValue(key: string, params?: HashMap | undefined) {\n    const lang = this.langResolver.resolveLangBasedOnScope(this.path!);\n    this.lastValue = this.service.translate(key, params, lang);\n    this.cdr.markForCheck();\n  }\n\n  private resolveScope(\n    lang: string,\n    providerScope: TranslocoScope | null\n  ): Observable<Translation | Translation[]> {\n    const resolvedScope = this.scopeResolver.resolve({\n      inline: undefined,\n      provider: providerScope,\n    });\n    this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n    const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n\n    return this.service._loadDependencies(this.path, inlineLoader);\n  }\n}\n", "import { NgModule } from '@angular/core';\n\nimport { TranslocoDirective } from './transloco.directive';\nimport { TranslocoPipe } from './transloco.pipe';\n\nconst decl = [TranslocoDirective, TranslocoPipe];\n\n@NgModule({\n  imports: decl,\n  exports: decl,\n})\nexport class TranslocoModule {}\n", "import {\n  EnvironmentProviders,\n  makeEnvironmentProviders,\n  Provider,\n  Type,\n} from '@angular/core';\n\nimport { TRANSLOCO_LOADER, TranslocoLoader } from './transloco.loader';\nimport {\n  PartialTranslocoConfig,\n  TRANSLOCO_CONFIG,\n  translocoConfig,\n} from './transloco.config';\nimport { TRANSLOCO_SCOPE } from './transloco-scope';\nimport { TranslocoScope } from './types';\nimport {\n  DefaultTranspiler,\n  TRANSLOCO_TRANSPILER,\n  TranslocoTranspiler,\n} from './transloco.transpiler';\nimport {\n  Default<PERSON><PERSON><PERSON>,\n  TRANSLOCO_MISSING_HANDLER,\n  TranslocoMissingHandler,\n} from './transloco-missing-handler';\nimport {\n  DefaultInterceptor,\n  TRANSLOCO_INTERCEPTOR,\n  TranslocoInterceptor,\n} from './transloco.interceptor';\nimport {\n  DefaultFallbackStrategy,\n  TRANSLOCO_FALLBACK_STRATEGY,\n  TranslocoFallbackStrategy,\n} from './transloco-fallback-strategy';\nimport { TRANSLOCO_LOADING_TEMPLATE } from './transloco-loading-template';\nimport { Content } from './template-handler';\nimport { TRANSLOCO_LANG } from './transloco-lang';\n\nexport interface TranslocoOptions {\n  config: PartialTranslocoConfig;\n  loader?: Type<TranslocoLoader>;\n}\n\nexport function provideTransloco(options: TranslocoOptions) {\n  const providers: EnvironmentProviders[] = [\n    provideTranslocoTranspiler(DefaultTranspiler),\n    provideTranslocoMissingHandler(DefaultHandler),\n    provideTranslocoInterceptor(DefaultInterceptor),\n    provideTranslocoFallbackStrategy(DefaultFallbackStrategy),\n  ];\n\n  if (options.config) {\n    providers.push(provideTranslocoConfig(options.config));\n  }\n\n  if (options.loader) {\n    providers.push(provideTranslocoLoader(options.loader));\n  }\n\n  return providers;\n}\n\nexport function provideTranslocoConfig(config: PartialTranslocoConfig) {\n  return makeEnvironmentProviders([\n    {\n      provide: TRANSLOCO_CONFIG,\n      useValue: translocoConfig(config),\n    },\n  ]);\n}\n\nexport function provideTranslocoLoader(loader: Type<TranslocoLoader>) {\n  return makeEnvironmentProviders([\n    { provide: TRANSLOCO_LOADER, useClass: loader },\n  ]);\n}\n\nexport function provideTranslocoScope(scope: TranslocoScope) {\n  return {\n    provide: TRANSLOCO_SCOPE,\n    useValue: scope,\n    multi: true,\n  };\n}\n\nexport function provideTranslocoLoadingTpl(content: Content) {\n  return {\n    provide: TRANSLOCO_LOADING_TEMPLATE,\n    useValue: content,\n  };\n}\n\nexport function provideTranslocoTranspiler(\n  transpiler: Type<TranslocoTranspiler>\n) {\n  return makeEnvironmentProviders([\n    {\n      provide: TRANSLOCO_TRANSPILER,\n      useClass: transpiler,\n      deps: [TRANSLOCO_CONFIG],\n    },\n  ]);\n}\n\nexport function provideTranslocoFallbackStrategy(\n  strategy: Type<TranslocoFallbackStrategy>\n) {\n  return makeEnvironmentProviders([\n    {\n      provide: TRANSLOCO_FALLBACK_STRATEGY,\n      useClass: strategy,\n      deps: [TRANSLOCO_CONFIG],\n    },\n  ]);\n}\n\nexport function provideTranslocoMissingHandler(\n  handler: Type<TranslocoMissingHandler>\n) {\n  return makeEnvironmentProviders([\n    {\n      provide: TRANSLOCO_MISSING_HANDLER,\n      useClass: handler,\n    },\n  ]);\n}\n\nexport function provideTranslocoInterceptor(\n  interceptor: Type<TranslocoInterceptor>\n) {\n  return makeEnvironmentProviders([\n    {\n      provide: TRANSLOCO_INTERCEPTOR,\n      useClass: interceptor,\n    },\n  ]);\n}\n\nexport function provideTranslocoLang(lang: string): Provider {\n  return {\n    provide: TRANSLOCO_LANG,\n    useValue: lang,\n  };\n}\n", "import {\n  APP_INITIALIZER,\n  Inject,\n  Injectable,\n  InjectionToken,\n  ModuleWithProviders,\n  NgModule,\n} from '@angular/core';\nimport { Observable, of } from 'rxjs';\n\nimport { TranslocoLoader } from './transloco.loader';\nimport { HashMap, Translation } from './types';\nimport { TranslocoModule } from './transloco.module';\nimport { provideTransloco } from './transloco.providers';\nimport { TranslocoConfig } from './transloco.config';\nimport { TranslocoService } from './transloco.service';\n\nexport interface TranslocoTestingOptions {\n  translocoConfig?: Partial<TranslocoConfig>;\n  preloadLangs?: boolean;\n  langs?: HashMap<Translation>;\n}\n\nconst TRANSLOCO_TEST_LANGS = new InjectionToken<HashMap<Translation>>(\n  'TRANSLOCO_TEST_LANGS - Available testing languages'\n);\nconst TRANSLOCO_TEST_OPTIONS = new InjectionToken<TranslocoTestingOptions>(\n  'TRANSLOCO_TEST_OPTIONS - Testing options'\n);\n\n@Injectable()\nexport class TestingLoader implements TranslocoLoader {\n  constructor(\n    @Inject(TRANSLOCO_TEST_LANGS) private langs: HashMap<Translation>\n  ) {}\n\n  getTranslation(lang: string): Observable<Translation> | Promise<Translation> {\n    return of(this.langs[lang]);\n  }\n}\n\nexport function initTranslocoService(\n  service: TranslocoService,\n  langs: HashMap<Translation> = {},\n  options: TranslocoTestingOptions\n) {\n  const preloadAllLangs = () =>\n    options.preloadLangs\n      ? Promise.all(\n          Object.keys(langs).map((lang) => service.load(lang).toPromise())\n        )\n      : Promise.resolve();\n\n  return preloadAllLangs;\n}\n\n@NgModule({\n  exports: [TranslocoModule],\n})\nexport class TranslocoTestingModule {\n  static forRoot(\n    options: TranslocoTestingOptions\n  ): ModuleWithProviders<TranslocoTestingModule> {\n    return {\n      ngModule: TranslocoTestingModule,\n      providers: [\n        provideTransloco({\n          loader: TestingLoader,\n          config: {\n            prodMode: true,\n            missingHandler: { logMissingKey: false },\n            ...options.translocoConfig,\n          },\n        }),\n        {\n          provide: TRANSLOCO_TEST_LANGS,\n          useValue: options.langs,\n        },\n        {\n          provide: TRANSLOCO_TEST_OPTIONS,\n          useValue: options,\n        },\n        {\n          provide: APP_INITIALIZER,\n          useFactory: initTranslocoService,\n          deps: [\n            TranslocoService,\n            TRANSLOCO_TEST_LANGS,\n            TRANSLOCO_TEST_OPTIONS,\n          ],\n          multi: true,\n        },\n      ],\n    };\n  }\n}\n", "import { isBrowser } from './helpers';\n\n/**\n * Returns the language code name from the browser, e.g. \"en\"\n */\nexport function getBrowserLang(): string | undefined {\n  let browserLang = getBrowserCultureLang();\n  if (!browserLang || !isBrowser()) {\n    return undefined;\n  }\n\n  if (browserLang.indexOf('-') !== -1) {\n    browserLang = browserLang.split('-')[0];\n  }\n\n  if (browserLang.indexOf('_') !== -1) {\n    browserLang = browserLang.split('_')[0];\n  }\n\n  return browserLang;\n}\n\n/**\n * Returns the culture language code name from the browser, e.g. \"en-US\"\n */\nexport function getBrowserCultureLang(): string {\n  if (!isBrowser()) {\n    return '';\n  }\n\n  const navigator = window.navigator;\n\n  return navigator.languages?.[0] ?? navigator.language;\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["_unflatten", "_flatten", "i1.TranslocoService"], "mappings": ";;;;;MAgBa,aAAa,CAAA;AACJ,IAAA,YAAA,CAAA;AAApB,IAAA,WAAA,CAAoB,YAAsC,EAAA;QAAtC,IAAY,CAAA,YAAA,GAAZ,YAAY,CAA0B;KAAI;AAE9D,IAAA,cAAc,CAAC,IAAY,EAAA;AACzB,QAAA,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;KAC9C;AACF,CAAA;MAEY,gBAAgB,GAAG,IAAI,cAAc,CAChD,kBAAkB;;ACrBJ,SAAA,QAAQ,CAAI,GAAM,EAAE,IAAa,EAAA;IAC/C,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,OAAO,GAAG,CAAC;AACZ,KAAA;;AAGD,IAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;AACnD,QAAA,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;AAClB,KAAA;IAED,OAAQ,IAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,GAAU,CAAC,CAAC;AAC1E,CAAC;SAEe,QAAQ,CAAC,GAAQ,EAAE,IAAY,EAAE,GAAQ,EAAA;AACvD,IAAA,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IAEjB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAEnC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,KAAI;QAChC,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,YAAA,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AACjB,SAAA;AAAM,aAAA;AACL,YAAA,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClC,kBAAE,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;kBACjB,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;AACtB,SAAA;AAED,QAAA,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;KACzB,EAAE,GAAG,CAAC,CAAC;AAER,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAEK,SAAU,IAAI,CAAC,UAAe,EAAA;IAClC,IAAI,CAAC,UAAU,EAAE;AACf,QAAA,OAAO,CAAC,CAAC;AACV,KAAA;AAED,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QAC7B,OAAO,UAAU,CAAC,MAAM,CAAC;AAC1B,KAAA;AAED,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxB,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;AACvC,KAAA;IAED,OAAO,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5C,CAAC;AAEK,SAAU,OAAO,CAAC,UAAe,EAAA;AACrC,IAAA,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAEK,SAAU,UAAU,CAAC,GAAY,EAAA;AACrC,IAAA,OAAO,OAAO,GAAG,KAAK,UAAU,CAAC;AACnC,CAAC;AAEK,SAAU,QAAQ,CAAC,GAAY,EAAA;AACnC,IAAA,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC;AACjC,CAAC;AAEK,SAAU,QAAQ,CAAC,GAAY,EAAA;AACnC,IAAA,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC;AACjC,CAAC;AAEK,SAAU,QAAQ,CAAC,IAAa,EAAA;AACpC,IAAA,OAAO,CAAC,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpE,CAAC;AAIK,SAAU,WAAW,CAAI,KAAc,EAAA;AAC3C,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;AAChD,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,WAAW,CAAC,GAAW,EAAA;AACrC,IAAA,OAAO,GAAG;SACP,OAAO,CAAC,qBAAqB,EAAE,CAAC,IAAI,EAAE,KAAK,KAC1C,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,CACrD;AACA,SAAA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;AAChC,CAAC;SAEe,SAAS,GAAA;AACvB,IAAA,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACvC,CAAC;AAEK,SAAU,KAAK,CAAC,KAAc,EAAA;AAClC,IAAA,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/C,CAAC;AAEK,SAAU,SAAS,CAAC,KAAc,EAAA;AACtC,IAAA,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;AAChC,CAAC;AAEK,SAAU,QAAQ,CAAC,KAAsB,EAAA;IAC7C,IAAI,QAAQ,CAAC,KAAK,CAAC;AAAE,QAAA,OAAO,KAAK,CAAC;AAElC,IAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;AAChE,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEK,SAAU,aAAa,CAAC,IAAS,EAAA;IACrC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;AAChD,CAAC;AAEK,SAAU,YAAY,CAAC,IAAS,EAAA;AACpC,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AAC1D,CAAC;AAEK,SAAU,eAAe,CAAC,IAAS,EAAA;IACvC,OAAO,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAEK,SAAU,SAAS,CAAC,GAAgB,EAAA;AACxC,IAAA,OAAOA,WAAU,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAEK,SAAU,OAAO,CAAC,GAAgB,EAAA;IACtC,OAAOC,SAAQ,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvC;;MClHa,gBAAgB,GAAG,IAAI,cAAc,CAChD,kBAAkB,EAClB;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,aAAa;AAC7B,CAAA,EACD;AAEW,MAAA,aAAa,GAAoB;AAC5C,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,oBAAoB,EAAE,KAAK;AAC3B,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,aAAa,EAAE,CAAC;AAChB,IAAA,YAAY,EAAE,EAAE;AAChB,IAAA,cAAc,EAAE,EAAE;AAClB,IAAA,cAAc,EAAE;AACd,QAAA,aAAa,EAAE,IAAI;AACnB,QAAA,sBAAsB,EAAE,KAAK;AAC7B,QAAA,UAAU,EAAE,KAAK;AAClB,KAAA;AACD,IAAA,OAAO,EAAE;AACP,QAAA,GAAG,EAAE,KAAK;AACX,KAAA;AACD,IAAA,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAC3B;AAUc,SAAA,eAAe,CAC7B,MAAA,GAAiC,EAAE,EAAA;IAEnC,OAAO;AACL,QAAA,GAAG,aAAa;AAChB,QAAA,GAAG,MAAM;AACT,QAAA,cAAc,EAAE;YACd,GAAG,aAAa,CAAC,cAAc;YAC/B,GAAG,MAAM,CAAC,cAAc;AACzB,SAAA;AACD,QAAA,OAAO,EAAE;YACP,GAAG,aAAa,CAAC,OAAO;YACxB,GAAG,MAAM,CAAC,OAAO;AAClB,SAAA;KACF,CAAC;AACJ;;MCtDa,oBAAoB,GAAG,IAAI,cAAc,CACpD,sBAAsB,EACtB;MAeW,iBAAiB,CAAA;AAClB,IAAA,oBAAoB,CAAS;;AAGvC,IAAA,WAAA,CAAkD,MAAwB,EAAA;QACxE,IAAI,CAAC,oBAAoB,GAAG,cAAc,CAAC,MAAM,IAAI,aAAa,CAAC,CAAC;KACrE;IAED,SAAS,CACP,KAAU,EACV,MAAA,GAAkB,EAAE,EACpB,WAAwB,EACxB,GAAW,EAAA;AAEX,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,YAAA,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE,KAAK,KAAI;AAC3D,gBAAA,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AACrB,gBAAA,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;AAC5B,oBAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,iBAAA;AAED,gBAAA,OAAO,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAClC,sBAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC;sBAC5D,EAAE,CAAC;AACT,aAAC,CAAC,CAAC;AACJ,SAAA;AAAM,aAAA,IAAI,MAAM,EAAE;AACjB,YAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,gBAAA,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;AAC5D,aAAA;AAAM,iBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC/B,gBAAA,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;AAC3D,aAAA;AACF,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;;;;;;;;;;;;;;;;;;;;;AAuBG;IACO,YAAY,CACpB,KAAU,EACV,MAAA,GAAkB,EAAE,EACpB,WAAwB,EACxB,GAAW,EAAA;QAEX,IAAI,MAAM,GAAG,KAAK,CAAC;QAEnB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;;YAEhC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;;YAE9B,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;;AAGtC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;;YAGlE,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AAC3C,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,MAAM,CAAC;KACf;IAES,WAAW,CACnB,KAAe,EACf,MAAA,GAAkB,EAAE,EACpB,WAAwB,EACxB,GAAW,EAAA;QAEX,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;KACtE;AA3FU,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,kBAII,gBAAgB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAJrC,iBAAiB,EAAA,CAAA,CAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;;0BAKI,QAAQ;;0BAAI,MAAM;2BAAC,gBAAgB,CAAA;;AA0FlD,SAAS,cAAc,CAAC,MAAuB,EAAA;IAC7C,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC;IAE1C,OAAO,IAAI,MAAM,CAAC,CAAG,EAAA,KAAK,CAAQ,KAAA,EAAA,GAAG,CAAE,CAAA,EAAE,GAAG,CAAC,CAAC;AAChD,CAAC;AAMK,SAAU,eAAe,CAAC,UAAkB,EAAA;AAChD,IAAA,MAAM,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IACzD,MAAM,IAAI,GAAG,EAAE,CAAC;AAChB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAC/B,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;AACvC,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChD,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAGK,MAAO,oBACX,SAAQ,iBAAiB,CAAA;AAGf,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IAEtC,SAAS,CACP,KAAU,EACV,MAAA,GAAkB,EAAE,EACpB,WAAwB,EACxB,GAAW,EAAA;QAEX,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,YAAA,UAAU,GAAG,KAAK,CAAC,OAAO,CACxB,6BAA6B,EAC7B,CAAC,KAAa,EAAE,YAAoB,EAAE,IAAY,KAAI;gBACpD,IAAI;oBACF,MAAM,IAAI,GACR,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBAElC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,iBAAA;AAAC,gBAAA,OAAO,CAAM,EAAE;oBACf,IAAI,OAAO,GAAG,CAAA,uBAAA,EAA0B,KAAK,CAAA;AAC+D,sHAAA,EAAA,YAAY,cAAc,CAAC;oBACvI,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;AAC3C,wBAAA,OAAO,GAAG,CAAA,mBAAA,EAAsB,YAAY,CAAA,yDAAA,CAA2D,CAAC;AACzG,qBAAA;AACD,oBAAA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AAC1B,iBAAA;AACH,aAAC,CACF,CAAC;AACH,SAAA;AAED,QAAA,OAAO,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;KAC9D;uGAnCU,oBAAoB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAApB,oBAAoB,EAAA,CAAA,CAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;;;MCpJE,yBAAyB,GACpC,IAAI,cAAc,CAA8B,2BAA2B,EAAE;MAWlE,cAAc,CAAA;IACzB,MAAM,CAAC,GAAW,EAAE,MAAuB,EAAA;QACzC,IAAI,MAAM,CAAC,cAAc,CAAC,aAAa,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC3D,YAAA,MAAM,GAAG,GAAG,CAA4B,yBAAA,EAAA,GAAG,GAAG,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,CAAA,GAAA,EAAM,GAAG,CAAE,CAAA,EAAE,6BAA6B,CAAC,CAAC;AAC1D,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACZ;uGARU,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAAd,cAAc,EAAA,CAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,UAAU;;;MCZE,qBAAqB,GAAG,IAAI,cAAc,CACrD,uBAAuB,EACvB;MASW,kBAAkB,CAAA;AAC7B,IAAA,kBAAkB,CAAC,WAAwB,EAAA;AACzC,QAAA,OAAO,WAAW,CAAC;KACpB;IAED,qBAAqB,CAAC,CAAS,EAAE,KAAa,EAAA;AAC5C,QAAA,OAAO,KAAK,CAAC;KACd;uGAPU,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAAlB,kBAAkB,EAAA,CAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;;;MCVE,2BAA2B,GACtC,IAAI,cAAc,CAA4B,6BAA6B,EAAE;MAOlE,uBAAuB,CAAA;AACY,IAAA,UAAA,CAAA;AAA9C,IAAA,WAAA,CAA8C,UAA2B,EAAA;QAA3B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAiB;KAAI;IAE7E,YAAY,GAAA;AACV,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QAClD,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;AACH,SAAA;AAED,QAAA,OAAO,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,CAAC,YAAY,CAAC,CAAC;KACpE;AAZU,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,kBACd,gBAAgB,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GADzB,uBAAuB,EAAA,CAAA,CAAA;;2FAAvB,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBADnC,UAAU;;0BAEI,MAAM;2BAAC,gBAAgB,CAAA;;;ACPtC;;;;;AAKG;AACG,SAAU,gBAAgB,CAAC,IAAY,EAAA;IAC3C,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,KAAK,CAAC,GAAG,EAAE,CAAC;AAEZ,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;;;;AAKG;AACG,SAAU,gBAAgB,CAAC,IAAY,EAAA;IAC3C,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;IAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAC;AAChC,CAAC;AAED;;;;;;AAMG;AACG,SAAU,YAAY,CAC1B,GAAuB,EACvB,KAAa,EACb,IAAI,GAAG,GAAG,EAAA;AAEV,IAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACjB,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACjC,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,EAAG,CAAC;QAEjC,OAAO,QAAQ,KAAK,KAAK,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7E,KAAA;AAED,IAAA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACrB,CAAC;AAEe,SAAA,yBAAyB,CACvC,OAAyB,EACzB,IAAa,EAAA;IAEb,MAAM,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACjD,IAAI,CAAC,SAAS,EAAE;;AAEd,QAAA,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;AAC9C,KAAA;;AAGD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEK,SAAU,mBAAmB,CACjC,kBAA4B,EAAA;AAE5B,IAAA,OAAO,kBAAkB,GAAG,CAAC,MAAqB,KAAK,MAAM,GAAG,IAAI,CAAI,CAAC,CAAC,CAAC;AAC7E,CAAC;AAED,SAAS,YAAY,CAAC,YAA0B,EAAE,KAAa,EAAA;AAC7D,IAAA,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AACpD,QAAA,GAAG,CAAC,CAAA,EAAG,KAAK,CAAA,CAAA,EAAI,IAAI,CAAA,CAAE,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAE7C,QAAA,OAAO,GAAG,CAAC;KACZ,EAAE,EAAsD,CAAC,CAAC;AAC7D,CAAC;AAEe,SAAA,mBAAmB,CACjC,aAAoC,EACpC,KAAc,EAAA;IAEd,OAAO,eAAe,CAAC,aAAa,CAAC;UACjC,YAAY,CAAC,aAAa,CAAC,MAAO,EAAE,KAAM,CAAC;UAC3C,SAAS,CAAC;AAChB,CAAC;AAEK,SAAU,eAAe,CAAC,IAAY,EAAA;IAC1C,OAAO;AACL,QAAA,KAAK,EAAE,gBAAgB,CAAC,IAAI,CAAC,IAAI,IAAI;AACrC,QAAA,QAAQ,EAAE,gBAAgB,CAAC,IAAI,CAAC;KACjC,CAAC;AACJ;;AC1FM,SAAU,aAAa,CAAC,OAAgB,EAAA;IAC5C,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AAEzD,IAAA,IAAI,YAAY,EAAE;AAChB,QAAA,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AACtC,QAAA,IAAI,UAAU,CAAC,UAAU,CAAC,KAAK,KAAK,EAAE;YACpC,MAAM,CAAA,8DAAA,EAAiE,IAAI,CAAA,CAAE,CAAC;AAC/E,SAAA;QAED,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,KACnC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,GAAG,GAAG,CAChC,CAAC;AACH,KAAA;IAED,OAAO,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC/C;;ACZgB,SAAA,mBAAmB,CAAC,EAClC,UAAU,EACV,IAAI,EACJ,IAAI,EACJ,YAAY,EACZ,YAAY,GACJ,EAAA;AACR,IAAA,MAAM,KAAK,GAAG,YAAY,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAE3D,IAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAI;AACxB,QAAA,MAAM,MAAM,GAAG,aAAa,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;AAEvE,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CACtB,GAAG,CAAC,CAAC,WAAW,MAAM;YACpB,WAAW;AACX,YAAA,IAAI,EAAE,IAAI;SACX,CAAC,CAAC,CACJ,CAAC;AACJ,KAAC,CAAC,CAAC;AACL;;AC0CA,IAAI,OAAyB,CAAC;AAExB,SAAU,SAAS,CACvB,GAAoB,EACpB,MAAkB,GAAA,EAAE,EACpB,IAAa,EAAA;IAEb,OAAO,OAAO,CAAC,SAAS,CAAI,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACjD,CAAC;AAEK,SAAU,eAAe,CAC7B,GAAoB,EACpB,MAAkB,GAAA,EAAE,EACpB,IAAa,EAAA;IAEb,OAAO,OAAO,CAAC,eAAe,CAAI,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;MAGY,gBAAgB,CAAA;AAoBqB,IAAA,MAAA,CAAA;AACR,IAAA,MAAA,CAAA;AAE9B,IAAA,cAAA,CAAA;AAC+B,IAAA,WAAA,CAAA;AAG/B,IAAA,gBAAA,CAAA;AA1BV,IAAA,YAAY,CAAqB;IAEzB,YAAY,GAAwB,IAAI,CAAC;AACzC,IAAA,YAAY,GAAG,IAAI,GAAG,EAAuB,CAAC;AAC9C,IAAA,KAAK,GAAG,IAAI,GAAG,EAAmC,CAAC;AACnD,IAAA,iBAAiB,CAAqB;IACtC,WAAW,GAAG,EAAE,CAAC;IACjB,cAAc,GAAmB,EAAE,CAAC;IACpC,qBAAqB,GAAG,KAAK,CAAC;AAC9B,IAAA,IAAI,CAA0B;AAC9B,IAAA,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;AAChC,IAAA,MAAM,GAAG,IAAI,OAAO,EAAmB,CAAC;AAEhD,IAAA,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;AAC5B,IAAA,MAAM,CAEb;IAEF,WACgD,CAAA,MAAuB,EAC/B,MAA2B,EAEzD,cAAuC,EACR,WAAiC,EAC9C,UAA2B,EAE7C,gBAA2C,EAAA;QAPL,IAAM,CAAA,MAAA,GAAN,MAAM,CAAiB;QAC/B,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqB;QAEzD,IAAc,CAAA,cAAA,GAAd,cAAc,CAAyB;QACR,IAAW,CAAA,WAAA,GAAX,WAAW,CAAsB;QAGhE,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAA2B;AAEnD,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACpD,SAAA;QACD,OAAO,GAAG,IAAI,CAAC;AACf,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC;AACzD,QAAA,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAS,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;;;QAG/D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;AAE7C;;AAEG;AACH,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;YAC/C,IAAI,CAAC,CAAC,IAAI,KAAK,wBAAwB,IAAI,CAAC,CAAC,UAAU,EAAE;gBACvD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACxC,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;IAED,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;AAED,IAAA,cAAc,CAAC,IAAY,EAAA;AACzB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KACzB;IAED,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;KAC7B;AAED,IAAA,aAAa,CAAC,IAAY,EAAA;QACxB,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACf,YAAA,IAAI,EAAE,aAAa;AACnB,YAAA,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC;AAC/B,SAAA,CAAC,CAAC;AACH,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,iBAAiB,CAAC,KAAqB,EAAA;AACrC,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;KAC7B;AAED;;;;;;AAMG;IACH,iBAAiB,GAAA;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;AAED,IAAA,IAAI,CAAC,IAAY,EAAE,OAAA,GAAuB,EAAE,EAAA;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACpC,QAAA,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AAED,QAAA,IAAI,eAEH,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACzC,QAAA,IAAI,KAAa,CAAC;AAClB,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,MAAM,cAAc,GAAG;YACrB,IAAI;YACJ,UAAU,EAAE,IAAI,CAAC,MAAM;YACvB,YAAY,EAAE,OAAO,CAAC,YAAY;AAClC,YAAA,IAAI,EAAE,OAAO,GAAG,EAAE,KAAK,EAAE,KAAM,EAAE,GAAG,SAAS;SAC9C,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;;YAErC,MAAM,QAAQ,GAAG,OAAO;AACtB,kBAAE,CAAG,EAAA,KAAM,IAAI,IAAI,CAAC,iBAAiB,CAAE,CAAA;AACvC,kBAAE,IAAI,CAAC,iBAAiB,CAAC;YAE3B,MAAM,OAAO,GAAG,mBAAmB,CAAC;AAClC,gBAAA,GAAG,cAAc;AACjB,gBAAA,YAAY,EAAE,QAAS;AACxB,aAAA,CAAC,CAAC;AACH,YAAA,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrC,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,MAAM,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC;AAC7C,YAAA,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AAChC,SAAA;QAED,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAChC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAChC,GAAG,CAAC,CAAC,WAAW,KAAI;AAClB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAC9B,gBAAA,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;oBACxB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC;;AAE1C,oBAAA,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AACnB,wBAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC,qBAAA;AACH,iBAAC,CAAC,CAAC;gBACH,OAAO;AACR,aAAA;AACD,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACxC,SAAC,CAAC,EACF,UAAU,CAAC,CAAC,KAAK,KAAI;AACnB,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACzB,OAAO,CAAC,KAAK,CAAC,CAAA,4BAAA,EAA+B,IAAI,CAAG,CAAA,CAAA,EAAE,KAAK,CAAC,CAAC;AAC9D,aAAA;YAED,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3C,SAAC,CAAC,EACF,WAAW,CAAC,CAAC,CAAC,CACf,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAE5B,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;;;;;;;;;AAUG;IACH,SAAS,CACP,GAAoB,EACpB,MAAkB,GAAA,EAAE,EACpB,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,EAAA;AAE3B,QAAA,IAAI,CAAC,GAAG;AAAE,YAAA,OAAO,GAAU,CAAC;AAE5B,QAAA,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAE9D,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACtB,YAAA,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KACf,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAA,EAAG,KAAK,CAAA,CAAA,EAAI,CAAC,CAAA,CAAE,GAAG,CAAC,EAAE,MAAM,EAAE,WAAW,CAAC,CAC1D,CAAC;AACV,SAAA;AAED,QAAA,GAAG,GAAG,KAAK,GAAG,CAAG,EAAA,KAAK,CAAI,CAAA,EAAA,GAAG,CAAE,CAAA,GAAG,GAAG,CAAC;QAEtC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AACrD,QAAA,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QAE/B,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACnD,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;KAC/D;AAED;;;;;;;;;;AAUG;IACH,eAAe,CACb,GAAoB,EACpB,MAAgB,EAChB,IAAiD,EACjD,SAAS,GAAG,KAAK,EAAA;AAEjB,QAAA,IAAI,YAAsC,CAAC;QAC3C,MAAM,IAAI,GAAG,CAAC,IAAY,EAAE,OAAqB,KAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,IAAI,CAC3B,GAAG,CAAC,MACF,SAAS;cACL,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC;AACzC,cAAE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CACtC,CACF,CAAC;AACJ,QAAA,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;AACf,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChE,SAAA;QAED,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;;AAE7C,YAAA,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC3D,YAAA,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC;YAC3B,YAAY,GAAG,mBAAmB,CAAC,aAAa,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;AACxE,SAAA;QAED,IAAI,GAAG,IAAc,CAAC;AACtB,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;AACnD,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;AACnB,SAAA;;QAED,MAAM,KAAK,GAAG,IAAI,CAAC;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAC3B,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAG,EAAA,KAAK,CAAI,CAAA,EAAA,IAAI,CAAE,CAAA,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC,CAChE,CAAC;KACH;AAED;;;;;;;AAOG;AACK,IAAA,eAAe,CAAC,IAAY,EAAA;QAClC,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;KAC5C;IAsBD,eAAe,CACb,GAA0B,EAC1B,MAAyB,GAAA,EAAE,EAC3B,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,EAAA;QAE3B,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACvC,YAAA,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAC9D,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACtB,gBAAA,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KACf,IAAI,CAAC,eAAe,CAClB,KAAK,GAAG,CAAA,EAAG,KAAK,CAAA,CAAA,EAAI,CAAC,CAAA,CAAE,GAAG,CAAC,EAC3B,MAAO,EACP,WAAW,CACZ,CACK,CAAC;AACV,aAAA;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AACrD,YAAA,GAAG,GAAG,KAAK,GAAG,CAAG,EAAA,KAAK,CAAI,CAAA,EAAA,GAAG,CAAE,CAAA,GAAG,GAAG,CAAC;AAEtC,YAAA,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;;YAE/D,OAAO,OAAO,CAAC,KAAK,CAAC;kBACjB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAO,EAAE,IAAI,CAAC;AACpC,kBAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,MAAO,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;AAC7D,SAAA;QAED,MAAM,YAAY,GAAQ,EAAE,CAAC;AAC7B,QAAA,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAClD,YAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9D,SAAA;AAED,QAAA,OAAO,YAAY,CAAC;KACrB;AAsBD,IAAA,qBAAqB,CACnB,GAA0B,EAC1B,MAAuB,EACvB,IAAa,EAAA;QAEb,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,eAAe,CAAI,GAAG,EAAE,MAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1D,SAAA;AAED,QAAA,MAAM,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAEhE;AACuH;AACvH,QAAA,OAAO,IAAI,CAAC,qBAAqB,CAAI,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,IAAI,CACpE,GAAG,CAAC,CAAC,KAAK,KAAI;AACZ,YAAA,MAAM,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC;YAC7B,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;AAClC,gBAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAI,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AACjE,aAAA;AAED,YAAA,OAAO,YAAY,CAAC;SACrB,CAAC,CACH,CAAC;KACH;AAaD,IAAA,cAAc,CAAC,WAAoB,EAAA;AACjC,QAAA,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;gBAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AACjD,aAAA;AAAM,iBAAA;;AAEL,gBAAA,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;AACrE,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBAE7D,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAChD,aAAA;AACF,SAAA;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AAED;;;;;;;;;AASG;AACH,IAAA,iBAAiB,CAAC,IAAa,EAAA;AAC7B,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;AAClC,QAAA,IAAI,IAAI,EAAE;YACR,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC;YAC3D,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,kBAAkB,EAAE;AAC3C,gBAAA,SAAS,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;AACtB,aAAA;AAAM,iBAAA;gBACL,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAChC,GAAG,CAAC,CAAC,WAAW,KAAK,CAAG,EAAA,IAAI,IAAI,WAAW,CAAA,CAAE,CAAC,CAC/C,CAAC;AACH,aAAA;AACF,SAAA;AAED,QAAA,OAAO,SAAS,CAAC,IAAI,CACnB,SAAS,CAAC,CAAC,QAAQ,KACjB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CACnE,CACF,CAAC;KACH;AAED;;;;;;;;;AASG;IACH,cAAc,CACZ,WAAwB,EACxB,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,EAC3B,OAAA,GAAiC,EAAE,EAAA;QAEnC,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;QACnD,MAAM,aAAa,GAAG,EAAE,GAAG,QAAQ,EAAE,GAAG,OAAO,EAAE,CAAC;AAClD,QAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAErC;;;AAGG;QACH,IAAI,yBAAyB,GAAG,WAAW,CAAC;;AAG5C,QAAA,IAAI,KAAK,EAAE;YACT,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACvC,yBAAyB,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,GAAG,WAAW,EAAE,CAAC,CAAC;AAC7D,SAAA;AAED,QAAA,MAAM,WAAW,GAAG,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAE1D,QAAA,MAAM,iBAAiB,GAAG;YACxB,IAAI,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAC5D,YAAA,GAAG,yBAAyB;SAC7B,CAAC;QAEF,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAQ,CAAC,GAAG;AACjD,cAAE,iBAAiB;AACnB,cAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC/B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAClD,kBAAkB,EAClB,WAAW,CACZ,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC7C,QAAA,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KACtE;AAED;;;;;;;;;AASG;IACH,iBAAiB,CACf,GAAW,EACX,KAAa;;AAEb,IAAA,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,EAC3B,UAAgD,EAAE,EAAA;AAElD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1E,QAAA,MAAM,QAAQ,GAAG;YACf,CAAC,GAAG,GAAG,QAAQ;SAChB,CAAC;AAEF,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;KAClE;AAED;;;AAGG;IACH,oCAAoC,CAAC,EACnC,YAAY,GAC0B,EAAA;AACtC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;QAC1E,IAAI,YAAY,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;AACrD,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAK,CAAC;AAChC,SAAA;KACF;AAED;;AAEG;AACH,IAAA,iBAAiB,CAAC,GAAW,EAAE,KAAU,EAAE,MAAgB,EAAA;QACzD,IAAI,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC,UAAU,IAAI,KAAK,KAAK,EAAE,EAAE;AAC1D,YAAA,OAAO,EAAE,CAAC;AACX,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,sBAAsB,EAAE,EAAE;;AAEhE,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAClC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAClC,GAAG,EACH,MAAM,EACN,IAAI,CAAC,iBAAkB,CACxB,CAAC;AACF,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AAEnC,YAAA,OAAO,aAAa,CAAC;AACtB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAC/B,GAAG,EACH,IAAI,CAAC,qBAAqB,EAAE,EAC5B,MAAM,CACP,CAAC;KACH;AAED;;AAEG;AACH,IAAA,aAAa,CAAC,IAAY,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACzD;AAED;;;;;AAKG;AACH,IAAA,MAAM,CAAC,IAAY,EAAA;AACjB,QAAA,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACzD;AAED;;;;;AAKG;IACH,iBAAiB,CACf,IAAY,EACZ,YAA2B,EAAA;AAE3B,QAAA,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAExC,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE;AACnE,YAAA,OAAO,aAAa,CAAC;AACnB,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,EAAE,CAAC;AAClC,aAAA,CAAC,CAAC;AACJ,SAAA;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;KAC1C;AAED;;AAEG;AACH,IAAA,sBAAsB,CAAC,WAAmB,EAAA;AACxC,QAAA,IACE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/B,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,EAC3C;YACA,OAAO,CAAA,EAAG,WAAW,CAAI,CAAA,EAAA,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;AACjD,SAAA;AACD,QAAA,OAAO,WAAW,CAAC;KACpB;AAED;;AAEG;IACH,cAAc,CAAC,KAAa,EAAE,KAAa,EAAA;AACzC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;AAC7B,YAAA,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,EAAE,CAAC;AAC/B,SAAA;QACD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;KACzC;IAED,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;;;AAGhC,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC1B,SAAA;;;;;AAKD,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;KACpB;AAEO,IAAA,mBAAmB,CAAC,IAAY,EAAA;QACtC,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;KACxC;IAEO,oBAAoB,GAAA;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC;AAE1C,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,YAAA,OAAO,IAAI,CAAC,iBAAiB,EAAc,CAAC;AAC7C,SAAA;AAED,QAAA,OAAQ,IAAI,CAAC,iBAAiB,EAAuB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;KACxE;IAEO,qBAAqB,GAAA;QAC3B,OAAO;YACL,GAAG,IAAI,CAAC,MAAM;AACd,YAAA,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC;KACH;AAED;;;AAGG;AACK,IAAA,sBAAsB,CAAC,IAAa,EAAA;AAC1C,QAAA,QACE,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC,sBAAsB;AAClD,YAAA,IAAI,KAAK,IAAI,CAAC,iBAAiB,EAC/B;KACH;IAEO,aAAa,CAAC,IAAY,EAAE,WAAwB,EAAA;AAC1D,QAAA,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;AAC9D,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACf,YAAA,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;AACnC,YAAA,IAAI,EAAE,wBAAwB;AAC9B,YAAA,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC;AAC/B,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;KAC1B;IAEO,aAAa,CAAC,IAAY,EAAE,WAAwB,EAAA;;;AAG1D,QAAA,IAAI,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE;AACpC,YAAA,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC;AAE9B,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;gBAC9B,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACtE,aAAA;AACF,SAAA;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACjC,QAAA,MAAM,SAAS,GAAG,WAAW,CAAC,aAAa,CAAC;QAC5C,MAAM,QAAQ,GAAG,SAAU,CAAC,WAAW,CAAC,aAAc,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;QAG3B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC5B,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC5D,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,MAAM,cAAc,GAAG,QAAQ,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAElE,QAAA,IAAI,CAAC,QAAQ,IAAI,cAAc,EAAE;YAC/B,IAAI,GAAG,GAAG,CAAA,yDAAA,CAA2D,CAAC;AACtE,YAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,GAAG,IAAI,sCAAsC,CAAC;AAC/C,aAAA;AAED,YAAA,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AACtB,SAAA;QAED,IAAI,WAAW,GAAG,QAAQ,CAAC;;AAE3B,QAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;;;YAGvB,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;AACzC,YAAA,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,SAAA;QAED,WAAW,CAAC,aAAc,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACf,YAAA,IAAI,EAAE,wBAAwB;AAC9B,YAAA,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC;AAC/B,SAAA,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;KAC5C;AAEO,IAAA,cAAc,CAAC,KAAa,EAAA;QAClC,MAAM,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1C,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;KAClD;AAED;;;;AAIG;AACK,IAAA,mBAAmB,CAAC,IAAY,EAAA;QACtC,IAAI,WAAW,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,KAAK,CAAC;AAEV,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;;AAE5B,YAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;;YAE7C,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;;AAE3C,YAAA,WAAW,GAAG,OAAO,GAAG,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;;AAE7D,YAAA,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACtE,SAAA;AAED,QAAA,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;KAC/B;IAEO,cAAc,CAAC,WAAwB,EAAE,GAAY,EAAA;QAC3D,MAAM,MAAM,GAAgB,EAAE,CAAC;AAC/B,QAAA,MAAM,MAAM,GAAG,CAAG,EAAA,GAAG,GAAG,CAAC;AAEzB,QAAA,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;AACpC,YAAA,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AACjC,gBAAA,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;AAClE,aAAA;AACF,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AAEO,IAAA,UAAU,CAAC,GAAmC,EAAA;AACpD,QAAA,OAAO,GAAG,YAAY,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KACjE;uGAtuBU,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAoBL,gBAAgB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAC5B,oBAAoB,EAAA,EAAA,EAAA,KAAA,EACpB,yBAAyB,EAAA,EAAA,EAAA,KAAA,EAEzB,qBAAqB,EAAA,EAAA,EAAA,KAAA,EACrB,gBAAgB,EAAA,EAAA,EAAA,KAAA,EAChB,2BAA2B,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AA1B1B,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADH,MAAM,EAAA,CAAA,CAAA;;2FACnB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAA;;0BAqB7B,QAAQ;;0BAAI,MAAM;2BAAC,gBAAgB,CAAA;;0BACnC,MAAM;2BAAC,oBAAoB,CAAA;;0BAC3B,MAAM;2BAAC,yBAAyB,CAAA;;0BAEhC,MAAM;2BAAC,qBAAqB,CAAA;;0BAC5B,MAAM;2BAAC,gBAAgB,CAAA;;0BACvB,MAAM;2BAAC,2BAA2B,CAAA;;;MChH1B,wBAAwB,CAAA;AAC1B,IAAA,IAAI,CAAqB;uGADvB,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAxB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,wBAAwB,EALzB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;AAET,EAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;2FAGU,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBANpC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAA;;AAET,EAAA,CAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAEU,IAAI,EAAA,CAAA;sBAAZ,KAAK;;;MCFK,eAAe,CAAA;AACN,IAAA,IAAA,CAAA;AAAuB,IAAA,GAAA,CAAA;IAA3C,WAAoB,CAAA,IAAa,EAAU,GAAqB,EAAA;QAA5C,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAS;QAAU,IAAG,CAAA,GAAA,GAAH,GAAG,CAAkB;KAAI;IAEpE,UAAU,GAAA;AACR,QAAA,IAAI,IAAI,CAAC,IAAI,YAAY,WAAW,EAAE;YACpC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxC,SAAA;AAAM,aAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;YACxE,YAAY,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvC,YAAA,YAAY,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;AACvC,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrC,SAAA;KACF;IAED,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;KAClB;AACF;;MCvBY,cAAc,GAAG,IAAI,cAAc,CAAS,gBAAgB;;MCE5D,0BAA0B,GAAG,IAAI,cAAc,CAC1D,4BAA4B;;MCDjB,eAAe,GAAG,IAAI,cAAc,CAC/C,iBAAiB;;MCGN,YAAY,CAAA;IACvB,WAAW,GAAG,KAAK,CAAC;;AAGpB,IAAA,OAAO,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAsB,EAAA;QACtD,IAAI,IAAI,GAAG,MAAM,CAAC;AAClB;;;AAGG;QACH,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,GAAG,MAAM,CAAC;AAEd,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AAED,QAAA,IAAI,QAAQ,EAAE;YACZ,MAAM,GAAG,SAAS,CAAC,GAAG,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACvD,IAAI,GAAG,SAAS,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,MAAM,EAAE;YACV,MAAM,GAAG,SAAS,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACrD,IAAI,GAAG,SAAS,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAExB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;;;;AASG;AACH,IAAA,uBAAuB,CAAC,IAAY,EAAA;AAClC,QAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAErC,QAAA,OAAO,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;KAC9C;AAED;;;;;;;;;AASG;IACH,eAAe,CAAC,IAAY,EAAE,KAAc,EAAA;AAC1C,QAAA,OAAO,KAAK,GAAG,CAAG,EAAA,KAAK,CAAI,CAAA,EAAA,IAAI,CAAE,CAAA,GAAG,IAAI,CAAC;KAC1C;AACF;;MC3DY,aAAa,CAAA;AACJ,IAAA,OAAA,CAAA;AAApB,IAAA,WAAA,CAAoB,OAAyB,EAAA;QAAzB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAkB;KAAI;;AAGjD,IAAA,OAAO,CAAC,MAA2B,EAAA;AACjC,QAAA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;AACpC,QAAA,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AAED,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;AAC3B,gBAAA,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,EAAE,GAAG,QAAyB,CAAC;gBACxE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAE1C,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AAED,YAAA,OAAO,QAAkB,CAAC;AAC3B,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KAClB;AACF;;MCSY,kBAAkB,CAAA;AACrB,IAAA,OAAO,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AACnC,IAAA,GAAG,GAAG,MAAM,CAA2B,WAAW,EAAE;AAC1D,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC,CAAC;IACK,YAAY,GAAG,MAAM,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1D,aAAa,GAAmC,MAAM,CAC5D,eAAe,EACf,EAAE,QAAQ,EAAE,IAAI,EAAE,CACnB,CAAC;AACM,IAAA,kBAAkB,GAAG,MAAM,CAAC,0BAA0B,EAAE;AAC9D,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC,CAAC;AACK,IAAA,GAAG,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAChC,IAAA,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AAC1B,IAAA,GAAG,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC/B,IAAA,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IAErC,YAAY,GAAwB,IAAI,CAAC;AACzC,IAAA,IAAI,CAA2C;IAEvC,eAAe,GACrB,EAAE,CAAC;AAEe,IAAA,GAAG,CAAqB;IAClB,MAAM,GAAY,EAAE,CAAC;AACtB,IAAA,WAAW,CAAqB;AACjC,IAAA,UAAU,CAAqB;AAC/B,IAAA,UAAU,CAAqB;AACzB,IAAA,SAAS,CAAsB;AAErD,IAAA,WAAW,CAAqB;AAChC,IAAA,gBAAgB,CAA8B;;IAE9C,WAAW,GAAG,KAAK,CAAC;AACpB,IAAA,IAAI,CAAqB;AACzB,IAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;IAClC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvC,IAAA,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,GAAG,WAAW,GAAG,YAAY,CAAC;AAE3E,IAAA,OAAO,sBAAsB,CAC3B,GAAuB,EACvB,GAAY,EAAA;AAEZ,QAAA,OAAO,IAAI,CAAC;KACb;IAED,QAAQ,GAAA;AACN,QAAA,MAAM,kBAAkB,GAAG,yBAAyB,CAClD,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CACrC,CAAC;AAEF,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;AAC1C,aAAA,IAAI,CACH,SAAS,CAAC,CAAC,UAAU,KAAI;AACvB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;gBACrC,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,QAAQ,EAAE,IAAI,CAAC,YAAY;AAC3B,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC,CAAC;AAEH,YAAA,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;kBACpC,QAAQ,CACN,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,aAAa,KACnC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,CAAC,CACvC,CACF;kBACD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAClD,SAAC,CAAC,EACF,mBAAmB,CAAC,kBAAkB,CAAC,CACxC;aACA,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAC1D,IAAI,CAAC,IAAK,CACX,CAAC;YACF,IAAI,CAAC,QAAQ,KAAK,WAAW;AAC3B,kBAAE,IAAI,CAAC,iBAAiB,EAAE;AAC1B,kBAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/D,YAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;AACxB,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B,SAAC,CAAC,CAAC;AAEL,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACpD,YAAA,IAAI,cAAc,EAAE;AAClB,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACtE,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;AACpC,aAAA;AACF,SAAA;KACF;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;;;AAGhC,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE;YACjC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AAC1E,YAAA,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACrC,SAAA;KACF;IAEO,iBAAiB,GAAA;QACvB,IAAI,CAAC,YAAY,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CACvB,IAAI,CAAC,IAAI,CAAC,aAAa,EACvB,WAAW,EACX,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CACjE,CAAC;KACH;IAEO,kBAAkB,CAAC,IAAY,EAAE,IAAa,EAAA;AACpD,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,IAAI,EAAE;;AAEb,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,WAAY,CAAC;AACtD,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,YAAY,EAAE,CAAC;AACpB,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAI,EAAE;gBACjD,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;gBAC1C,WAAW,EAAE,IAAI,CAAC,WAAY;AAC/B,aAAA,CAAC,CAAC;AACJ,SAAA;KACF;IAES,cAAc,CACtB,IAAY,EACZ,IAAwB,EAAA;AAExB,QAAA,OAAO,CAAC,GAAW,EAAE,MAAgB,KAAI;AACvC,YAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAA,EAAG,IAAI,CAAA,CAAA,EAAI,GAAG,CAAE,CAAA,GAAG,GAAG,CAAC;YAC/C,MAAM,UAAU,GAAG,MAAM;kBACrB,CAAG,EAAA,QAAQ,CAAG,EAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,CAAA;kBACtC,QAAQ,CAAC;AAEb,YAAA,IACE,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,EACtE;gBACA,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;AAC/C,aAAA;AAED,YAAA,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,GAAG;gBACjC,MAAM;AACN,gBAAA,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC;aACtD,CAAC;YAEF,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;AAChD,SAAC,CAAC;KACH;IAEO,qBAAqB,GAAA;AAC3B,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC;KAClD;IAED,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;;;AAGhC,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC1B,SAAA;KACF;IAEO,YAAY,GAAA;AAClB,QAAA,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC;KACrC;IAEO,YAAY,CAClB,IAAY,EACZ,aAAoC,EAAA;AAEpC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YAC/C,MAAM,EAAE,IAAI,CAAC,WAAW;AACxB,YAAA,QAAQ,EAAE,aAAa;AACxB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACnE,MAAM,YAAY,GAAG,mBAAmB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAEvE,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KAChE;uGApLU,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,CAAA,WAAA,EAAA,KAAA,CAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,QAAA,CAAA,EAAA,WAAA,EAAA,CAAA,gBAAA,EAAA,aAAA,CAAA,EAAA,UAAA,EAAA,CAAA,eAAA,EAAA,YAAA,CAAA,EAAA,UAAA,EAAA,CAAA,eAAA,EAAA,YAAA,CAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,EAAA,WAAA,CAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAyBqB,GAAG,EAAA,CAAA;sBAAtB,KAAK;uBAAC,WAAW,CAAA;gBACQ,MAAM,EAAA,CAAA;sBAA/B,KAAK;uBAAC,iBAAiB,CAAA;gBACC,WAAW,EAAA,CAAA;sBAAnC,KAAK;uBAAC,gBAAgB,CAAA;gBACC,UAAU,EAAA,CAAA;sBAAjC,KAAK;uBAAC,eAAe,CAAA;gBACE,UAAU,EAAA,CAAA;sBAAjC,KAAK;uBAAC,eAAe,CAAA;gBACQ,SAAS,EAAA,CAAA;sBAAtC,KAAK;uBAAC,qBAAqB,CAAA;;;MC3CjB,aAAa,CAAA;AASd,IAAA,OAAA,CAAA;AAGA,IAAA,aAAA,CAAA;AAGA,IAAA,YAAA,CAAA;AACA,IAAA,GAAA,CAAA;IAfF,YAAY,GAAwB,IAAI,CAAC;IACzC,SAAS,GAAG,EAAE,CAAC;AACf,IAAA,OAAO,CAAqB;AAC5B,IAAA,IAAI,CAAqB;AACzB,IAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAClC,IAAA,aAAa,CAAiB;AAEtC,IAAA,WAAA,CACU,OAAyB,EAGzB,aAAkD,EAGlD,YAAgC,EAChC,GAAsB,EAAA;QAPtB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAkB;QAGzB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAqC;QAGlD,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAoB;QAChC,IAAG,CAAA,GAAA,GAAH,GAAG,CAAmB;QAE9B,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACtD;;;AAID,IAAA,SAAS,CACP,GAAmB,EACnB,MAAgB,EAChB,UAAmB,EAAA;QAEnB,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,OAAO,GAAU,CAAC;AACnB,SAAA;QAED,MAAM,OAAO,GAAG,MAAM,GAAG,CAAA,EAAG,GAAG,CAAG,EAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,CAAE,GAAG,GAAG,CAAC;AAEjE,QAAA,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC;AACvB,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;AAEjC,QAAA,MAAM,kBAAkB,GAAG,yBAAyB,CAClD,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,YAAY,IAAI,UAAU,CAChC,CAAC;AAEF,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;AAC1C,aAAA,IAAI,CACH,SAAS,CAAC,CAAC,UAAU,KAAI;AACvB,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACrC,gBAAA,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,IAAI,CAAC,YAAY;AAC3B,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC,CAAC;AAEH,YAAA,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;kBACpC,QAAQ,CACN,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,aAAa,KACnC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,CAAC,CACvC,CACF;kBACD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAClD,SAAC,CAAC,EACF,mBAAmB,CAAC,kBAAkB,CAAC,CACxC;AACA,aAAA,SAAS,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QAElD,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;;;AAGjC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;KAC1B;IAEO,WAAW,CAAC,GAAW,EAAE,MAA4B,EAAA;AAC3D,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC3D,QAAA,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;KACzB;IAEO,YAAY,CAClB,IAAY,EACZ,aAAoC,EAAA;AAEpC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AAC/C,YAAA,MAAM,EAAE,SAAS;AACjB,YAAA,QAAQ,EAAE,aAAa;AACxB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACnE,MAAM,YAAY,GAAG,mBAAmB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAEvE,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;KAChE;uGA/FU,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,gBAAA,EAAA,EAAA,EAAA,KAAA,EAWd,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAGf,cAAc,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA;qGAdb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACJ,oBAAA,IAAI,EAAE,WAAW;AACjB,oBAAA,IAAI,EAAE,KAAK;AACX,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAWI,QAAQ;;0BACR,MAAM;2BAAC,eAAe,CAAA;;0BAEtB,QAAQ;;0BACR,MAAM;2BAAC,cAAc,CAAA;;;ACpC1B,MAAM,IAAI,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;MAMpC,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YANd,kBAAkB,EAAE,aAAa,CAAjC,EAAA,OAAA,EAAA,CAAA,kBAAkB,EAAE,aAAa,CAAA,EAAA,CAAA,CAAA;wGAMlC,eAAe,EAAA,CAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,IAAI;AACb,oBAAA,OAAO,EAAE,IAAI;AACd,iBAAA,CAAA;;;ACkCK,SAAU,gBAAgB,CAAC,OAAyB,EAAA;AACxD,IAAA,MAAM,SAAS,GAA2B;QACxC,0BAA0B,CAAC,iBAAiB,CAAC;QAC7C,8BAA8B,CAAC,cAAc,CAAC;QAC9C,2BAA2B,CAAC,kBAAkB,CAAC;QAC/C,gCAAgC,CAAC,uBAAuB,CAAC;KAC1D,CAAC;IAEF,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,KAAA;IAED,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,KAAA;AAED,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEK,SAAU,sBAAsB,CAAC,MAA8B,EAAA;AACnE,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,gBAAgB;AACzB,YAAA,QAAQ,EAAE,eAAe,CAAC,MAAM,CAAC;AAClC,SAAA;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,sBAAsB,CAAC,MAA6B,EAAA;AAClE,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA,EAAE,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE;AAChD,KAAA,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,qBAAqB,CAAC,KAAqB,EAAA;IACzD,OAAO;AACL,QAAA,OAAO,EAAE,eAAe;AACxB,QAAA,QAAQ,EAAE,KAAK;AACf,QAAA,KAAK,EAAE,IAAI;KACZ,CAAC;AACJ,CAAC;AAEK,SAAU,0BAA0B,CAAC,OAAgB,EAAA;IACzD,OAAO;AACL,QAAA,OAAO,EAAE,0BAA0B;AACnC,QAAA,QAAQ,EAAE,OAAO;KAClB,CAAC;AACJ,CAAC;AAEK,SAAU,0BAA0B,CACxC,UAAqC,EAAA;AAErC,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,oBAAoB;AAC7B,YAAA,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,CAAC,gBAAgB,CAAC;AACzB,SAAA;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,gCAAgC,CAC9C,QAAyC,EAAA;AAEzC,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,2BAA2B;AACpC,YAAA,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,CAAC,gBAAgB,CAAC;AACzB,SAAA;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,8BAA8B,CAC5C,OAAsC,EAAA;AAEtC,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,yBAAyB;AAClC,YAAA,QAAQ,EAAE,OAAO;AAClB,SAAA;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,2BAA2B,CACzC,WAAuC,EAAA;AAEvC,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,qBAAqB;AAC9B,YAAA,QAAQ,EAAE,WAAW;AACtB,SAAA;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,oBAAoB,CAAC,IAAY,EAAA;IAC/C,OAAO;AACL,QAAA,OAAO,EAAE,cAAc;AACvB,QAAA,QAAQ,EAAE,IAAI;KACf,CAAC;AACJ;;ACzHA,MAAM,oBAAoB,GAAG,IAAI,cAAc,CAC7C,oDAAoD,CACrD,CAAC;AACF,MAAM,sBAAsB,GAAG,IAAI,cAAc,CAC/C,0CAA0C,CAC3C,CAAC;MAGW,aAAa,CAAA;AAEgB,IAAA,KAAA,CAAA;AADxC,IAAA,WAAA,CACwC,KAA2B,EAAA;QAA3B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAsB;KAC/D;AAEJ,IAAA,cAAc,CAAC,IAAY,EAAA;QACzB,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;KAC7B;AAPU,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,kBAEd,oBAAoB,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAFnB,aAAa,EAAA,CAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,UAAU;;0BAGN,MAAM;2BAAC,oBAAoB,CAAA;;AAQ1B,SAAU,oBAAoB,CAClC,OAAyB,EACzB,KAA8B,GAAA,EAAE,EAChC,OAAgC,EAAA;AAEhC,IAAA,MAAM,eAAe,GAAG,MACtB,OAAO,CAAC,YAAY;AAClB,UAAE,OAAO,CAAC,GAAG,CACT,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,CACjE;AACH,UAAE,OAAO,CAAC,OAAO,EAAE,CAAC;AAExB,IAAA,OAAO,eAAe,CAAC;AACzB,CAAC;MAKY,sBAAsB,CAAA;IACjC,OAAO,OAAO,CACZ,OAAgC,EAAA;QAEhC,OAAO;AACL,YAAA,QAAQ,EAAE,sBAAsB;AAChC,YAAA,SAAS,EAAE;AACT,gBAAA,gBAAgB,CAAC;AACf,oBAAA,MAAM,EAAE,aAAa;AACrB,oBAAA,MAAM,EAAE;AACN,wBAAA,QAAQ,EAAE,IAAI;AACd,wBAAA,cAAc,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE;wBACxC,GAAG,OAAO,CAAC,eAAe;AAC3B,qBAAA;iBACF,CAAC;AACF,gBAAA;AACE,oBAAA,OAAO,EAAE,oBAAoB;oBAC7B,QAAQ,EAAE,OAAO,CAAC,KAAK;AACxB,iBAAA;AACD,gBAAA;AACE,oBAAA,OAAO,EAAE,sBAAsB;AAC/B,oBAAA,QAAQ,EAAE,OAAO;AAClB,iBAAA;AACD,gBAAA;AACE,oBAAA,OAAO,EAAE,eAAe;AACxB,oBAAA,UAAU,EAAE,oBAAoB;AAChC,oBAAA,IAAI,EAAE;wBACJ,gBAAgB;wBAChB,oBAAoB;wBACpB,sBAAsB;AACvB,qBAAA;AACD,oBAAA,KAAK,EAAE,IAAI;AACZ,iBAAA;AACF,aAAA;SACF,CAAC;KACH;uGAnCU,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,sBAAsB,YAFvB,eAAe,CAAA,EAAA,CAAA,CAAA;AAEd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,sBAAsB,YAFvB,eAAe,CAAA,EAAA,CAAA,CAAA;;2FAEd,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAHlC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,CAAC;AAC3B,iBAAA,CAAA;;;ACxDD;;AAEG;SACa,cAAc,GAAA;AAC5B,IAAA,IAAI,WAAW,GAAG,qBAAqB,EAAE,CAAC;AAC1C,IAAA,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,EAAE;AAChC,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IAED,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACnC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,KAAA;IAED,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACnC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,KAAA;AAED,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;AAEG;SACa,qBAAqB,GAAA;IACnC,IAAI,CAAC,SAAS,EAAE,EAAE;AAChB,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;AAED,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAEnC,OAAO,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC;AACxD;;ACjCA;;AAEG;;;;"}