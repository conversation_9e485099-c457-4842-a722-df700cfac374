{"name": "@ngneat/transloco", "version": "6.0.4", "description": "The internationalization (i18n) library for Angular", "schematics": "./schematics/src/collection.json", "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/ngneat/transloco/issues"}, "homepage": "https://ngneat.github.io/transloco/", "repository": {"type": "git", "url": "https://github.com/ngneat/transloco"}, "keywords": ["angular", "angular 2", "i18n", "translate", "angular translate", "angular i18n", "transloco"], "author": {"name": "<PERSON><PERSON><PERSON> Basal", "email": "<EMAIL>", "url": "https://netbasal.com"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/shaharkazaz"}, {"name": "<PERSON><PERSON>ded", "url": "https://github.com/itayod"}], "license": "MIT", "peerDependencies": {"@angular/core": ">=16.0.0"}, "dependencies": {"fs-extra": "^11.0.0", "glob": "^10.0.0", "tslib": "^2.2.0", "flat": "6.0.1", "@ngneat/transloco-utils": "^5.0.0", "replace-in-file": "^7.0.1", "ora": "^5.4.1", "lodash.kebabcase": "^4.1.1"}, "module": "fesm2022/ngneat-transloco.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/ngneat-transloco.mjs", "esm": "./esm2022/ngneat-transloco.mjs", "default": "./fesm2022/ngneat-transloco.mjs"}}, "sideEffects": false}