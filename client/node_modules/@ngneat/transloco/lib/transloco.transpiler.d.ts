import { InjectionToken, Injector } from '@angular/core';
import { HashMap, Translation } from './types';
import { TranslocoConfig } from './transloco.config';
import * as i0 from "@angular/core";
export declare const TRANSLOCO_TRANSPILER: InjectionToken<TranslocoTranspiler>;
export interface TranslocoTranspiler {
    transpile(value: any, params: HashMap, translation: Translation, key: string): any;
    onLangChanged?(lang: string): void;
}
export declare class DefaultTranspiler implements TranslocoTranspiler {
    protected interpolationMatcher: RegExp;
    constructor(config?: TranslocoConfig);
    transpile(value: any, params: HashMap | undefined, translation: Translation, key: string): any;
    /**
     *
     * @example
     *
     * const en = {
     *  a: {
     *    b: {
     *      c: "Hello {{ value }}"
     *    }
     *  }
     * }
     *
     * const params =  {
     *  "b.c": { value: "Transloco "}
     * }
     *
     * service.selectTranslate('a', params);
     *
     * // the first param will be the result of `en.a`.
     * // the second param will be `params`.
     * parser.transpile(value, params, {});
     *
     *
     */
    protected handleObject(value: any, params: HashMap | undefined, translation: Translation, key: string): any;
    protected handleArray(value: string[], params: HashMap | undefined, translation: Translation, key: string): any[];
    static ɵfac: i0.ɵɵFactoryDeclaration<DefaultTranspiler, [{ optional: true; }]>;
    static ɵprov: i0.ɵɵInjectableDeclaration<DefaultTranspiler>;
}
export interface TranslocoTranspilerFunction {
    transpile(...args: string[]): any;
}
export declare function getFunctionArgs(argsString: string): string[];
export declare class FunctionalTranspiler extends DefaultTranspiler implements TranslocoTranspiler {
    protected injector: Injector;
    transpile(value: any, params: HashMap | undefined, translation: Translation, key: string): any;
    static ɵfac: i0.ɵɵFactoryDeclaration<FunctionalTranspiler, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<FunctionalTranspiler>;
}
