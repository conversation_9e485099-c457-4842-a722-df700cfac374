import { ProviderScope, Translation } from './types';
export declare function getValue<T>(obj: T, path: keyof T): any;
export declare function setValue(obj: any, prop: string, val: any): any;
export declare function size(collection: any): number;
export declare function isEmpty(collection: any): boolean;
export declare function isFunction(val: unknown): val is CallableFunction;
export declare function isString(val: unknown): val is string;
export declare function isNumber(val: unknown): val is number;
export declare function isObject(item: unknown): boolean;
export declare function coerceArray<T>(value: T | T[]): T[];
export declare function coerceArray<T>(value: T | readonly T[]): readonly T[];
export declare function toCamelCase(str: string): string;
export declare function isBrowser(): boolean;
export declare function isNil(value: unknown): value is null | undefined;
export declare function isDefined(value: unknown): boolean;
export declare function toNumber(value: number | string): number | null;
export declare function isScopeObject(item: any): item is ProviderScope;
export declare function isScopeArray(item: any): item is ProviderScope[];
export declare function hasInlineLoader(item: any): item is ProviderScope;
export declare function unflatten(obj: Translation): Translation;
export declare function flatten(obj: Translation): Translation;
