import { InjectionToken } from '@angular/core';
import { Translation } from './types';
import * as i0 from "@angular/core";
export declare const TRANSLOCO_INTERCEPTOR: InjectionToken<TranslocoInterceptor>;
export interface TranslocoInterceptor {
    preSaveTranslation(translation: Translation, lang: string): Translation;
    preSaveTranslationKey(key: string, value: string, lang: string): string;
}
export declare class DefaultInterceptor implements TranslocoInterceptor {
    preSaveTranslation(translation: Translation): Translation;
    preSaveTranslationKey(_: string, value: string): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<DefaultInterceptor, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<DefaultInterceptor>;
}
