import { ModuleWithProviders } from '@angular/core';
import { Observable } from 'rxjs';
import { TranslocoLoader } from './transloco.loader';
import { HashMap, Translation } from './types';
import { TranslocoConfig } from './transloco.config';
import { TranslocoService } from './transloco.service';
import * as i0 from "@angular/core";
import * as i1 from "./transloco.module";
export interface TranslocoTestingOptions {
    translocoConfig?: Partial<TranslocoConfig>;
    preloadLangs?: boolean;
    langs?: HashMap<Translation>;
}
export declare class TestingLoader implements TranslocoLoader {
    private langs;
    constructor(langs: HashMap<Translation>);
    getTranslation(lang: string): Observable<Translation> | Promise<Translation>;
    static ɵfac: i0.ɵɵFactoryDeclaration<TestingLoader, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TestingLoader>;
}
export declare function initTranslocoService(service: TranslocoService, langs: HashMap<Translation> | undefined, options: TranslocoTestingOptions): () => Promise<void> | Promise<(Translation | undefined)[]>;
export declare class TranslocoTestingModule {
    static forRoot(options: TranslocoTestingOptions): ModuleWithProviders<TranslocoTestingModule>;
    static ɵfac: i0.ɵɵFactoryDeclaration<TranslocoTestingModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TranslocoTestingModule, never, never, [typeof i1.TranslocoModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TranslocoTestingModule>;
}
