import { InjectionToken } from '@angular/core';
import { Observable } from 'rxjs';
import { Translation } from './types';
export interface TranslocoLoader {
    getTranslation(lang: string, data?: TranslocoLoaderData): Observable<Translation> | Promise<Translation>;
}
export type TranslocoLoaderData = {
    scope: string;
};
export declare class DefaultLoader implements TranslocoLoader {
    private translations;
    constructor(translations: Map<string, Translation>);
    getTranslation(lang: string): Observable<Translation>;
}
export declare const TRANSLOCO_LOADER: InjectionToken<TranslocoLoader>;
