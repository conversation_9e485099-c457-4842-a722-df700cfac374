import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Observable } from 'rxjs';
import { TranslocoLoader } from './transloco.loader';
import { TranslocoTranspiler } from './transloco.transpiler';
import { AvailableLangs, HashMap, InlineLoader, LoadOptions, SetTranslationOptions, TranslateParams, Translation, TranslocoEvents, TranslocoScope } from './types';
import { TranslocoConfig } from './transloco.config';
import { TranslocoMissingHandler } from './transloco-missing-handler';
import { TranslocoInterceptor } from './transloco.interceptor';
import { TranslocoFallbackStrategy } from './transloco-fallback-strategy';
import * as i0 from "@angular/core";
export declare function translate<T = string>(key: TranslateParams, params?: HashMap, lang?: string): T;
export declare function translateObject<T>(key: TranslateParams, params?: HashMap, lang?: string): T | T[];
export declare class TranslocoService implements OnDestroy {
    private loader;
    private parser;
    private missingHandler;
    private interceptor;
    private fallbackStrategy;
    langChanges$: Observable<string>;
    private subscription;
    private translations;
    private cache;
    private firstFallbackLang;
    private defaultLang;
    private availableLangs;
    private isResolvedMissingOnce;
    private lang;
    private failedLangs;
    private events;
    events$: Observable<TranslocoEvents>;
    readonly config: TranslocoConfig & {
        scopeMapping?: HashMap<string>;
    };
    constructor(loader: TranslocoLoader, parser: TranslocoTranspiler, missingHandler: TranslocoMissingHandler, interceptor: TranslocoInterceptor, userConfig: TranslocoConfig, fallbackStrategy: TranslocoFallbackStrategy);
    getDefaultLang(): string;
    setDefaultLang(lang: string): void;
    getActiveLang(): string;
    setActiveLang(lang: string): this;
    setAvailableLangs(langs: AvailableLangs): void;
    /**
     * Gets the available languages.
     *
     * @returns
     * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`
     * depending on how the available languages are set in your module.
     */
    getAvailableLangs(): AvailableLangs;
    load(path: string, options?: LoadOptions): Observable<Translation>;
    /**
     * Gets the instant translated value of a key
     *
     * @example
     *
     * translate<string>('hello')
     * translate('hello', { value: 'value' })
     * translate<string[]>(['hello', 'key'])
     * translate('hello', { }, 'en')
     * translate('scope.someKey', { }, 'en')
     */
    translate<T = string>(key: TranslateParams, params?: HashMap, lang?: string): T;
    /**
     * Gets the translated value of a key as observable
     *
     * @example
     *
     * selectTranslate<string>('hello').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)
     *
     */
    selectTranslate<T = any>(key: TranslateParams, params?: HashMap, lang?: string | TranslocoScope | TranslocoScope[], _isObject?: boolean): Observable<T>;
    /**
     * Whether the scope with lang
     *
     * @example
     *
     * todos/en => true
     * todos => false
     */
    private isScopeWithLang;
    /**
     * Translate the given path that returns an object
     *
     * @example
     *
     * service.translateObject('path.to.object', {'subpath': { value: 'someValue'}}) => returns translated object
     *
     */
    translateObject<T = any>(key: string, params?: HashMap, lang?: string): T;
    translateObject<T = any>(key: string[], params?: HashMap, lang?: string): T[];
    translateObject<T = any>(key: TranslateParams, params?: HashMap, lang?: string): T | T[];
    translateObject<T = any>(key: HashMap | Map<string, HashMap>, params?: null, lang?: string): T[];
    selectTranslateObject<T = any>(key: string, params?: HashMap, lang?: string): Observable<T>;
    selectTranslateObject<T = any>(key: string[], params?: HashMap, lang?: string): Observable<T[]>;
    selectTranslateObject<T = any>(key: TranslateParams, params?: HashMap, lang?: string): Observable<T> | Observable<T[]>;
    selectTranslateObject<T = any>(key: HashMap | Map<string, HashMap>, params?: null, lang?: string): Observable<T[]>;
    /**
     * Gets an object of translations for a given language
     *
     * @example
     *
     * getTranslation()
     * getTranslation('en')
     * getTranslation('admin-page/en')
     */
    getTranslation(): Map<string, Translation>;
    getTranslation(langOrScope: string): Translation;
    /**
     * Gets an object of translations for a given language
     *
     * @example
     *
     * selectTranslation().subscribe() - will return the current lang translation
     * selectTranslation('es').subscribe()
     * selectTranslation('admin-page').subscribe() - will return the current lang scope translation
     * selectTranslation('admin-page/es').subscribe()
     */
    selectTranslation(lang?: string): Observable<Translation>;
    /**
     * Sets or merge a given translation object to current lang
     *
     * @example
     *
     * setTranslation({ ... })
     * setTranslation({ ... }, 'en')
     * setTranslation({ ... }, 'es', { merge: false } )
     * setTranslation({ ... }, 'todos/en', { merge: false } )
     */
    setTranslation(translation: Translation, lang?: string, options?: SetTranslationOptions): void;
    /**
     * Sets translation key with given value
     *
     * @example
     *
     * setTranslationKey('key', 'value')
     * setTranslationKey('key.nested', 'value')
     * setTranslationKey('key.nested', 'value', 'en')
     * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )
     */
    setTranslationKey(key: string, value: string, lang?: string, options?: Omit<SetTranslationOptions, 'merge'>): void;
    /**
     * Sets the fallback lang for the currently active language
     * @param fallbackLang
     */
    setFallbackLangForMissingTranslation({ fallbackLang, }: Pick<TranslocoConfig, 'fallbackLang'>): void;
    /**
     * @internal
     */
    _handleMissingKey(key: string, value: any, params?: HashMap): any;
    /**
     * @internal
     */
    _isLangScoped(lang: string): boolean;
    /**
     * Checks if a given string is one of the specified available languages.
     * @returns
     * True if the given string is an available language.
     * False if the given string is not an available language.
     */
    isLang(lang: string): boolean;
    /**
     * @internal
     *
     * We always want to make sure the global lang is loaded
     * before loading the scope since you can access both via the pipe/directive.
     */
    _loadDependencies(path: string, inlineLoader?: InlineLoader): Observable<Translation | Translation[]>;
    /**
     * @internal
     */
    _completeScopeWithLang(langOrScope: string): string;
    /**
     * @internal
     */
    _setScopeAlias(scope: string, alias: string): void;
    ngOnDestroy(): void;
    private isLoadedTranslation;
    private getAvailableLangsIds;
    private getMissingHandlerData;
    /**
     * Use a fallback translation set for missing keys of the primary language
     * This is unrelated to the fallback language (which changes the active language)
     */
    private useFallbackTranslation;
    private handleSuccess;
    private handleFailure;
    private getMappedScope;
    /**
     * If lang is scope we need to check the following cases:
     * todos/es => in this case we should take `es` as lang
     * todos => in this case we should set the active lang as lang
     */
    private resolveLangAndScope;
    private getObjectByKey;
    private getEntries;
    static ɵfac: i0.ɵɵFactoryDeclaration<TranslocoService, [{ optional: true; }, null, null, null, null, null]>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TranslocoService>;
}
