import { EnvironmentProviders, Provider, Type } from '@angular/core';
import { TranslocoLoader } from './transloco.loader';
import { PartialTranslocoConfig } from './transloco.config';
import { TranslocoScope } from './types';
import { TranslocoTranspiler } from './transloco.transpiler';
import { TranslocoMissingHandler } from './transloco-missing-handler';
import { TranslocoInterceptor } from './transloco.interceptor';
import { TranslocoFallbackStrategy } from './transloco-fallback-strategy';
import { Content } from './template-handler';
export interface TranslocoOptions {
    config: PartialTranslocoConfig;
    loader?: Type<TranslocoLoader>;
}
export declare function provideTransloco(options: TranslocoOptions): EnvironmentProviders[];
export declare function provideTranslocoConfig(config: PartialTranslocoConfig): EnvironmentProviders;
export declare function provideTranslocoLoader(loader: Type<TranslocoLoader>): EnvironmentProviders;
export declare function provideTranslocoScope(scope: TranslocoScope): {
    provide: import("@angular/core").InjectionToken<TranslocoScope>;
    useValue: TranslocoScope;
    multi: boolean;
};
export declare function provideTranslocoLoadingTpl(content: Content): {
    provide: import("@angular/core").InjectionToken<Content>;
    useValue: Content;
};
export declare function provideTranslocoTranspiler(transpiler: Type<TranslocoTranspiler>): EnvironmentProviders;
export declare function provideTranslocoFallbackStrategy(strategy: Type<TranslocoFallbackStrategy>): EnvironmentProviders;
export declare function provideTranslocoMissingHandler(handler: Type<TranslocoMissingHandler>): EnvironmentProviders;
export declare function provideTranslocoInterceptor(interceptor: Type<TranslocoInterceptor>): EnvironmentProviders;
export declare function provideTranslocoLang(lang: string): Provider;
