import { TranslocoLoader, TranslocoLoaderData } from './transloco.loader';
import { InlineLoader } from './types';
interface Options {
    path: string;
    fallbackPath?: string;
    inlineLoader?: InlineLoader;
    mainLoader: TranslocoLoader;
    data?: TranslocoLoaderData;
}
export declare function getFallbacksLoaders({ mainLoader, path, data, fallbackPath, inlineLoader, }: Options): import("rxjs").Observable<{
    translation: any;
    lang: string;
}>[];
export {};
