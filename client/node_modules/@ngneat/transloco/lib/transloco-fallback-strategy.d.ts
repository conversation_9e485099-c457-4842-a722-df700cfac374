import { InjectionToken } from '@angular/core';
import { TranslocoConfig } from './transloco.config';
import * as i0 from "@angular/core";
export declare const TRANSLOCO_FALLBACK_STRATEGY: InjectionToken<TranslocoFallbackStrategy>;
export interface TranslocoFallbackStrategy {
    getNextLangs(failedLang: string): string[];
}
export declare class DefaultFallbackStrategy implements TranslocoFallbackStrategy {
    private userConfig;
    constructor(userConfig: TranslocoConfig);
    getNextLangs(): string[];
    static ɵfac: i0.ɵɵFactoryDeclaration<DefaultFallbackStrategy, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<DefaultFallbackStrategy>;
}
