import { ChangeDetectorRef, OnDestroy, PipeTransform } from '@angular/core';
import { TranslocoService } from './transloco.service';
import { HashMap, OrArray, TranslocoScope } from './types';
import * as i0 from "@angular/core";
export declare class TranslocoPipe implements PipeTransform, OnDestroy {
    private service;
    private providerScope;
    private providerLang;
    private cdr;
    private subscription;
    private lastValue;
    private lastKey;
    private path;
    private langResolver;
    private scopeResolver;
    constructor(service: TranslocoService, providerScope: OrArray<TranslocoScope> | undefined, providerLang: string | undefined, cdr: ChangeDetectorRef);
    transform(key?: string | null, params?: HashMap, inlineLang?: string): string;
    ngOnDestroy(): void;
    private updateValue;
    private resolveScope;
    static ɵfac: i0.ɵɵFactoryDeclaration<TranslocoPipe, [null, { optional: true; }, { optional: true; }, null]>;
    static ɵpipe: i0.ɵɵPipeDeclaration<TranslocoPipe, "transloco", true>;
}
