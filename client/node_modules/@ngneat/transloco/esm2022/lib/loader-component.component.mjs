import { Component, Input } from '@angular/core';
import * as i0 from "@angular/core";
export class TranslocoLoaderComponent {
    html;
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoLoaderComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });
    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "14.0.0", version: "16.1.7", type: TranslocoLoaderComponent, isStandalone: true, selector: "ng-component", inputs: { html: "html" }, ngImport: i0, template: `
    <div class="transloco-loader-template" [innerHTML]="html"></div>
  `, isInline: true });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoLoaderComponent, decorators: [{
            type: Component,
            args: [{
                    template: `
    <div class="transloco-loader-template" [innerHTML]="html"></div>
  `,
                    standalone: true,
                }]
        }], propDecorators: { html: [{
                type: Input
            }] } });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibG9hZGVyLWNvbXBvbmVudC5jb21wb25lbnQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9saWJzL3RyYW5zbG9jby9zcmMvbGliL2xvYWRlci1jb21wb25lbnQuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE9BQU8sRUFBRSxTQUFTLEVBQUUsS0FBSyxFQUFFLE1BQU0sZUFBZSxDQUFDOztBQVFqRCxNQUFNLE9BQU8sd0JBQXdCO0lBQzFCLElBQUksQ0FBcUI7dUdBRHZCLHdCQUF3QjsyRkFBeEIsd0JBQXdCLGtHQUx6Qjs7R0FFVDs7MkZBR1Usd0JBQXdCO2tCQU5wQyxTQUFTO21CQUFDO29CQUNULFFBQVEsRUFBRTs7R0FFVDtvQkFDRCxVQUFVLEVBQUUsSUFBSTtpQkFDakI7OEJBRVUsSUFBSTtzQkFBWixLQUFLIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ29tcG9uZW50LCBJbnB1dCB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xuXG5AQ29tcG9uZW50KHtcbiAgdGVtcGxhdGU6IGBcbiAgICA8ZGl2IGNsYXNzPVwidHJhbnNsb2NvLWxvYWRlci10ZW1wbGF0ZVwiIFtpbm5lckhUTUxdPVwiaHRtbFwiPjwvZGl2PlxuICBgLFxuICBzdGFuZGFsb25lOiB0cnVlLFxufSlcbmV4cG9ydCBjbGFzcyBUcmFuc2xvY29Mb2FkZXJDb21wb25lbnQge1xuICBASW5wdXQoKSBodG1sOiBzdHJpbmcgfCB1bmRlZmluZWQ7XG59XG4iXX0=