import { isScopeObject, toCamelCase } from './helpers';
export class ScopeResolver {
    service;
    constructor(service) {
        this.service = service;
    }
    // inline => provider
    resolve(params) {
        const { inline, provider } = params;
        if (inline) {
            return inline;
        }
        if (provider) {
            if (isScopeObject(provider)) {
                const { scope, alias = toCamelCase(scope) } = provider;
                this.service._setScopeAlias(scope, alias);
                return scope;
            }
            return provider;
        }
        return undefined;
    }
}
//# sourceMappingURL=data:application/json;base64,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