import { Inject, Injectable, Optional } from '@angular/core';
import { BehaviorSubject, catchError, combineLatest, EMPTY, forkJoin, from, map, of, retry, shareReplay, Subject, switchMap, tap, } from 'rxjs';
import { DefaultLoader, TRANSLOCO_LOADER, } from './transloco.loader';
import { TRANSLOCO_TRANSPILER, } from './transloco.transpiler';
import { flatten, isEmpty, isNil, isScopeArray, isScopeObject, isString, size, toCamelCase, unflatten, } from './helpers';
import { TRANSLOCO_CONFIG } from './transloco.config';
import { TRANSLOCO_MISSING_HANDLER, } from './transloco-missing-handler';
import { TRANSLOCO_INTERCEPTOR, } from './transloco.interceptor';
import { TRANSLOCO_FALLBACK_STRATEGY, } from './transloco-fallback-strategy';
import { getEventPayload, getLangFromScope, getScopeFromLang, resolveInlineLoader, } from './shared';
import { getFallbacksLoaders } from './get-fallbacks-loaders';
import { resolveLoader } from './resolve-loader';
import * as i0 from "@angular/core";
let service;
export function translate(key, params = {}, lang) {
    return service.translate(key, params, lang);
}
export function translateObject(key, params = {}, lang) {
    return service.translateObject(key, params, lang);
}
export class TranslocoService {
    loader;
    parser;
    missingHandler;
    interceptor;
    fallbackStrategy;
    langChanges$;
    subscription = null;
    translations = new Map();
    cache = new Map();
    firstFallbackLang;
    defaultLang = '';
    availableLangs = [];
    isResolvedMissingOnce = false;
    lang;
    failedLangs = new Set();
    events = new Subject();
    events$ = this.events.asObservable();
    config;
    constructor(loader, parser, missingHandler, interceptor, userConfig, fallbackStrategy) {
        this.loader = loader;
        this.parser = parser;
        this.missingHandler = missingHandler;
        this.interceptor = interceptor;
        this.fallbackStrategy = fallbackStrategy;
        if (!this.loader) {
            this.loader = new DefaultLoader(this.translations);
        }
        service = this;
        this.config = JSON.parse(JSON.stringify(userConfig));
        this.setAvailableLangs(this.config.availableLangs || []);
        this.setFallbackLangForMissingTranslation(this.config);
        this.setDefaultLang(this.config.defaultLang);
        this.lang = new BehaviorSubject(this.getDefaultLang());
        // Don't use distinctUntilChanged as we need the ability to update
        // the value when using setTranslation or setTranslationKeys
        this.langChanges$ = this.lang.asObservable();
        /**
         * When we have a failure, we want to define the next language that succeeded as the active
         */
        this.subscription = this.events$.subscribe((e) => {
            if (e.type === 'translationLoadSuccess' && e.wasFailure) {
                this.setActiveLang(e.payload.langName);
            }
        });
    }
    getDefaultLang() {
        return this.defaultLang;
    }
    setDefaultLang(lang) {
        this.defaultLang = lang;
    }
    getActiveLang() {
        return this.lang.getValue();
    }
    setActiveLang(lang) {
        this.parser.onLangChanged?.(lang);
        this.lang.next(lang);
        this.events.next({
            type: 'langChanged',
            payload: getEventPayload(lang),
        });
        return this;
    }
    setAvailableLangs(langs) {
        this.availableLangs = langs;
    }
    /**
     * Gets the available languages.
     *
     * @returns
     * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`
     * depending on how the available languages are set in your module.
     */
    getAvailableLangs() {
        return this.availableLangs;
    }
    load(path, options = {}) {
        const cached = this.cache.get(path);
        if (cached) {
            return cached;
        }
        let loadTranslation;
        const isScope = this._isLangScoped(path);
        let scope;
        if (isScope) {
            scope = getScopeFromLang(path);
        }
        const loadersOptions = {
            path,
            mainLoader: this.loader,
            inlineLoader: options.inlineLoader,
            data: isScope ? { scope: scope } : undefined,
        };
        if (this.useFallbackTranslation(path)) {
            // if the path is scope the fallback should be `scope/fallbackLang`;
            const fallback = isScope
                ? `${scope}/${this.firstFallbackLang}`
                : this.firstFallbackLang;
            const loaders = getFallbacksLoaders({
                ...loadersOptions,
                fallbackPath: fallback,
            });
            loadTranslation = forkJoin(loaders);
        }
        else {
            const loader = resolveLoader(loadersOptions);
            loadTranslation = from(loader);
        }
        const load$ = loadTranslation.pipe(retry(this.config.failedRetries), tap((translation) => {
            if (Array.isArray(translation)) {
                translation.forEach((t) => {
                    this.handleSuccess(t.lang, t.translation);
                    // Save the fallback in cache so we'll not create a redundant request
                    if (t.lang !== path) {
                        this.cache.set(t.lang, of({}));
                    }
                });
                return;
            }
            this.handleSuccess(path, translation);
        }), catchError((error) => {
            if (!this.config.prodMode) {
                console.error(`Error while trying to load "${path}"`, error);
            }
            return this.handleFailure(path, options);
        }), shareReplay(1));
        this.cache.set(path, load$);
        return load$;
    }
    /**
     * Gets the instant translated value of a key
     *
     * @example
     *
     * translate<string>('hello')
     * translate('hello', { value: 'value' })
     * translate<string[]>(['hello', 'key'])
     * translate('hello', { }, 'en')
     * translate('scope.someKey', { }, 'en')
     */
    translate(key, params = {}, lang = this.getActiveLang()) {
        if (!key)
            return key;
        const { scope, resolveLang } = this.resolveLangAndScope(lang);
        if (Array.isArray(key)) {
            return key.map((k) => this.translate(scope ? `${scope}.${k}` : k, params, resolveLang));
        }
        key = scope ? `${scope}.${key}` : key;
        const translation = this.getTranslation(resolveLang);
        const value = translation[key];
        if (!value) {
            return this._handleMissingKey(key, value, params);
        }
        return this.parser.transpile(value, params, translation, key);
    }
    /**
     * Gets the translated value of a key as observable
     *
     * @example
     *
     * selectTranslate<string>('hello').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)
     *
     */
    selectTranslate(key, params, lang, _isObject = false) {
        let inlineLoader;
        const load = (lang, options) => this.load(lang, options).pipe(map(() => _isObject
            ? this.translateObject(key, params, lang)
            : this.translate(key, params, lang)));
        if (isNil(lang)) {
            return this.langChanges$.pipe(switchMap((lang) => load(lang)));
        }
        if (isScopeArray(lang) || isScopeObject(lang)) {
            // it's a scope object.
            const providerScope = Array.isArray(lang) ? lang[0] : lang;
            lang = providerScope.scope;
            inlineLoader = resolveInlineLoader(providerScope, providerScope.scope);
        }
        lang = lang;
        if (this.isLang(lang) || this.isScopeWithLang(lang)) {
            return load(lang);
        }
        // it's a scope
        const scope = lang;
        return this.langChanges$.pipe(switchMap((lang) => load(`${scope}/${lang}`, { inlineLoader })));
    }
    /**
     * Whether the scope with lang
     *
     * @example
     *
     * todos/en => true
     * todos => false
     */
    isScopeWithLang(lang) {
        return this.isLang(getLangFromScope(lang));
    }
    translateObject(key, params = {}, lang = this.getActiveLang()) {
        if (isString(key) || Array.isArray(key)) {
            const { resolveLang, scope } = this.resolveLangAndScope(lang);
            if (Array.isArray(key)) {
                return key.map((k) => this.translateObject(scope ? `${scope}.${k}` : k, params, resolveLang));
            }
            const translation = this.getTranslation(resolveLang);
            key = scope ? `${scope}.${key}` : key;
            const value = unflatten(this.getObjectByKey(translation, key));
            /* If an empty object was returned we want to try and translate the key as a string and not an object */
            return isEmpty(value)
                ? this.translate(key, params, lang)
                : this.parser.transpile(value, params, translation, key);
        }
        const translations = [];
        for (const [_key, _params] of this.getEntries(key)) {
            translations.push(this.translateObject(_key, _params, lang));
        }
        return translations;
    }
    selectTranslateObject(key, params, lang) {
        if (isString(key) || Array.isArray(key)) {
            return this.selectTranslate(key, params, lang, true);
        }
        const [[firstKey, firstParams], ...rest] = this.getEntries(key);
        /* In order to avoid subscribing multiple times to the load language event by calling selectTranslateObject for each pair,
         * we listen to when the first key has been translated (the language is loaded) and translate the rest synchronously */
        return this.selectTranslateObject(firstKey, firstParams, lang).pipe(map((value) => {
            const translations = [value];
            for (const [_key, _params] of rest) {
                translations.push(this.translateObject(_key, _params, lang));
            }
            return translations;
        }));
    }
    getTranslation(langOrScope) {
        if (langOrScope) {
            if (this.isLang(langOrScope)) {
                return this.translations.get(langOrScope) || {};
            }
            else {
                // This is a scope, build the scope value from the translation object
                const { scope, resolveLang } = this.resolveLangAndScope(langOrScope);
                const translation = this.translations.get(resolveLang) || {};
                return this.getObjectByKey(translation, scope);
            }
        }
        return this.translations;
    }
    /**
     * Gets an object of translations for a given language
     *
     * @example
     *
     * selectTranslation().subscribe() - will return the current lang translation
     * selectTranslation('es').subscribe()
     * selectTranslation('admin-page').subscribe() - will return the current lang scope translation
     * selectTranslation('admin-page/es').subscribe()
     */
    selectTranslation(lang) {
        let language$ = this.langChanges$;
        if (lang) {
            const scopeLangSpecified = getLangFromScope(lang) !== lang;
            if (this.isLang(lang) || scopeLangSpecified) {
                language$ = of(lang);
            }
            else {
                language$ = this.langChanges$.pipe(map((currentLang) => `${lang}/${currentLang}`));
            }
        }
        return language$.pipe(switchMap((language) => this.load(language).pipe(map(() => this.getTranslation(language)))));
    }
    /**
     * Sets or merge a given translation object to current lang
     *
     * @example
     *
     * setTranslation({ ... })
     * setTranslation({ ... }, 'en')
     * setTranslation({ ... }, 'es', { merge: false } )
     * setTranslation({ ... }, 'todos/en', { merge: false } )
     */
    setTranslation(translation, lang = this.getActiveLang(), options = {}) {
        const defaults = { merge: true, emitChange: true };
        const mergedOptions = { ...defaults, ...options };
        const scope = getScopeFromLang(lang);
        /**
         * If this isn't a scope we use the whole translation as is
         * otherwise we need to flat the scope and use it
         */
        let flattenScopeOrTranslation = translation;
        // Merged the scoped language into the active language
        if (scope) {
            const key = this.getMappedScope(scope);
            flattenScopeOrTranslation = flatten({ [key]: translation });
        }
        const currentLang = scope ? getLangFromScope(lang) : lang;
        const mergedTranslation = {
            ...(mergedOptions.merge && this.getTranslation(currentLang)),
            ...flattenScopeOrTranslation,
        };
        const flattenTranslation = this.config.flatten.aot
            ? mergedTranslation
            : flatten(mergedTranslation);
        const withHook = this.interceptor.preSaveTranslation(flattenTranslation, currentLang);
        this.translations.set(currentLang, withHook);
        mergedOptions.emitChange && this.setActiveLang(this.getActiveLang());
    }
    /**
     * Sets translation key with given value
     *
     * @example
     *
     * setTranslationKey('key', 'value')
     * setTranslationKey('key.nested', 'value')
     * setTranslationKey('key.nested', 'value', 'en')
     * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )
     */
    setTranslationKey(key, value, 
    // Todo: Add the lang to the options in v3
    lang = this.getActiveLang(), options = {}) {
        const withHook = this.interceptor.preSaveTranslationKey(key, value, lang);
        const newValue = {
            [key]: withHook,
        };
        this.setTranslation(newValue, lang, { ...options, merge: true });
    }
    /**
     * Sets the fallback lang for the currently active language
     * @param fallbackLang
     */
    setFallbackLangForMissingTranslation({ fallbackLang, }) {
        const lang = Array.isArray(fallbackLang) ? fallbackLang[0] : fallbackLang;
        if (fallbackLang && this.useFallbackTranslation(lang)) {
            this.firstFallbackLang = lang;
        }
    }
    /**
     * @internal
     */
    _handleMissingKey(key, value, params) {
        if (this.config.missingHandler.allowEmpty && value === '') {
            return '';
        }
        if (!this.isResolvedMissingOnce && this.useFallbackTranslation()) {
            // We need to set it to true to prevent a loop
            this.isResolvedMissingOnce = true;
            const fallbackValue = this.translate(key, params, this.firstFallbackLang);
            this.isResolvedMissingOnce = false;
            return fallbackValue;
        }
        return this.missingHandler.handle(key, this.getMissingHandlerData(), params);
    }
    /**
     * @internal
     */
    _isLangScoped(lang) {
        return this.getAvailableLangsIds().indexOf(lang) === -1;
    }
    /**
     * Checks if a given string is one of the specified available languages.
     * @returns
     * True if the given string is an available language.
     * False if the given string is not an available language.
     */
    isLang(lang) {
        return this.getAvailableLangsIds().indexOf(lang) !== -1;
    }
    /**
     * @internal
     *
     * We always want to make sure the global lang is loaded
     * before loading the scope since you can access both via the pipe/directive.
     */
    _loadDependencies(path, inlineLoader) {
        const mainLang = getLangFromScope(path);
        if (this._isLangScoped(path) && !this.isLoadedTranslation(mainLang)) {
            return combineLatest([
                this.load(mainLang),
                this.load(path, { inlineLoader }),
            ]);
        }
        return this.load(path, { inlineLoader });
    }
    /**
     * @internal
     */
    _completeScopeWithLang(langOrScope) {
        if (this._isLangScoped(langOrScope) &&
            !this.isLang(getLangFromScope(langOrScope))) {
            return `${langOrScope}/${this.getActiveLang()}`;
        }
        return langOrScope;
    }
    /**
     * @internal
     */
    _setScopeAlias(scope, alias) {
        if (!this.config.scopeMapping) {
            this.config.scopeMapping = {};
        }
        this.config.scopeMapping[scope] = alias;
    }
    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
            // Caretaker note: it's important to clean up references to subscriptions since they save the `next`
            // callback within its `destination` property, preventing classes from being GC'd.
            this.subscription = null;
        }
        // Caretaker note: since this is the root provider, it'll be destroyed when the `NgModuleRef.destroy()` is run.
        // Cached values capture `this`, thus leading to a circular reference and preventing the `TranslocoService` from
        // being GC'd. This would lead to a memory leak when server-side rendering is used since the service is created
        // and destroyed per each HTTP request, but any service is not getting GC'd.
        this.cache.clear();
    }
    isLoadedTranslation(lang) {
        return size(this.getTranslation(lang));
    }
    getAvailableLangsIds() {
        const first = this.getAvailableLangs()[0];
        if (isString(first)) {
            return this.getAvailableLangs();
        }
        return this.getAvailableLangs().map((l) => l.id);
    }
    getMissingHandlerData() {
        return {
            ...this.config,
            activeLang: this.getActiveLang(),
            availableLangs: this.availableLangs,
            defaultLang: this.defaultLang,
        };
    }
    /**
     * Use a fallback translation set for missing keys of the primary language
     * This is unrelated to the fallback language (which changes the active language)
     */
    useFallbackTranslation(lang) {
        return (this.config.missingHandler.useFallbackTranslation &&
            lang !== this.firstFallbackLang);
    }
    handleSuccess(lang, translation) {
        this.setTranslation(translation, lang, { emitChange: false });
        this.events.next({
            wasFailure: !!this.failedLangs.size,
            type: 'translationLoadSuccess',
            payload: getEventPayload(lang),
        });
        this.failedLangs.forEach((l) => this.cache.delete(l));
        this.failedLangs.clear();
    }
    handleFailure(lang, loadOptions) {
        // When starting to load a first choice language, initialize
        // the failed counter and resolve the fallback langs.
        if (isNil(loadOptions.failedCounter)) {
            loadOptions.failedCounter = 0;
            if (!loadOptions.fallbackLangs) {
                loadOptions.fallbackLangs = this.fallbackStrategy.getNextLangs(lang);
            }
        }
        const splitted = lang.split('/');
        const fallbacks = loadOptions.fallbackLangs;
        const nextLang = fallbacks[loadOptions.failedCounter];
        this.failedLangs.add(lang);
        // This handles the case where a loaded fallback language is requested again
        if (this.cache.has(nextLang)) {
            this.handleSuccess(nextLang, this.getTranslation(nextLang));
            return EMPTY;
        }
        const isFallbackLang = nextLang === splitted[splitted.length - 1];
        if (!nextLang || isFallbackLang) {
            let msg = `Unable to load translation and all the fallback languages`;
            if (splitted.length > 1) {
                msg += `, did you misspelled the scope name?`;
            }
            throw new Error(msg);
        }
        let resolveLang = nextLang;
        // if it's scoped lang
        if (splitted.length > 1) {
            // We need to resolve it to:
            // todos/langNotExists => todos/nextLang
            splitted[splitted.length - 1] = nextLang;
            resolveLang = splitted.join('/');
        }
        loadOptions.failedCounter++;
        this.events.next({
            type: 'translationLoadFailure',
            payload: getEventPayload(lang),
        });
        return this.load(resolveLang, loadOptions);
    }
    getMappedScope(scope) {
        const { scopeMapping = {} } = this.config;
        return scopeMapping[scope] || toCamelCase(scope);
    }
    /**
     * If lang is scope we need to check the following cases:
     * todos/es => in this case we should take `es` as lang
     * todos => in this case we should set the active lang as lang
     */
    resolveLangAndScope(lang) {
        let resolveLang = lang;
        let scope;
        if (this._isLangScoped(lang)) {
            // en for example
            const langFromScope = getLangFromScope(lang);
            // en is lang
            const hasLang = this.isLang(langFromScope);
            // take en
            resolveLang = hasLang ? langFromScope : this.getActiveLang();
            // find the scope
            scope = this.getMappedScope(hasLang ? getScopeFromLang(lang) : lang);
        }
        return { scope, resolveLang };
    }
    getObjectByKey(translation, key) {
        const result = {};
        const prefix = `${key}.`;
        for (const currentKey in translation) {
            if (currentKey.startsWith(prefix)) {
                result[currentKey.replace(prefix, '')] = translation[currentKey];
            }
        }
        return result;
    }
    getEntries(key) {
        return key instanceof Map ? key.entries() : Object.entries(key);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoService, deps: [{ token: TRANSLOCO_LOADER, optional: true }, { token: TRANSLOCO_TRANSPILER }, { token: TRANSLOCO_MISSING_HANDLER }, { token: TRANSLOCO_INTERCEPTOR }, { token: TRANSLOCO_CONFIG }, { token: TRANSLOCO_FALLBACK_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoService, providedIn: 'root' });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoService, decorators: [{
            type: Injectable,
            args: [{ providedIn: 'root' }]
        }], ctorParameters: function () { return [{ type: undefined, decorators: [{
                    type: Optional
                }, {
                    type: Inject,
                    args: [TRANSLOCO_LOADER]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_TRANSPILER]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_MISSING_HANDLER]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_INTERCEPTOR]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_CONFIG]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_FALLBACK_STRATEGY]
                }] }]; } });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHJhbnNsb2NvLnNlcnZpY2UuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9saWJzL3RyYW5zbG9jby9zcmMvbGliL3RyYW5zbG9jby5zZXJ2aWNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE9BQU8sRUFBRSxNQUFNLEVBQUUsVUFBVSxFQUFhLFFBQVEsRUFBRSxNQUFNLGVBQWUsQ0FBQztBQUN4RSxPQUFPLEVBQ0wsZUFBZSxFQUNmLFVBQVUsRUFDVixhQUFhLEVBQ2IsS0FBSyxFQUNMLFFBQVEsRUFDUixJQUFJLEVBQ0osR0FBRyxFQUVILEVBQUUsRUFDRixLQUFLLEVBQ0wsV0FBVyxFQUNYLE9BQU8sRUFFUCxTQUFTLEVBQ1QsR0FBRyxHQUNKLE1BQU0sTUFBTSxDQUFDO0FBRWQsT0FBTyxFQUNMLGFBQWEsRUFDYixnQkFBZ0IsR0FFakIsTUFBTSxvQkFBb0IsQ0FBQztBQUM1QixPQUFPLEVBQ0wsb0JBQW9CLEdBRXJCLE1BQU0sd0JBQXdCLENBQUM7QUFjaEMsT0FBTyxFQUNMLE9BQU8sRUFDUCxPQUFPLEVBQ1AsS0FBSyxFQUNMLFlBQVksRUFDWixhQUFhLEVBQ2IsUUFBUSxFQUNSLElBQUksRUFDSixXQUFXLEVBQ1gsU0FBUyxHQUNWLE1BQU0sV0FBVyxDQUFDO0FBQ25CLE9BQU8sRUFBRSxnQkFBZ0IsRUFBbUIsTUFBTSxvQkFBb0IsQ0FBQztBQUN2RSxPQUFPLEVBQ0wseUJBQXlCLEdBRzFCLE1BQU0sNkJBQTZCLENBQUM7QUFDckMsT0FBTyxFQUNMLHFCQUFxQixHQUV0QixNQUFNLHlCQUF5QixDQUFDO0FBQ2pDLE9BQU8sRUFDTCwyQkFBMkIsR0FFNUIsTUFBTSwrQkFBK0IsQ0FBQztBQUN2QyxPQUFPLEVBQ0wsZUFBZSxFQUNmLGdCQUFnQixFQUNoQixnQkFBZ0IsRUFDaEIsbUJBQW1CLEdBQ3BCLE1BQU0sVUFBVSxDQUFDO0FBQ2xCLE9BQU8sRUFBRSxtQkFBbUIsRUFBRSxNQUFNLHlCQUF5QixDQUFDO0FBQzlELE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSxrQkFBa0IsQ0FBQzs7QUFFakQsSUFBSSxPQUF5QixDQUFDO0FBRTlCLE1BQU0sVUFBVSxTQUFTLENBQ3ZCLEdBQW9CLEVBQ3BCLFNBQWtCLEVBQUUsRUFDcEIsSUFBYTtJQUViLE9BQU8sT0FBTyxDQUFDLFNBQVMsQ0FBSSxHQUFHLEVBQUUsTUFBTSxFQUFFLElBQUksQ0FBQyxDQUFDO0FBQ2pELENBQUM7QUFFRCxNQUFNLFVBQVUsZUFBZSxDQUM3QixHQUFvQixFQUNwQixTQUFrQixFQUFFLEVBQ3BCLElBQWE7SUFFYixPQUFPLE9BQU8sQ0FBQyxlQUFlLENBQUksR0FBRyxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsQ0FBQztBQUN2RCxDQUFDO0FBR0QsTUFBTSxPQUFPLGdCQUFnQjtJQW9CcUI7SUFDUjtJQUU5QjtJQUMrQjtJQUcvQjtJQTFCVixZQUFZLENBQXFCO0lBRXpCLFlBQVksR0FBd0IsSUFBSSxDQUFDO0lBQ3pDLFlBQVksR0FBRyxJQUFJLEdBQUcsRUFBdUIsQ0FBQztJQUM5QyxLQUFLLEdBQUcsSUFBSSxHQUFHLEVBQW1DLENBQUM7SUFDbkQsaUJBQWlCLENBQXFCO0lBQ3RDLFdBQVcsR0FBRyxFQUFFLENBQUM7SUFDakIsY0FBYyxHQUFtQixFQUFFLENBQUM7SUFDcEMscUJBQXFCLEdBQUcsS0FBSyxDQUFDO0lBQzlCLElBQUksQ0FBMEI7SUFDOUIsV0FBVyxHQUFHLElBQUksR0FBRyxFQUFVLENBQUM7SUFDaEMsTUFBTSxHQUFHLElBQUksT0FBTyxFQUFtQixDQUFDO0lBRWhELE9BQU8sR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFlBQVksRUFBRSxDQUFDO0lBQzVCLE1BQU0sQ0FFYjtJQUVGLFlBQ2dELE1BQXVCLEVBQy9CLE1BQTJCLEVBRXpELGNBQXVDLEVBQ1IsV0FBaUMsRUFDOUMsVUFBMkIsRUFFN0MsZ0JBQTJDO1FBUEwsV0FBTSxHQUFOLE1BQU0sQ0FBaUI7UUFDL0IsV0FBTSxHQUFOLE1BQU0sQ0FBcUI7UUFFekQsbUJBQWMsR0FBZCxjQUFjLENBQXlCO1FBQ1IsZ0JBQVcsR0FBWCxXQUFXLENBQXNCO1FBR2hFLHFCQUFnQixHQUFoQixnQkFBZ0IsQ0FBMkI7UUFFbkQsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDaEIsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLGFBQWEsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7U0FDcEQ7UUFDRCxPQUFPLEdBQUcsSUFBSSxDQUFDO1FBQ2YsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQztRQUVyRCxJQUFJLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxjQUFjLElBQUksRUFBRSxDQUFDLENBQUM7UUFDekQsSUFBSSxDQUFDLG9DQUFvQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztRQUN2RCxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLENBQUM7UUFDN0MsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLGVBQWUsQ0FBUyxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQztRQUMvRCxrRUFBa0U7UUFDbEUsNERBQTREO1FBQzVELElBQUksQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQztRQUU3Qzs7V0FFRztRQUNILElBQUksQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRTtZQUMvQyxJQUFJLENBQUMsQ0FBQyxJQUFJLEtBQUssd0JBQXdCLElBQUksQ0FBQyxDQUFDLFVBQVUsRUFBRTtnQkFDdkQsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDO2FBQ3hDO1FBQ0gsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQsY0FBYztRQUNaLE9BQU8sSUFBSSxDQUFDLFdBQVcsQ0FBQztJQUMxQixDQUFDO0lBRUQsY0FBYyxDQUFDLElBQVk7UUFDekIsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUM7SUFDMUIsQ0FBQztJQUVELGFBQWE7UUFDWCxPQUFPLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUM7SUFDOUIsQ0FBQztJQUVELGFBQWEsQ0FBQyxJQUFZO1FBQ3hCLElBQUksQ0FBQyxNQUFNLENBQUMsYUFBYSxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDbEMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDckIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUM7WUFDZixJQUFJLEVBQUUsYUFBYTtZQUNuQixPQUFPLEVBQUUsZUFBZSxDQUFDLElBQUksQ0FBQztTQUMvQixDQUFDLENBQUM7UUFDSCxPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRCxpQkFBaUIsQ0FBQyxLQUFxQjtRQUNyQyxJQUFJLENBQUMsY0FBYyxHQUFHLEtBQUssQ0FBQztJQUM5QixDQUFDO0lBRUQ7Ozs7OztPQU1HO0lBQ0gsaUJBQWlCO1FBQ2YsT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDO0lBQzdCLENBQUM7SUFFRCxJQUFJLENBQUMsSUFBWSxFQUFFLFVBQXVCLEVBQUU7UUFDMUMsTUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDcEMsSUFBSSxNQUFNLEVBQUU7WUFDVixPQUFPLE1BQU0sQ0FBQztTQUNmO1FBRUQsSUFBSSxlQUVILENBQUM7UUFDRixNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3pDLElBQUksS0FBYSxDQUFDO1FBQ2xCLElBQUksT0FBTyxFQUFFO1lBQ1gsS0FBSyxHQUFHLGdCQUFnQixDQUFDLElBQUksQ0FBQyxDQUFDO1NBQ2hDO1FBRUQsTUFBTSxjQUFjLEdBQUc7WUFDckIsSUFBSTtZQUNKLFVBQVUsRUFBRSxJQUFJLENBQUMsTUFBTTtZQUN2QixZQUFZLEVBQUUsT0FBTyxDQUFDLFlBQVk7WUFDbEMsSUFBSSxFQUFFLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxLQUFLLEVBQUUsS0FBTSxFQUFFLENBQUMsQ0FBQyxDQUFDLFNBQVM7U0FDOUMsQ0FBQztRQUVGLElBQUksSUFBSSxDQUFDLHNCQUFzQixDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ3JDLG9FQUFvRTtZQUNwRSxNQUFNLFFBQVEsR0FBRyxPQUFPO2dCQUN0QixDQUFDLENBQUMsR0FBRyxLQUFNLElBQUksSUFBSSxDQUFDLGlCQUFpQixFQUFFO2dCQUN2QyxDQUFDLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDO1lBRTNCLE1BQU0sT0FBTyxHQUFHLG1CQUFtQixDQUFDO2dCQUNsQyxHQUFHLGNBQWM7Z0JBQ2pCLFlBQVksRUFBRSxRQUFTO2FBQ3hCLENBQUMsQ0FBQztZQUNILGVBQWUsR0FBRyxRQUFRLENBQUMsT0FBTyxDQUFDLENBQUM7U0FDckM7YUFBTTtZQUNMLE1BQU0sTUFBTSxHQUFHLGFBQWEsQ0FBQyxjQUFjLENBQUMsQ0FBQztZQUM3QyxlQUFlLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1NBQ2hDO1FBRUQsTUFBTSxLQUFLLEdBQUcsZUFBZSxDQUFDLElBQUksQ0FDaEMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLEVBQ2hDLEdBQUcsQ0FBQyxDQUFDLFdBQVcsRUFBRSxFQUFFO1lBQ2xCLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxXQUFXLENBQUMsRUFBRTtnQkFDOUIsV0FBVyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFO29CQUN4QixJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxDQUFDO29CQUMxQyxxRUFBcUU7b0JBQ3JFLElBQUksQ0FBQyxDQUFDLElBQUksS0FBSyxJQUFJLEVBQUU7d0JBQ25CLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7cUJBQ2hDO2dCQUNILENBQUMsQ0FBQyxDQUFDO2dCQUNILE9BQU87YUFDUjtZQUNELElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxFQUFFLFdBQVcsQ0FBQyxDQUFDO1FBQ3hDLENBQUMsQ0FBQyxFQUNGLFVBQVUsQ0FBQyxDQUFDLEtBQUssRUFBRSxFQUFFO1lBQ25CLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRTtnQkFDekIsT0FBTyxDQUFDLEtBQUssQ0FBQywrQkFBK0IsSUFBSSxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUM7YUFDOUQ7WUFFRCxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBQzNDLENBQUMsQ0FBQyxFQUNGLFdBQVcsQ0FBQyxDQUFDLENBQUMsQ0FDZixDQUFDO1FBRUYsSUFBSSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxFQUFFLEtBQUssQ0FBQyxDQUFDO1FBRTVCLE9BQU8sS0FBSyxDQUFDO0lBQ2YsQ0FBQztJQUVEOzs7Ozs7Ozs7O09BVUc7SUFDSCxTQUFTLENBQ1AsR0FBb0IsRUFDcEIsU0FBa0IsRUFBRSxFQUNwQixJQUFJLEdBQUcsSUFBSSxDQUFDLGFBQWEsRUFBRTtRQUUzQixJQUFJLENBQUMsR0FBRztZQUFFLE9BQU8sR0FBVSxDQUFDO1FBRTVCLE1BQU0sRUFBRSxLQUFLLEVBQUUsV0FBVyxFQUFFLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxDQUFDO1FBRTlELElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsRUFBRTtZQUN0QixPQUFPLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUNuQixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxNQUFNLEVBQUUsV0FBVyxDQUFDLENBQzFELENBQUM7U0FDVjtRQUVELEdBQUcsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLEdBQUcsS0FBSyxJQUFJLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUM7UUFFdEMsTUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUNyRCxNQUFNLEtBQUssR0FBRyxXQUFXLENBQUMsR0FBRyxDQUFDLENBQUM7UUFFL0IsSUFBSSxDQUFDLEtBQUssRUFBRTtZQUNWLE9BQU8sSUFBSSxDQUFDLGlCQUFpQixDQUFDLEdBQUcsRUFBRSxLQUFLLEVBQUUsTUFBTSxDQUFDLENBQUM7U0FDbkQ7UUFFRCxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsV0FBVyxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQ2hFLENBQUM7SUFFRDs7Ozs7Ozs7OztPQVVHO0lBQ0gsZUFBZSxDQUNiLEdBQW9CLEVBQ3BCLE1BQWdCLEVBQ2hCLElBQWlELEVBQ2pELFNBQVMsR0FBRyxLQUFLO1FBRWpCLElBQUksWUFBc0MsQ0FBQztRQUMzQyxNQUFNLElBQUksR0FBRyxDQUFDLElBQVksRUFBRSxPQUFxQixFQUFFLEVBQUUsQ0FDbkQsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsT0FBTyxDQUFDLENBQUMsSUFBSSxDQUMzQixHQUFHLENBQUMsR0FBRyxFQUFFLENBQ1AsU0FBUztZQUNQLENBQUMsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLEdBQUcsRUFBRSxNQUFNLEVBQUUsSUFBSSxDQUFDO1lBQ3pDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEdBQUcsRUFBRSxNQUFNLEVBQUUsSUFBSSxDQUFDLENBQ3RDLENBQ0YsQ0FBQztRQUNKLElBQUksS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ2YsT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7U0FDaEU7UUFFRCxJQUFJLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSxhQUFhLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDN0MsdUJBQXVCO1lBQ3ZCLE1BQU0sYUFBYSxHQUFHLEtBQUssQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1lBQzNELElBQUksR0FBRyxhQUFhLENBQUMsS0FBSyxDQUFDO1lBQzNCLFlBQVksR0FBRyxtQkFBbUIsQ0FBQyxhQUFhLEVBQUUsYUFBYSxDQUFDLEtBQUssQ0FBQyxDQUFDO1NBQ3hFO1FBRUQsSUFBSSxHQUFHLElBQWMsQ0FBQztRQUN0QixJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNuRCxPQUFPLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUNuQjtRQUNELGVBQWU7UUFDZixNQUFNLEtBQUssR0FBRyxJQUFJLENBQUM7UUFDbkIsT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FDM0IsU0FBUyxDQUFDLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxJQUFJLENBQUMsR0FBRyxLQUFLLElBQUksSUFBSSxFQUFFLEVBQUUsRUFBRSxZQUFZLEVBQUUsQ0FBQyxDQUFDLENBQ2hFLENBQUM7SUFDSixDQUFDO0lBRUQ7Ozs7Ozs7T0FPRztJQUNLLGVBQWUsQ0FBQyxJQUFZO1FBQ2xDLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO0lBQzdDLENBQUM7SUFzQkQsZUFBZSxDQUNiLEdBQTBCLEVBQzFCLFNBQXlCLEVBQUUsRUFDM0IsSUFBSSxHQUFHLElBQUksQ0FBQyxhQUFhLEVBQUU7UUFFM0IsSUFBSSxRQUFRLENBQUMsR0FBRyxDQUFDLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsRUFBRTtZQUN2QyxNQUFNLEVBQUUsV0FBVyxFQUFFLEtBQUssRUFBRSxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUM5RCxJQUFJLEtBQUssQ0FBQyxPQUFPLENBQUMsR0FBRyxDQUFDLEVBQUU7Z0JBQ3RCLE9BQU8sR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQ25CLElBQUksQ0FBQyxlQUFlLENBQ2xCLEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFDM0IsTUFBTyxFQUNQLFdBQVcsQ0FDWixDQUNLLENBQUM7YUFDVjtZQUVELE1BQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsV0FBVyxDQUFDLENBQUM7WUFDckQsR0FBRyxHQUFHLEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRyxLQUFLLElBQUksR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQztZQUV0QyxNQUFNLEtBQUssR0FBRyxTQUFTLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxXQUFXLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQztZQUMvRCx3R0FBd0c7WUFDeEcsT0FBTyxPQUFPLENBQUMsS0FBSyxDQUFDO2dCQUNuQixDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxHQUFHLEVBQUUsTUFBTyxFQUFFLElBQUksQ0FBQztnQkFDcEMsQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxNQUFPLEVBQUUsV0FBVyxFQUFFLEdBQUcsQ0FBQyxDQUFDO1NBQzdEO1FBRUQsTUFBTSxZQUFZLEdBQVEsRUFBRSxDQUFDO1FBQzdCLEtBQUssTUFBTSxDQUFDLElBQUksRUFBRSxPQUFPLENBQUMsSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsQ0FBQyxFQUFFO1lBQ2xELFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLEVBQUUsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFDLENBQUM7U0FDOUQ7UUFFRCxPQUFPLFlBQVksQ0FBQztJQUN0QixDQUFDO0lBc0JELHFCQUFxQixDQUNuQixHQUEwQixFQUMxQixNQUF1QixFQUN2QixJQUFhO1FBRWIsSUFBSSxRQUFRLENBQUMsR0FBRyxDQUFDLElBQUksS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsRUFBRTtZQUN2QyxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUksR0FBRyxFQUFFLE1BQU8sRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUM7U0FDMUQ7UUFFRCxNQUFNLENBQUMsQ0FBQyxRQUFRLEVBQUUsV0FBVyxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBRWhFOytIQUN1SDtRQUN2SCxPQUFPLElBQUksQ0FBQyxxQkFBcUIsQ0FBSSxRQUFRLEVBQUUsV0FBVyxFQUFFLElBQUksQ0FBQyxDQUFDLElBQUksQ0FDcEUsR0FBRyxDQUFDLENBQUMsS0FBSyxFQUFFLEVBQUU7WUFDWixNQUFNLFlBQVksR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzdCLEtBQUssTUFBTSxDQUFDLElBQUksRUFBRSxPQUFPLENBQUMsSUFBSSxJQUFJLEVBQUU7Z0JBQ2xDLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBSSxJQUFJLEVBQUUsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFDLENBQUM7YUFDakU7WUFFRCxPQUFPLFlBQVksQ0FBQztRQUN0QixDQUFDLENBQUMsQ0FDSCxDQUFDO0lBQ0osQ0FBQztJQWFELGNBQWMsQ0FBQyxXQUFvQjtRQUNqQyxJQUFJLFdBQVcsRUFBRTtZQUNmLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsRUFBRTtnQkFDNUIsT0FBTyxJQUFJLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUMsSUFBSSxFQUFFLENBQUM7YUFDakQ7aUJBQU07Z0JBQ0wscUVBQXFFO2dCQUNyRSxNQUFNLEVBQUUsS0FBSyxFQUFFLFdBQVcsRUFBRSxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxXQUFXLENBQUMsQ0FBQztnQkFDckUsTUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDLElBQUksRUFBRSxDQUFDO2dCQUU3RCxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUMsV0FBVyxFQUFFLEtBQUssQ0FBQyxDQUFDO2FBQ2hEO1NBQ0Y7UUFFRCxPQUFPLElBQUksQ0FBQyxZQUFZLENBQUM7SUFDM0IsQ0FBQztJQUVEOzs7Ozs7Ozs7T0FTRztJQUNILGlCQUFpQixDQUFDLElBQWE7UUFDN0IsSUFBSSxTQUFTLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQztRQUNsQyxJQUFJLElBQUksRUFBRTtZQUNSLE1BQU0sa0JBQWtCLEdBQUcsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLEtBQUssSUFBSSxDQUFDO1lBQzNELElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsSUFBSSxrQkFBa0IsRUFBRTtnQkFDM0MsU0FBUyxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsQ0FBQzthQUN0QjtpQkFBTTtnQkFDTCxTQUFTLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQ2hDLEdBQUcsQ0FBQyxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsR0FBRyxJQUFJLElBQUksV0FBVyxFQUFFLENBQUMsQ0FDL0MsQ0FBQzthQUNIO1NBQ0Y7UUFFRCxPQUFPLFNBQVMsQ0FBQyxJQUFJLENBQ25CLFNBQVMsQ0FBQyxDQUFDLFFBQVEsRUFBRSxFQUFFLENBQ3JCLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FDbkUsQ0FDRixDQUFDO0lBQ0osQ0FBQztJQUVEOzs7Ozs7Ozs7T0FTRztJQUNILGNBQWMsQ0FDWixXQUF3QixFQUN4QixJQUFJLEdBQUcsSUFBSSxDQUFDLGFBQWEsRUFBRSxFQUMzQixVQUFpQyxFQUFFO1FBRW5DLE1BQU0sUUFBUSxHQUFHLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsSUFBSSxFQUFFLENBQUM7UUFDbkQsTUFBTSxhQUFhLEdBQUcsRUFBRSxHQUFHLFFBQVEsRUFBRSxHQUFHLE9BQU8sRUFBRSxDQUFDO1FBQ2xELE1BQU0sS0FBSyxHQUFHLGdCQUFnQixDQUFDLElBQUksQ0FBQyxDQUFDO1FBRXJDOzs7V0FHRztRQUNILElBQUkseUJBQXlCLEdBQUcsV0FBVyxDQUFDO1FBRTVDLHNEQUFzRDtRQUN0RCxJQUFJLEtBQUssRUFBRTtZQUNULE1BQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDdkMseUJBQXlCLEdBQUcsT0FBTyxDQUFDLEVBQUUsQ0FBQyxHQUFHLENBQUMsRUFBRSxXQUFXLEVBQUUsQ0FBQyxDQUFDO1NBQzdEO1FBRUQsTUFBTSxXQUFXLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1FBRTFELE1BQU0saUJBQWlCLEdBQUc7WUFDeEIsR0FBRyxDQUFDLGFBQWEsQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDLGNBQWMsQ0FBQyxXQUFXLENBQUMsQ0FBQztZQUM1RCxHQUFHLHlCQUF5QjtTQUM3QixDQUFDO1FBRUYsTUFBTSxrQkFBa0IsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQVEsQ0FBQyxHQUFHO1lBQ2pELENBQUMsQ0FBQyxpQkFBaUI7WUFDbkIsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1FBQy9CLE1BQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsa0JBQWtCLENBQ2xELGtCQUFrQixFQUNsQixXQUFXLENBQ1osQ0FBQztRQUNGLElBQUksQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLFdBQVcsRUFBRSxRQUFRLENBQUMsQ0FBQztRQUM3QyxhQUFhLENBQUMsVUFBVSxJQUFJLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDLENBQUM7SUFDdkUsQ0FBQztJQUVEOzs7Ozs7Ozs7T0FTRztJQUNILGlCQUFpQixDQUNmLEdBQVcsRUFDWCxLQUFhO0lBQ2IsMENBQTBDO0lBQzFDLElBQUksR0FBRyxJQUFJLENBQUMsYUFBYSxFQUFFLEVBQzNCLFVBQWdELEVBQUU7UUFFbEQsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxxQkFBcUIsQ0FBQyxHQUFHLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxDQUFDO1FBQzFFLE1BQU0sUUFBUSxHQUFHO1lBQ2YsQ0FBQyxHQUFHLENBQUMsRUFBRSxRQUFRO1NBQ2hCLENBQUM7UUFFRixJQUFJLENBQUMsY0FBYyxDQUFDLFFBQVEsRUFBRSxJQUFJLEVBQUUsRUFBRSxHQUFHLE9BQU8sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQztJQUNuRSxDQUFDO0lBRUQ7OztPQUdHO0lBQ0gsb0NBQW9DLENBQUMsRUFDbkMsWUFBWSxHQUMwQjtRQUN0QyxNQUFNLElBQUksR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFlBQVksQ0FBQztRQUMxRSxJQUFJLFlBQVksSUFBSSxJQUFJLENBQUMsc0JBQXNCLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDckQsSUFBSSxDQUFDLGlCQUFpQixHQUFHLElBQUssQ0FBQztTQUNoQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILGlCQUFpQixDQUFDLEdBQVcsRUFBRSxLQUFVLEVBQUUsTUFBZ0I7UUFDekQsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLGNBQWUsQ0FBQyxVQUFVLElBQUksS0FBSyxLQUFLLEVBQUUsRUFBRTtZQUMxRCxPQUFPLEVBQUUsQ0FBQztTQUNYO1FBRUQsSUFBSSxDQUFDLElBQUksQ0FBQyxxQkFBcUIsSUFBSSxJQUFJLENBQUMsc0JBQXNCLEVBQUUsRUFBRTtZQUNoRSw4Q0FBOEM7WUFDOUMsSUFBSSxDQUFDLHFCQUFxQixHQUFHLElBQUksQ0FBQztZQUNsQyxNQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUNsQyxHQUFHLEVBQ0gsTUFBTSxFQUNOLElBQUksQ0FBQyxpQkFBa0IsQ0FDeEIsQ0FBQztZQUNGLElBQUksQ0FBQyxxQkFBcUIsR0FBRyxLQUFLLENBQUM7WUFFbkMsT0FBTyxhQUFhLENBQUM7U0FDdEI7UUFFRCxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUMsTUFBTSxDQUMvQixHQUFHLEVBQ0gsSUFBSSxDQUFDLHFCQUFxQixFQUFFLEVBQzVCLE1BQU0sQ0FDUCxDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0gsYUFBYSxDQUFDLElBQVk7UUFDeEIsT0FBTyxJQUFJLENBQUMsb0JBQW9CLEVBQUUsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7SUFDMUQsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0gsTUFBTSxDQUFDLElBQVk7UUFDakIsT0FBTyxJQUFJLENBQUMsb0JBQW9CLEVBQUUsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7SUFDMUQsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0gsaUJBQWlCLENBQ2YsSUFBWSxFQUNaLFlBQTJCO1FBRTNCLE1BQU0sUUFBUSxHQUFHLGdCQUFnQixDQUFDLElBQUksQ0FBQyxDQUFDO1FBRXhDLElBQUksSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxRQUFRLENBQUMsRUFBRTtZQUNuRSxPQUFPLGFBQWEsQ0FBQztnQkFDbkIsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUM7Z0JBQ25CLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLEVBQUUsWUFBWSxFQUFFLENBQUM7YUFDbEMsQ0FBQyxDQUFDO1NBQ0o7UUFDRCxPQUFPLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLEVBQUUsWUFBWSxFQUFFLENBQUMsQ0FBQztJQUMzQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxzQkFBc0IsQ0FBQyxXQUFtQjtRQUN4QyxJQUNFLElBQUksQ0FBQyxhQUFhLENBQUMsV0FBVyxDQUFDO1lBQy9CLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxnQkFBZ0IsQ0FBQyxXQUFXLENBQUMsQ0FBQyxFQUMzQztZQUNBLE9BQU8sR0FBRyxXQUFXLElBQUksSUFBSSxDQUFDLGFBQWEsRUFBRSxFQUFFLENBQUM7U0FDakQ7UUFDRCxPQUFPLFdBQVcsQ0FBQztJQUNyQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxjQUFjLENBQUMsS0FBYSxFQUFFLEtBQWE7UUFDekMsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxFQUFFO1lBQzdCLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxHQUFHLEVBQUUsQ0FBQztTQUMvQjtRQUNELElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxHQUFHLEtBQUssQ0FBQztJQUMxQyxDQUFDO0lBRUQsV0FBVztRQUNULElBQUksSUFBSSxDQUFDLFlBQVksRUFBRTtZQUNyQixJQUFJLENBQUMsWUFBWSxDQUFDLFdBQVcsRUFBRSxDQUFDO1lBQ2hDLG9HQUFvRztZQUNwRyxrRkFBa0Y7WUFDbEYsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUM7U0FDMUI7UUFDRCwrR0FBK0c7UUFDL0csZ0hBQWdIO1FBQ2hILCtHQUErRztRQUMvRyw0RUFBNEU7UUFDNUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsQ0FBQztJQUNyQixDQUFDO0lBRU8sbUJBQW1CLENBQUMsSUFBWTtRQUN0QyxPQUFPLElBQUksQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDekMsQ0FBQztJQUVPLG9CQUFvQjtRQUMxQixNQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUUxQyxJQUFJLFFBQVEsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUNuQixPQUFPLElBQUksQ0FBQyxpQkFBaUIsRUFBYyxDQUFDO1NBQzdDO1FBRUQsT0FBUSxJQUFJLENBQUMsaUJBQWlCLEVBQXVCLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7SUFDekUsQ0FBQztJQUVPLHFCQUFxQjtRQUMzQixPQUFPO1lBQ0wsR0FBRyxJQUFJLENBQUMsTUFBTTtZQUNkLFVBQVUsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ2hDLGNBQWMsRUFBRSxJQUFJLENBQUMsY0FBYztZQUNuQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7U0FDOUIsQ0FBQztJQUNKLENBQUM7SUFFRDs7O09BR0c7SUFDSyxzQkFBc0IsQ0FBQyxJQUFhO1FBQzFDLE9BQU8sQ0FDTCxJQUFJLENBQUMsTUFBTSxDQUFDLGNBQWUsQ0FBQyxzQkFBc0I7WUFDbEQsSUFBSSxLQUFLLElBQUksQ0FBQyxpQkFBaUIsQ0FDaEMsQ0FBQztJQUNKLENBQUM7SUFFTyxhQUFhLENBQUMsSUFBWSxFQUFFLFdBQXdCO1FBQzFELElBQUksQ0FBQyxjQUFjLENBQUMsV0FBVyxFQUFFLElBQUksRUFBRSxFQUFFLFVBQVUsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDO1FBQzlELElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDO1lBQ2YsVUFBVSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUk7WUFDbkMsSUFBSSxFQUFFLHdCQUF3QjtZQUM5QixPQUFPLEVBQUUsZUFBZSxDQUFDLElBQUksQ0FBQztTQUMvQixDQUFDLENBQUM7UUFDSCxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN0RCxJQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssRUFBRSxDQUFDO0lBQzNCLENBQUM7SUFFTyxhQUFhLENBQUMsSUFBWSxFQUFFLFdBQXdCO1FBQzFELDREQUE0RDtRQUM1RCxxREFBcUQ7UUFDckQsSUFBSSxLQUFLLENBQUMsV0FBVyxDQUFDLGFBQWEsQ0FBQyxFQUFFO1lBQ3BDLFdBQVcsQ0FBQyxhQUFhLEdBQUcsQ0FBQyxDQUFDO1lBRTlCLElBQUksQ0FBQyxXQUFXLENBQUMsYUFBYSxFQUFFO2dCQUM5QixXQUFXLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLENBQUM7YUFDdEU7U0FDRjtRQUVELE1BQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDakMsTUFBTSxTQUFTLEdBQUcsV0FBVyxDQUFDLGFBQWEsQ0FBQztRQUM1QyxNQUFNLFFBQVEsR0FBRyxTQUFVLENBQUMsV0FBVyxDQUFDLGFBQWMsQ0FBQyxDQUFDO1FBQ3hELElBQUksQ0FBQyxXQUFXLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBRTNCLDRFQUE0RTtRQUM1RSxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxFQUFFO1lBQzVCLElBQUksQ0FBQyxhQUFhLENBQUMsUUFBUSxFQUFFLElBQUksQ0FBQyxjQUFjLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUM1RCxPQUFPLEtBQUssQ0FBQztTQUNkO1FBRUQsTUFBTSxjQUFjLEdBQUcsUUFBUSxLQUFLLFFBQVEsQ0FBQyxRQUFRLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDO1FBRWxFLElBQUksQ0FBQyxRQUFRLElBQUksY0FBYyxFQUFFO1lBQy9CLElBQUksR0FBRyxHQUFHLDJEQUEyRCxDQUFDO1lBQ3RFLElBQUksUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7Z0JBQ3ZCLEdBQUcsSUFBSSxzQ0FBc0MsQ0FBQzthQUMvQztZQUVELE1BQU0sSUFBSSxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUM7U0FDdEI7UUFFRCxJQUFJLFdBQVcsR0FBRyxRQUFRLENBQUM7UUFDM0Isc0JBQXNCO1FBQ3RCLElBQUksUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFDdkIsNEJBQTRCO1lBQzVCLHdDQUF3QztZQUN4QyxRQUFRLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBRyxRQUFRLENBQUM7WUFDekMsV0FBVyxHQUFHLFFBQVEsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7U0FDbEM7UUFFRCxXQUFXLENBQUMsYUFBYyxFQUFFLENBQUM7UUFDN0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUM7WUFDZixJQUFJLEVBQUUsd0JBQXdCO1lBQzlCLE9BQU8sRUFBRSxlQUFlLENBQUMsSUFBSSxDQUFDO1NBQy9CLENBQUMsQ0FBQztRQUVILE9BQU8sSUFBSSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsV0FBVyxDQUFDLENBQUM7SUFDN0MsQ0FBQztJQUVPLGNBQWMsQ0FBQyxLQUFhO1FBQ2xDLE1BQU0sRUFBRSxZQUFZLEdBQUcsRUFBRSxFQUFFLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQztRQUMxQyxPQUFPLFlBQVksQ0FBQyxLQUFLLENBQUMsSUFBSSxXQUFXLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDbkQsQ0FBQztJQUVEOzs7O09BSUc7SUFDSyxtQkFBbUIsQ0FBQyxJQUFZO1FBQ3RDLElBQUksV0FBVyxHQUFHLElBQUksQ0FBQztRQUN2QixJQUFJLEtBQUssQ0FBQztRQUVWLElBQUksSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUM1QixpQkFBaUI7WUFDakIsTUFBTSxhQUFhLEdBQUcsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDN0MsYUFBYTtZQUNiLE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDM0MsVUFBVTtZQUNWLFdBQVcsR0FBRyxPQUFPLENBQUMsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQzdELGlCQUFpQjtZQUNqQixLQUFLLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUN0RTtRQUVELE9BQU8sRUFBRSxLQUFLLEVBQUUsV0FBVyxFQUFFLENBQUM7SUFDaEMsQ0FBQztJQUVPLGNBQWMsQ0FBQyxXQUF3QixFQUFFLEdBQVk7UUFDM0QsTUFBTSxNQUFNLEdBQWdCLEVBQUUsQ0FBQztRQUMvQixNQUFNLE1BQU0sR0FBRyxHQUFHLEdBQUcsR0FBRyxDQUFDO1FBRXpCLEtBQUssTUFBTSxVQUFVLElBQUksV0FBVyxFQUFFO1lBQ3BDLElBQUksVUFBVSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsRUFBRTtnQkFDakMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUcsV0FBVyxDQUFDLFVBQVUsQ0FBQyxDQUFDO2FBQ2xFO1NBQ0Y7UUFFRCxPQUFPLE1BQU0sQ0FBQztJQUNoQixDQUFDO0lBRU8sVUFBVSxDQUFDLEdBQW1DO1FBQ3BELE9BQU8sR0FBRyxZQUFZLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDO0lBQ2xFLENBQUM7dUdBdHVCVSxnQkFBZ0Isa0JBb0JMLGdCQUFnQiw2QkFDNUIsb0JBQW9CLGFBQ3BCLHlCQUF5QixhQUV6QixxQkFBcUIsYUFDckIsZ0JBQWdCLGFBQ2hCLDJCQUEyQjsyR0ExQjFCLGdCQUFnQixjQURILE1BQU07OzJGQUNuQixnQkFBZ0I7a0JBRDVCLFVBQVU7bUJBQUMsRUFBRSxVQUFVLEVBQUUsTUFBTSxFQUFFOzswQkFxQjdCLFFBQVE7OzBCQUFJLE1BQU07MkJBQUMsZ0JBQWdCOzswQkFDbkMsTUFBTTsyQkFBQyxvQkFBb0I7OzBCQUMzQixNQUFNOzJCQUFDLHlCQUF5Qjs7MEJBRWhDLE1BQU07MkJBQUMscUJBQXFCOzswQkFDNUIsTUFBTTsyQkFBQyxnQkFBZ0I7OzBCQUN2QixNQUFNOzJCQUFDLDJCQUEyQiIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEluamVjdCwgSW5qZWN0YWJsZSwgT25EZXN0cm95LCBPcHRpb25hbCB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xuaW1wb3J0IHtcbiAgQmVoYXZpb3JTdWJqZWN0LFxuICBjYXRjaEVycm9yLFxuICBjb21iaW5lTGF0ZXN0LFxuICBFTVBUWSxcbiAgZm9ya0pvaW4sXG4gIGZyb20sXG4gIG1hcCxcbiAgT2JzZXJ2YWJsZSxcbiAgb2YsXG4gIHJldHJ5LFxuICBzaGFyZVJlcGxheSxcbiAgU3ViamVjdCxcbiAgU3Vic2NyaXB0aW9uLFxuICBzd2l0Y2hNYXAsXG4gIHRhcCxcbn0gZnJvbSAncnhqcyc7XG5cbmltcG9ydCB7XG4gIERlZmF1bHRMb2FkZXIsXG4gIFRSQU5TTE9DT19MT0FERVIsXG4gIFRyYW5zbG9jb0xvYWRlcixcbn0gZnJvbSAnLi90cmFuc2xvY28ubG9hZGVyJztcbmltcG9ydCB7XG4gIFRSQU5TTE9DT19UUkFOU1BJTEVSLFxuICBUcmFuc2xvY29UcmFuc3BpbGVyLFxufSBmcm9tICcuL3RyYW5zbG9jby50cmFuc3BpbGVyJztcbmltcG9ydCB7XG4gIEF2YWlsYWJsZUxhbmdzLFxuICBIYXNoTWFwLFxuICBJbmxpbmVMb2FkZXIsXG4gIExhbmdEZWZpbml0aW9uLFxuICBMb2FkT3B0aW9ucyxcbiAgU2V0VHJhbnNsYXRpb25PcHRpb25zLFxuICBUcmFuc2xhdGVPYmplY3RQYXJhbXMsXG4gIFRyYW5zbGF0ZVBhcmFtcyxcbiAgVHJhbnNsYXRpb24sXG4gIFRyYW5zbG9jb0V2ZW50cyxcbiAgVHJhbnNsb2NvU2NvcGUsXG59IGZyb20gJy4vdHlwZXMnO1xuaW1wb3J0IHtcbiAgZmxhdHRlbixcbiAgaXNFbXB0eSxcbiAgaXNOaWwsXG4gIGlzU2NvcGVBcnJheSxcbiAgaXNTY29wZU9iamVjdCxcbiAgaXNTdHJpbmcsXG4gIHNpemUsXG4gIHRvQ2FtZWxDYXNlLFxuICB1bmZsYXR0ZW4sXG59IGZyb20gJy4vaGVscGVycyc7XG5pbXBvcnQgeyBUUkFOU0xPQ09fQ09ORklHLCBUcmFuc2xvY29Db25maWcgfSBmcm9tICcuL3RyYW5zbG9jby5jb25maWcnO1xuaW1wb3J0IHtcbiAgVFJBTlNMT0NPX01JU1NJTkdfSEFORExFUixcbiAgVHJhbnNsb2NvTWlzc2luZ0hhbmRsZXIsXG4gIFRyYW5zbG9jb01pc3NpbmdIYW5kbGVyRGF0YSxcbn0gZnJvbSAnLi90cmFuc2xvY28tbWlzc2luZy1oYW5kbGVyJztcbmltcG9ydCB7XG4gIFRSQU5TTE9DT19JTlRFUkNFUFRPUixcbiAgVHJhbnNsb2NvSW50ZXJjZXB0b3IsXG59IGZyb20gJy4vdHJhbnNsb2NvLmludGVyY2VwdG9yJztcbmltcG9ydCB7XG4gIFRSQU5TTE9DT19GQUxMQkFDS19TVFJBVEVHWSxcbiAgVHJhbnNsb2NvRmFsbGJhY2tTdHJhdGVneSxcbn0gZnJvbSAnLi90cmFuc2xvY28tZmFsbGJhY2stc3RyYXRlZ3knO1xuaW1wb3J0IHtcbiAgZ2V0RXZlbnRQYXlsb2FkLFxuICBnZXRMYW5nRnJvbVNjb3BlLFxuICBnZXRTY29wZUZyb21MYW5nLFxuICByZXNvbHZlSW5saW5lTG9hZGVyLFxufSBmcm9tICcuL3NoYXJlZCc7XG5pbXBvcnQgeyBnZXRGYWxsYmFja3NMb2FkZXJzIH0gZnJvbSAnLi9nZXQtZmFsbGJhY2tzLWxvYWRlcnMnO1xuaW1wb3J0IHsgcmVzb2x2ZUxvYWRlciB9IGZyb20gJy4vcmVzb2x2ZS1sb2FkZXInO1xuXG5sZXQgc2VydmljZTogVHJhbnNsb2NvU2VydmljZTtcblxuZXhwb3J0IGZ1bmN0aW9uIHRyYW5zbGF0ZTxUID0gc3RyaW5nPihcbiAga2V5OiBUcmFuc2xhdGVQYXJhbXMsXG4gIHBhcmFtczogSGFzaE1hcCA9IHt9LFxuICBsYW5nPzogc3RyaW5nXG4pOiBUIHtcbiAgcmV0dXJuIHNlcnZpY2UudHJhbnNsYXRlPFQ+KGtleSwgcGFyYW1zLCBsYW5nKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHRyYW5zbGF0ZU9iamVjdDxUPihcbiAga2V5OiBUcmFuc2xhdGVQYXJhbXMsXG4gIHBhcmFtczogSGFzaE1hcCA9IHt9LFxuICBsYW5nPzogc3RyaW5nXG4pOiBUIHwgVFtdIHtcbiAgcmV0dXJuIHNlcnZpY2UudHJhbnNsYXRlT2JqZWN0PFQ+KGtleSwgcGFyYW1zLCBsYW5nKTtcbn1cblxuQEluamVjdGFibGUoeyBwcm92aWRlZEluOiAncm9vdCcgfSlcbmV4cG9ydCBjbGFzcyBUcmFuc2xvY29TZXJ2aWNlIGltcGxlbWVudHMgT25EZXN0cm95IHtcbiAgbGFuZ0NoYW5nZXMkOiBPYnNlcnZhYmxlPHN0cmluZz47XG5cbiAgcHJpdmF0ZSBzdWJzY3JpcHRpb246IFN1YnNjcmlwdGlvbiB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIHRyYW5zbGF0aW9ucyA9IG5ldyBNYXA8c3RyaW5nLCBUcmFuc2xhdGlvbj4oKTtcbiAgcHJpdmF0ZSBjYWNoZSA9IG5ldyBNYXA8c3RyaW5nLCBPYnNlcnZhYmxlPFRyYW5zbGF0aW9uPj4oKTtcbiAgcHJpdmF0ZSBmaXJzdEZhbGxiYWNrTGFuZzogc3RyaW5nIHwgdW5kZWZpbmVkO1xuICBwcml2YXRlIGRlZmF1bHRMYW5nID0gJyc7XG4gIHByaXZhdGUgYXZhaWxhYmxlTGFuZ3M6IEF2YWlsYWJsZUxhbmdzID0gW107XG4gIHByaXZhdGUgaXNSZXNvbHZlZE1pc3NpbmdPbmNlID0gZmFsc2U7XG4gIHByaXZhdGUgbGFuZzogQmVoYXZpb3JTdWJqZWN0PHN0cmluZz47XG4gIHByaXZhdGUgZmFpbGVkTGFuZ3MgPSBuZXcgU2V0PHN0cmluZz4oKTtcbiAgcHJpdmF0ZSBldmVudHMgPSBuZXcgU3ViamVjdDxUcmFuc2xvY29FdmVudHM+KCk7XG5cbiAgZXZlbnRzJCA9IHRoaXMuZXZlbnRzLmFzT2JzZXJ2YWJsZSgpO1xuICByZWFkb25seSBjb25maWc6IFRyYW5zbG9jb0NvbmZpZyAmIHtcbiAgICBzY29wZU1hcHBpbmc/OiBIYXNoTWFwPHN0cmluZz47XG4gIH07XG5cbiAgY29uc3RydWN0b3IoXG4gICAgQE9wdGlvbmFsKCkgQEluamVjdChUUkFOU0xPQ09fTE9BREVSKSBwcml2YXRlIGxvYWRlcjogVHJhbnNsb2NvTG9hZGVyLFxuICAgIEBJbmplY3QoVFJBTlNMT0NPX1RSQU5TUElMRVIpIHByaXZhdGUgcGFyc2VyOiBUcmFuc2xvY29UcmFuc3BpbGVyLFxuICAgIEBJbmplY3QoVFJBTlNMT0NPX01JU1NJTkdfSEFORExFUilcbiAgICBwcml2YXRlIG1pc3NpbmdIYW5kbGVyOiBUcmFuc2xvY29NaXNzaW5nSGFuZGxlcixcbiAgICBASW5qZWN0KFRSQU5TTE9DT19JTlRFUkNFUFRPUikgcHJpdmF0ZSBpbnRlcmNlcHRvcjogVHJhbnNsb2NvSW50ZXJjZXB0b3IsXG4gICAgQEluamVjdChUUkFOU0xPQ09fQ09ORklHKSB1c2VyQ29uZmlnOiBUcmFuc2xvY29Db25maWcsXG4gICAgQEluamVjdChUUkFOU0xPQ09fRkFMTEJBQ0tfU1RSQVRFR1kpXG4gICAgcHJpdmF0ZSBmYWxsYmFja1N0cmF0ZWd5OiBUcmFuc2xvY29GYWxsYmFja1N0cmF0ZWd5XG4gICkge1xuICAgIGlmICghdGhpcy5sb2FkZXIpIHtcbiAgICAgIHRoaXMubG9hZGVyID0gbmV3IERlZmF1bHRMb2FkZXIodGhpcy50cmFuc2xhdGlvbnMpO1xuICAgIH1cbiAgICBzZXJ2aWNlID0gdGhpcztcbiAgICB0aGlzLmNvbmZpZyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodXNlckNvbmZpZykpO1xuXG4gICAgdGhpcy5zZXRBdmFpbGFibGVMYW5ncyh0aGlzLmNvbmZpZy5hdmFpbGFibGVMYW5ncyB8fCBbXSk7XG4gICAgdGhpcy5zZXRGYWxsYmFja0xhbmdGb3JNaXNzaW5nVHJhbnNsYXRpb24odGhpcy5jb25maWcpO1xuICAgIHRoaXMuc2V0RGVmYXVsdExhbmcodGhpcy5jb25maWcuZGVmYXVsdExhbmcpO1xuICAgIHRoaXMubGFuZyA9IG5ldyBCZWhhdmlvclN1YmplY3Q8c3RyaW5nPih0aGlzLmdldERlZmF1bHRMYW5nKCkpO1xuICAgIC8vIERvbid0IHVzZSBkaXN0aW5jdFVudGlsQ2hhbmdlZCBhcyB3ZSBuZWVkIHRoZSBhYmlsaXR5IHRvIHVwZGF0ZVxuICAgIC8vIHRoZSB2YWx1ZSB3aGVuIHVzaW5nIHNldFRyYW5zbGF0aW9uIG9yIHNldFRyYW5zbGF0aW9uS2V5c1xuICAgIHRoaXMubGFuZ0NoYW5nZXMkID0gdGhpcy5sYW5nLmFzT2JzZXJ2YWJsZSgpO1xuXG4gICAgLyoqXG4gICAgICogV2hlbiB3ZSBoYXZlIGEgZmFpbHVyZSwgd2Ugd2FudCB0byBkZWZpbmUgdGhlIG5leHQgbGFuZ3VhZ2UgdGhhdCBzdWNjZWVkZWQgYXMgdGhlIGFjdGl2ZVxuICAgICAqL1xuICAgIHRoaXMuc3Vic2NyaXB0aW9uID0gdGhpcy5ldmVudHMkLnN1YnNjcmliZSgoZSkgPT4ge1xuICAgICAgaWYgKGUudHlwZSA9PT0gJ3RyYW5zbGF0aW9uTG9hZFN1Y2Nlc3MnICYmIGUud2FzRmFpbHVyZSkge1xuICAgICAgICB0aGlzLnNldEFjdGl2ZUxhbmcoZS5wYXlsb2FkLmxhbmdOYW1lKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuXG4gIGdldERlZmF1bHRMYW5nKCkge1xuICAgIHJldHVybiB0aGlzLmRlZmF1bHRMYW5nO1xuICB9XG5cbiAgc2V0RGVmYXVsdExhbmcobGFuZzogc3RyaW5nKSB7XG4gICAgdGhpcy5kZWZhdWx0TGFuZyA9IGxhbmc7XG4gIH1cblxuICBnZXRBY3RpdmVMYW5nKCkge1xuICAgIHJldHVybiB0aGlzLmxhbmcuZ2V0VmFsdWUoKTtcbiAgfVxuXG4gIHNldEFjdGl2ZUxhbmcobGFuZzogc3RyaW5nKSB7XG4gICAgdGhpcy5wYXJzZXIub25MYW5nQ2hhbmdlZD8uKGxhbmcpO1xuICAgIHRoaXMubGFuZy5uZXh0KGxhbmcpO1xuICAgIHRoaXMuZXZlbnRzLm5leHQoe1xuICAgICAgdHlwZTogJ2xhbmdDaGFuZ2VkJyxcbiAgICAgIHBheWxvYWQ6IGdldEV2ZW50UGF5bG9hZChsYW5nKSxcbiAgICB9KTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHNldEF2YWlsYWJsZUxhbmdzKGxhbmdzOiBBdmFpbGFibGVMYW5ncykge1xuICAgIHRoaXMuYXZhaWxhYmxlTGFuZ3MgPSBsYW5ncztcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXRzIHRoZSBhdmFpbGFibGUgbGFuZ3VhZ2VzLlxuICAgKlxuICAgKiBAcmV0dXJuc1xuICAgKiBBbiBhcnJheSBvZiB0aGUgYXZhaWxhYmxlIGxhbmd1YWdlcy4gQ2FuIGJlIGVpdGhlciBhIGBzdHJpbmdbXWAgb3IgYSBgeyBpZDogc3RyaW5nOyBsYWJlbDogc3RyaW5nIH1bXWBcbiAgICogZGVwZW5kaW5nIG9uIGhvdyB0aGUgYXZhaWxhYmxlIGxhbmd1YWdlcyBhcmUgc2V0IGluIHlvdXIgbW9kdWxlLlxuICAgKi9cbiAgZ2V0QXZhaWxhYmxlTGFuZ3MoKSB7XG4gICAgcmV0dXJuIHRoaXMuYXZhaWxhYmxlTGFuZ3M7XG4gIH1cblxuICBsb2FkKHBhdGg6IHN0cmluZywgb3B0aW9uczogTG9hZE9wdGlvbnMgPSB7fSk6IE9ic2VydmFibGU8VHJhbnNsYXRpb24+IHtcbiAgICBjb25zdCBjYWNoZWQgPSB0aGlzLmNhY2hlLmdldChwYXRoKTtcbiAgICBpZiAoY2FjaGVkKSB7XG4gICAgICByZXR1cm4gY2FjaGVkO1xuICAgIH1cblxuICAgIGxldCBsb2FkVHJhbnNsYXRpb246IE9ic2VydmFibGU8XG4gICAgICBUcmFuc2xhdGlvbiB8IHsgdHJhbnNsYXRpb246IFRyYW5zbGF0aW9uOyBsYW5nOiBzdHJpbmcgfVtdXG4gICAgPjtcbiAgICBjb25zdCBpc1Njb3BlID0gdGhpcy5faXNMYW5nU2NvcGVkKHBhdGgpO1xuICAgIGxldCBzY29wZTogc3RyaW5nO1xuICAgIGlmIChpc1Njb3BlKSB7XG4gICAgICBzY29wZSA9IGdldFNjb3BlRnJvbUxhbmcocGF0aCk7XG4gICAgfVxuXG4gICAgY29uc3QgbG9hZGVyc09wdGlvbnMgPSB7XG4gICAgICBwYXRoLFxuICAgICAgbWFpbkxvYWRlcjogdGhpcy5sb2FkZXIsXG4gICAgICBpbmxpbmVMb2FkZXI6IG9wdGlvbnMuaW5saW5lTG9hZGVyLFxuICAgICAgZGF0YTogaXNTY29wZSA/IHsgc2NvcGU6IHNjb3BlISB9IDogdW5kZWZpbmVkLFxuICAgIH07XG5cbiAgICBpZiAodGhpcy51c2VGYWxsYmFja1RyYW5zbGF0aW9uKHBhdGgpKSB7XG4gICAgICAvLyBpZiB0aGUgcGF0aCBpcyBzY29wZSB0aGUgZmFsbGJhY2sgc2hvdWxkIGJlIGBzY29wZS9mYWxsYmFja0xhbmdgO1xuICAgICAgY29uc3QgZmFsbGJhY2sgPSBpc1Njb3BlXG4gICAgICAgID8gYCR7c2NvcGUhfS8ke3RoaXMuZmlyc3RGYWxsYmFja0xhbmd9YFxuICAgICAgICA6IHRoaXMuZmlyc3RGYWxsYmFja0xhbmc7XG5cbiAgICAgIGNvbnN0IGxvYWRlcnMgPSBnZXRGYWxsYmFja3NMb2FkZXJzKHtcbiAgICAgICAgLi4ubG9hZGVyc09wdGlvbnMsXG4gICAgICAgIGZhbGxiYWNrUGF0aDogZmFsbGJhY2shLFxuICAgICAgfSk7XG4gICAgICBsb2FkVHJhbnNsYXRpb24gPSBmb3JrSm9pbihsb2FkZXJzKTtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgbG9hZGVyID0gcmVzb2x2ZUxvYWRlcihsb2FkZXJzT3B0aW9ucyk7XG4gICAgICBsb2FkVHJhbnNsYXRpb24gPSBmcm9tKGxvYWRlcik7XG4gICAgfVxuXG4gICAgY29uc3QgbG9hZCQgPSBsb2FkVHJhbnNsYXRpb24ucGlwZShcbiAgICAgIHJldHJ5KHRoaXMuY29uZmlnLmZhaWxlZFJldHJpZXMpLFxuICAgICAgdGFwKCh0cmFuc2xhdGlvbikgPT4ge1xuICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh0cmFuc2xhdGlvbikpIHtcbiAgICAgICAgICB0cmFuc2xhdGlvbi5mb3JFYWNoKCh0KSA9PiB7XG4gICAgICAgICAgICB0aGlzLmhhbmRsZVN1Y2Nlc3ModC5sYW5nLCB0LnRyYW5zbGF0aW9uKTtcbiAgICAgICAgICAgIC8vIFNhdmUgdGhlIGZhbGxiYWNrIGluIGNhY2hlIHNvIHdlJ2xsIG5vdCBjcmVhdGUgYSByZWR1bmRhbnQgcmVxdWVzdFxuICAgICAgICAgICAgaWYgKHQubGFuZyAhPT0gcGF0aCkge1xuICAgICAgICAgICAgICB0aGlzLmNhY2hlLnNldCh0LmxhbmcsIG9mKHt9KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuaGFuZGxlU3VjY2VzcyhwYXRoLCB0cmFuc2xhdGlvbik7XG4gICAgICB9KSxcbiAgICAgIGNhdGNoRXJyb3IoKGVycm9yKSA9PiB7XG4gICAgICAgIGlmICghdGhpcy5jb25maWcucHJvZE1vZGUpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvciB3aGlsZSB0cnlpbmcgdG8gbG9hZCBcIiR7cGF0aH1cImAsIGVycm9yKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiB0aGlzLmhhbmRsZUZhaWx1cmUocGF0aCwgb3B0aW9ucyk7XG4gICAgICB9KSxcbiAgICAgIHNoYXJlUmVwbGF5KDEpXG4gICAgKTtcblxuICAgIHRoaXMuY2FjaGUuc2V0KHBhdGgsIGxvYWQkKTtcblxuICAgIHJldHVybiBsb2FkJDtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXRzIHRoZSBpbnN0YW50IHRyYW5zbGF0ZWQgdmFsdWUgb2YgYSBrZXlcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICpcbiAgICogdHJhbnNsYXRlPHN0cmluZz4oJ2hlbGxvJylcbiAgICogdHJhbnNsYXRlKCdoZWxsbycsIHsgdmFsdWU6ICd2YWx1ZScgfSlcbiAgICogdHJhbnNsYXRlPHN0cmluZ1tdPihbJ2hlbGxvJywgJ2tleSddKVxuICAgKiB0cmFuc2xhdGUoJ2hlbGxvJywgeyB9LCAnZW4nKVxuICAgKiB0cmFuc2xhdGUoJ3Njb3BlLnNvbWVLZXknLCB7IH0sICdlbicpXG4gICAqL1xuICB0cmFuc2xhdGU8VCA9IHN0cmluZz4oXG4gICAga2V5OiBUcmFuc2xhdGVQYXJhbXMsXG4gICAgcGFyYW1zOiBIYXNoTWFwID0ge30sXG4gICAgbGFuZyA9IHRoaXMuZ2V0QWN0aXZlTGFuZygpXG4gICk6IFQge1xuICAgIGlmICgha2V5KSByZXR1cm4ga2V5IGFzIGFueTtcblxuICAgIGNvbnN0IHsgc2NvcGUsIHJlc29sdmVMYW5nIH0gPSB0aGlzLnJlc29sdmVMYW5nQW5kU2NvcGUobGFuZyk7XG5cbiAgICBpZiAoQXJyYXkuaXNBcnJheShrZXkpKSB7XG4gICAgICByZXR1cm4ga2V5Lm1hcCgoaykgPT5cbiAgICAgICAgdGhpcy50cmFuc2xhdGUoc2NvcGUgPyBgJHtzY29wZX0uJHtrfWAgOiBrLCBwYXJhbXMsIHJlc29sdmVMYW5nKVxuICAgICAgKSBhcyBhbnk7XG4gICAgfVxuXG4gICAga2V5ID0gc2NvcGUgPyBgJHtzY29wZX0uJHtrZXl9YCA6IGtleTtcblxuICAgIGNvbnN0IHRyYW5zbGF0aW9uID0gdGhpcy5nZXRUcmFuc2xhdGlvbihyZXNvbHZlTGFuZyk7XG4gICAgY29uc3QgdmFsdWUgPSB0cmFuc2xhdGlvbltrZXldO1xuXG4gICAgaWYgKCF2YWx1ZSkge1xuICAgICAgcmV0dXJuIHRoaXMuX2hhbmRsZU1pc3NpbmdLZXkoa2V5LCB2YWx1ZSwgcGFyYW1zKTtcbiAgICB9XG5cbiAgICByZXR1cm4gdGhpcy5wYXJzZXIudHJhbnNwaWxlKHZhbHVlLCBwYXJhbXMsIHRyYW5zbGF0aW9uLCBrZXkpO1xuICB9XG5cbiAgLyoqXG4gICAqIEdldHMgdGhlIHRyYW5zbGF0ZWQgdmFsdWUgb2YgYSBrZXkgYXMgb2JzZXJ2YWJsZVxuICAgKlxuICAgKiBAZXhhbXBsZVxuICAgKlxuICAgKiBzZWxlY3RUcmFuc2xhdGU8c3RyaW5nPignaGVsbG8nKS5zdWJzY3JpYmUodmFsdWUgPT4gLi4uKVxuICAgKiBzZWxlY3RUcmFuc2xhdGU8c3RyaW5nPignaGVsbG8nLCB7fSwgJ2VzJykuc3Vic2NyaWJlKHZhbHVlID0+IC4uLilcbiAgICogc2VsZWN0VHJhbnNsYXRlPHN0cmluZz4oJ2hlbGxvJywge30sICd0b2RvcycpLnN1YnNjcmliZSh2YWx1ZSA9PiAuLi4pXG4gICAqIHNlbGVjdFRyYW5zbGF0ZTxzdHJpbmc+KCdoZWxsbycsIHt9LCB7IHNjb3BlOiAndG9kb3MnIH0pLnN1YnNjcmliZSh2YWx1ZSA9PiAuLi4pXG4gICAqXG4gICAqL1xuICBzZWxlY3RUcmFuc2xhdGU8VCA9IGFueT4oXG4gICAga2V5OiBUcmFuc2xhdGVQYXJhbXMsXG4gICAgcGFyYW1zPzogSGFzaE1hcCxcbiAgICBsYW5nPzogc3RyaW5nIHwgVHJhbnNsb2NvU2NvcGUgfCBUcmFuc2xvY29TY29wZVtdLFxuICAgIF9pc09iamVjdCA9IGZhbHNlXG4gICk6IE9ic2VydmFibGU8VD4ge1xuICAgIGxldCBpbmxpbmVMb2FkZXI6IElubGluZUxvYWRlciB8IHVuZGVmaW5lZDtcbiAgICBjb25zdCBsb2FkID0gKGxhbmc6IHN0cmluZywgb3B0aW9ucz86IExvYWRPcHRpb25zKSA9PlxuICAgICAgdGhpcy5sb2FkKGxhbmcsIG9wdGlvbnMpLnBpcGUoXG4gICAgICAgIG1hcCgoKSA9PlxuICAgICAgICAgIF9pc09iamVjdFxuICAgICAgICAgICAgPyB0aGlzLnRyYW5zbGF0ZU9iamVjdChrZXksIHBhcmFtcywgbGFuZylcbiAgICAgICAgICAgIDogdGhpcy50cmFuc2xhdGUoa2V5LCBwYXJhbXMsIGxhbmcpXG4gICAgICAgIClcbiAgICAgICk7XG4gICAgaWYgKGlzTmlsKGxhbmcpKSB7XG4gICAgICByZXR1cm4gdGhpcy5sYW5nQ2hhbmdlcyQucGlwZShzd2l0Y2hNYXAoKGxhbmcpID0+IGxvYWQobGFuZykpKTtcbiAgICB9XG5cbiAgICBpZiAoaXNTY29wZUFycmF5KGxhbmcpIHx8IGlzU2NvcGVPYmplY3QobGFuZykpIHtcbiAgICAgIC8vIGl0J3MgYSBzY29wZSBvYmplY3QuXG4gICAgICBjb25zdCBwcm92aWRlclNjb3BlID0gQXJyYXkuaXNBcnJheShsYW5nKSA/IGxhbmdbMF0gOiBsYW5nO1xuICAgICAgbGFuZyA9IHByb3ZpZGVyU2NvcGUuc2NvcGU7XG4gICAgICBpbmxpbmVMb2FkZXIgPSByZXNvbHZlSW5saW5lTG9hZGVyKHByb3ZpZGVyU2NvcGUsIHByb3ZpZGVyU2NvcGUuc2NvcGUpO1xuICAgIH1cblxuICAgIGxhbmcgPSBsYW5nIGFzIHN0cmluZztcbiAgICBpZiAodGhpcy5pc0xhbmcobGFuZykgfHwgdGhpcy5pc1Njb3BlV2l0aExhbmcobGFuZykpIHtcbiAgICAgIHJldHVybiBsb2FkKGxhbmcpO1xuICAgIH1cbiAgICAvLyBpdCdzIGEgc2NvcGVcbiAgICBjb25zdCBzY29wZSA9IGxhbmc7XG4gICAgcmV0dXJuIHRoaXMubGFuZ0NoYW5nZXMkLnBpcGUoXG4gICAgICBzd2l0Y2hNYXAoKGxhbmcpID0+IGxvYWQoYCR7c2NvcGV9LyR7bGFuZ31gLCB7IGlubGluZUxvYWRlciB9KSlcbiAgICApO1xuICB9XG5cbiAgLyoqXG4gICAqIFdoZXRoZXIgdGhlIHNjb3BlIHdpdGggbGFuZ1xuICAgKlxuICAgKiBAZXhhbXBsZVxuICAgKlxuICAgKiB0b2Rvcy9lbiA9PiB0cnVlXG4gICAqIHRvZG9zID0+IGZhbHNlXG4gICAqL1xuICBwcml2YXRlIGlzU2NvcGVXaXRoTGFuZyhsYW5nOiBzdHJpbmcpIHtcbiAgICByZXR1cm4gdGhpcy5pc0xhbmcoZ2V0TGFuZ0Zyb21TY29wZShsYW5nKSk7XG4gIH1cblxuICAvKipcbiAgICogVHJhbnNsYXRlIHRoZSBnaXZlbiBwYXRoIHRoYXQgcmV0dXJucyBhbiBvYmplY3RcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICpcbiAgICogc2VydmljZS50cmFuc2xhdGVPYmplY3QoJ3BhdGgudG8ub2JqZWN0JywgeydzdWJwYXRoJzogeyB2YWx1ZTogJ3NvbWVWYWx1ZSd9fSkgPT4gcmV0dXJucyB0cmFuc2xhdGVkIG9iamVjdFxuICAgKlxuICAgKi9cbiAgdHJhbnNsYXRlT2JqZWN0PFQgPSBhbnk+KGtleTogc3RyaW5nLCBwYXJhbXM/OiBIYXNoTWFwLCBsYW5nPzogc3RyaW5nKTogVDtcbiAgdHJhbnNsYXRlT2JqZWN0PFQgPSBhbnk+KGtleTogc3RyaW5nW10sIHBhcmFtcz86IEhhc2hNYXAsIGxhbmc/OiBzdHJpbmcpOiBUW107XG4gIHRyYW5zbGF0ZU9iamVjdDxUID0gYW55PihcbiAgICBrZXk6IFRyYW5zbGF0ZVBhcmFtcyxcbiAgICBwYXJhbXM/OiBIYXNoTWFwLFxuICAgIGxhbmc/OiBzdHJpbmdcbiAgKTogVCB8IFRbXTtcbiAgdHJhbnNsYXRlT2JqZWN0PFQgPSBhbnk+KFxuICAgIGtleTogSGFzaE1hcCB8IE1hcDxzdHJpbmcsIEhhc2hNYXA+LFxuICAgIHBhcmFtcz86IG51bGwsXG4gICAgbGFuZz86IHN0cmluZ1xuICApOiBUW107XG4gIHRyYW5zbGF0ZU9iamVjdDxUID0gYW55PihcbiAgICBrZXk6IFRyYW5zbGF0ZU9iamVjdFBhcmFtcyxcbiAgICBwYXJhbXM6IEhhc2hNYXAgfCBudWxsID0ge30sXG4gICAgbGFuZyA9IHRoaXMuZ2V0QWN0aXZlTGFuZygpXG4gICk6IFQgfCBUW10ge1xuICAgIGlmIChpc1N0cmluZyhrZXkpIHx8IEFycmF5LmlzQXJyYXkoa2V5KSkge1xuICAgICAgY29uc3QgeyByZXNvbHZlTGFuZywgc2NvcGUgfSA9IHRoaXMucmVzb2x2ZUxhbmdBbmRTY29wZShsYW5nKTtcbiAgICAgIGlmIChBcnJheS5pc0FycmF5KGtleSkpIHtcbiAgICAgICAgcmV0dXJuIGtleS5tYXAoKGspID0+XG4gICAgICAgICAgdGhpcy50cmFuc2xhdGVPYmplY3QoXG4gICAgICAgICAgICBzY29wZSA/IGAke3Njb3BlfS4ke2t9YCA6IGssXG4gICAgICAgICAgICBwYXJhbXMhLFxuICAgICAgICAgICAgcmVzb2x2ZUxhbmdcbiAgICAgICAgICApXG4gICAgICAgICkgYXMgYW55O1xuICAgICAgfVxuXG4gICAgICBjb25zdCB0cmFuc2xhdGlvbiA9IHRoaXMuZ2V0VHJhbnNsYXRpb24ocmVzb2x2ZUxhbmcpO1xuICAgICAga2V5ID0gc2NvcGUgPyBgJHtzY29wZX0uJHtrZXl9YCA6IGtleTtcblxuICAgICAgY29uc3QgdmFsdWUgPSB1bmZsYXR0ZW4odGhpcy5nZXRPYmplY3RCeUtleSh0cmFuc2xhdGlvbiwga2V5KSk7XG4gICAgICAvKiBJZiBhbiBlbXB0eSBvYmplY3Qgd2FzIHJldHVybmVkIHdlIHdhbnQgdG8gdHJ5IGFuZCB0cmFuc2xhdGUgdGhlIGtleSBhcyBhIHN0cmluZyBhbmQgbm90IGFuIG9iamVjdCAqL1xuICAgICAgcmV0dXJuIGlzRW1wdHkodmFsdWUpXG4gICAgICAgID8gdGhpcy50cmFuc2xhdGUoa2V5LCBwYXJhbXMhLCBsYW5nKVxuICAgICAgICA6IHRoaXMucGFyc2VyLnRyYW5zcGlsZSh2YWx1ZSwgcGFyYW1zISwgdHJhbnNsYXRpb24sIGtleSk7XG4gICAgfVxuXG4gICAgY29uc3QgdHJhbnNsYXRpb25zOiBUW10gPSBbXTtcbiAgICBmb3IgKGNvbnN0IFtfa2V5LCBfcGFyYW1zXSBvZiB0aGlzLmdldEVudHJpZXMoa2V5KSkge1xuICAgICAgdHJhbnNsYXRpb25zLnB1c2godGhpcy50cmFuc2xhdGVPYmplY3QoX2tleSwgX3BhcmFtcywgbGFuZykpO1xuICAgIH1cblxuICAgIHJldHVybiB0cmFuc2xhdGlvbnM7XG4gIH1cblxuICBzZWxlY3RUcmFuc2xhdGVPYmplY3Q8VCA9IGFueT4oXG4gICAga2V5OiBzdHJpbmcsXG4gICAgcGFyYW1zPzogSGFzaE1hcCxcbiAgICBsYW5nPzogc3RyaW5nXG4gICk6IE9ic2VydmFibGU8VD47XG4gIHNlbGVjdFRyYW5zbGF0ZU9iamVjdDxUID0gYW55PihcbiAgICBrZXk6IHN0cmluZ1tdLFxuICAgIHBhcmFtcz86IEhhc2hNYXAsXG4gICAgbGFuZz86IHN0cmluZ1xuICApOiBPYnNlcnZhYmxlPFRbXT47XG4gIHNlbGVjdFRyYW5zbGF0ZU9iamVjdDxUID0gYW55PihcbiAgICBrZXk6IFRyYW5zbGF0ZVBhcmFtcyxcbiAgICBwYXJhbXM/OiBIYXNoTWFwLFxuICAgIGxhbmc/OiBzdHJpbmdcbiAgKTogT2JzZXJ2YWJsZTxUPiB8IE9ic2VydmFibGU8VFtdPjtcbiAgc2VsZWN0VHJhbnNsYXRlT2JqZWN0PFQgPSBhbnk+KFxuICAgIGtleTogSGFzaE1hcCB8IE1hcDxzdHJpbmcsIEhhc2hNYXA+LFxuICAgIHBhcmFtcz86IG51bGwsXG4gICAgbGFuZz86IHN0cmluZ1xuICApOiBPYnNlcnZhYmxlPFRbXT47XG4gIHNlbGVjdFRyYW5zbGF0ZU9iamVjdDxUID0gYW55PihcbiAgICBrZXk6IFRyYW5zbGF0ZU9iamVjdFBhcmFtcyxcbiAgICBwYXJhbXM/OiBIYXNoTWFwIHwgbnVsbCxcbiAgICBsYW5nPzogc3RyaW5nXG4gICk6IE9ic2VydmFibGU8VD4gfCBPYnNlcnZhYmxlPFRbXT4ge1xuICAgIGlmIChpc1N0cmluZyhrZXkpIHx8IEFycmF5LmlzQXJyYXkoa2V5KSkge1xuICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0VHJhbnNsYXRlPFQ+KGtleSwgcGFyYW1zISwgbGFuZywgdHJ1ZSk7XG4gICAgfVxuXG4gICAgY29uc3QgW1tmaXJzdEtleSwgZmlyc3RQYXJhbXNdLCAuLi5yZXN0XSA9IHRoaXMuZ2V0RW50cmllcyhrZXkpO1xuXG4gICAgLyogSW4gb3JkZXIgdG8gYXZvaWQgc3Vic2NyaWJpbmcgbXVsdGlwbGUgdGltZXMgdG8gdGhlIGxvYWQgbGFuZ3VhZ2UgZXZlbnQgYnkgY2FsbGluZyBzZWxlY3RUcmFuc2xhdGVPYmplY3QgZm9yIGVhY2ggcGFpcixcbiAgICAgKiB3ZSBsaXN0ZW4gdG8gd2hlbiB0aGUgZmlyc3Qga2V5IGhhcyBiZWVuIHRyYW5zbGF0ZWQgKHRoZSBsYW5ndWFnZSBpcyBsb2FkZWQpIGFuZCB0cmFuc2xhdGUgdGhlIHJlc3Qgc3luY2hyb25vdXNseSAqL1xuICAgIHJldHVybiB0aGlzLnNlbGVjdFRyYW5zbGF0ZU9iamVjdDxUPihmaXJzdEtleSwgZmlyc3RQYXJhbXMsIGxhbmcpLnBpcGUoXG4gICAgICBtYXAoKHZhbHVlKSA9PiB7XG4gICAgICAgIGNvbnN0IHRyYW5zbGF0aW9ucyA9IFt2YWx1ZV07XG4gICAgICAgIGZvciAoY29uc3QgW19rZXksIF9wYXJhbXNdIG9mIHJlc3QpIHtcbiAgICAgICAgICB0cmFuc2xhdGlvbnMucHVzaCh0aGlzLnRyYW5zbGF0ZU9iamVjdDxUPihfa2V5LCBfcGFyYW1zLCBsYW5nKSk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gdHJhbnNsYXRpb25zO1xuICAgICAgfSlcbiAgICApO1xuICB9XG5cbiAgLyoqXG4gICAqIEdldHMgYW4gb2JqZWN0IG9mIHRyYW5zbGF0aW9ucyBmb3IgYSBnaXZlbiBsYW5ndWFnZVxuICAgKlxuICAgKiBAZXhhbXBsZVxuICAgKlxuICAgKiBnZXRUcmFuc2xhdGlvbigpXG4gICAqIGdldFRyYW5zbGF0aW9uKCdlbicpXG4gICAqIGdldFRyYW5zbGF0aW9uKCdhZG1pbi1wYWdlL2VuJylcbiAgICovXG4gIGdldFRyYW5zbGF0aW9uKCk6IE1hcDxzdHJpbmcsIFRyYW5zbGF0aW9uPjtcbiAgZ2V0VHJhbnNsYXRpb24obGFuZ09yU2NvcGU6IHN0cmluZyk6IFRyYW5zbGF0aW9uO1xuICBnZXRUcmFuc2xhdGlvbihsYW5nT3JTY29wZT86IHN0cmluZyk6IE1hcDxzdHJpbmcsIFRyYW5zbGF0aW9uPiB8IFRyYW5zbGF0aW9uIHtcbiAgICBpZiAobGFuZ09yU2NvcGUpIHtcbiAgICAgIGlmICh0aGlzLmlzTGFuZyhsYW5nT3JTY29wZSkpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudHJhbnNsYXRpb25zLmdldChsYW5nT3JTY29wZSkgfHwge307XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBUaGlzIGlzIGEgc2NvcGUsIGJ1aWxkIHRoZSBzY29wZSB2YWx1ZSBmcm9tIHRoZSB0cmFuc2xhdGlvbiBvYmplY3RcbiAgICAgICAgY29uc3QgeyBzY29wZSwgcmVzb2x2ZUxhbmcgfSA9IHRoaXMucmVzb2x2ZUxhbmdBbmRTY29wZShsYW5nT3JTY29wZSk7XG4gICAgICAgIGNvbnN0IHRyYW5zbGF0aW9uID0gdGhpcy50cmFuc2xhdGlvbnMuZ2V0KHJlc29sdmVMYW5nKSB8fCB7fTtcblxuICAgICAgICByZXR1cm4gdGhpcy5nZXRPYmplY3RCeUtleSh0cmFuc2xhdGlvbiwgc2NvcGUpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB0aGlzLnRyYW5zbGF0aW9ucztcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXRzIGFuIG9iamVjdCBvZiB0cmFuc2xhdGlvbnMgZm9yIGEgZ2l2ZW4gbGFuZ3VhZ2VcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICpcbiAgICogc2VsZWN0VHJhbnNsYXRpb24oKS5zdWJzY3JpYmUoKSAtIHdpbGwgcmV0dXJuIHRoZSBjdXJyZW50IGxhbmcgdHJhbnNsYXRpb25cbiAgICogc2VsZWN0VHJhbnNsYXRpb24oJ2VzJykuc3Vic2NyaWJlKClcbiAgICogc2VsZWN0VHJhbnNsYXRpb24oJ2FkbWluLXBhZ2UnKS5zdWJzY3JpYmUoKSAtIHdpbGwgcmV0dXJuIHRoZSBjdXJyZW50IGxhbmcgc2NvcGUgdHJhbnNsYXRpb25cbiAgICogc2VsZWN0VHJhbnNsYXRpb24oJ2FkbWluLXBhZ2UvZXMnKS5zdWJzY3JpYmUoKVxuICAgKi9cbiAgc2VsZWN0VHJhbnNsYXRpb24obGFuZz86IHN0cmluZyk6IE9ic2VydmFibGU8VHJhbnNsYXRpb24+IHtcbiAgICBsZXQgbGFuZ3VhZ2UkID0gdGhpcy5sYW5nQ2hhbmdlcyQ7XG4gICAgaWYgKGxhbmcpIHtcbiAgICAgIGNvbnN0IHNjb3BlTGFuZ1NwZWNpZmllZCA9IGdldExhbmdGcm9tU2NvcGUobGFuZykgIT09IGxhbmc7XG4gICAgICBpZiAodGhpcy5pc0xhbmcobGFuZykgfHwgc2NvcGVMYW5nU3BlY2lmaWVkKSB7XG4gICAgICAgIGxhbmd1YWdlJCA9IG9mKGxhbmcpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbGFuZ3VhZ2UkID0gdGhpcy5sYW5nQ2hhbmdlcyQucGlwZShcbiAgICAgICAgICBtYXAoKGN1cnJlbnRMYW5nKSA9PiBgJHtsYW5nfS8ke2N1cnJlbnRMYW5nfWApXG4gICAgICAgICk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIGxhbmd1YWdlJC5waXBlKFxuICAgICAgc3dpdGNoTWFwKChsYW5ndWFnZSkgPT5cbiAgICAgICAgdGhpcy5sb2FkKGxhbmd1YWdlKS5waXBlKG1hcCgoKSA9PiB0aGlzLmdldFRyYW5zbGF0aW9uKGxhbmd1YWdlKSkpXG4gICAgICApXG4gICAgKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBTZXRzIG9yIG1lcmdlIGEgZ2l2ZW4gdHJhbnNsYXRpb24gb2JqZWN0IHRvIGN1cnJlbnQgbGFuZ1xuICAgKlxuICAgKiBAZXhhbXBsZVxuICAgKlxuICAgKiBzZXRUcmFuc2xhdGlvbih7IC4uLiB9KVxuICAgKiBzZXRUcmFuc2xhdGlvbih7IC4uLiB9LCAnZW4nKVxuICAgKiBzZXRUcmFuc2xhdGlvbih7IC4uLiB9LCAnZXMnLCB7IG1lcmdlOiBmYWxzZSB9IClcbiAgICogc2V0VHJhbnNsYXRpb24oeyAuLi4gfSwgJ3RvZG9zL2VuJywgeyBtZXJnZTogZmFsc2UgfSApXG4gICAqL1xuICBzZXRUcmFuc2xhdGlvbihcbiAgICB0cmFuc2xhdGlvbjogVHJhbnNsYXRpb24sXG4gICAgbGFuZyA9IHRoaXMuZ2V0QWN0aXZlTGFuZygpLFxuICAgIG9wdGlvbnM6IFNldFRyYW5zbGF0aW9uT3B0aW9ucyA9IHt9XG4gICkge1xuICAgIGNvbnN0IGRlZmF1bHRzID0geyBtZXJnZTogdHJ1ZSwgZW1pdENoYW5nZTogdHJ1ZSB9O1xuICAgIGNvbnN0IG1lcmdlZE9wdGlvbnMgPSB7IC4uLmRlZmF1bHRzLCAuLi5vcHRpb25zIH07XG4gICAgY29uc3Qgc2NvcGUgPSBnZXRTY29wZUZyb21MYW5nKGxhbmcpO1xuXG4gICAgLyoqXG4gICAgICogSWYgdGhpcyBpc24ndCBhIHNjb3BlIHdlIHVzZSB0aGUgd2hvbGUgdHJhbnNsYXRpb24gYXMgaXNcbiAgICAgKiBvdGhlcndpc2Ugd2UgbmVlZCB0byBmbGF0IHRoZSBzY29wZSBhbmQgdXNlIGl0XG4gICAgICovXG4gICAgbGV0IGZsYXR0ZW5TY29wZU9yVHJhbnNsYXRpb24gPSB0cmFuc2xhdGlvbjtcblxuICAgIC8vIE1lcmdlZCB0aGUgc2NvcGVkIGxhbmd1YWdlIGludG8gdGhlIGFjdGl2ZSBsYW5ndWFnZVxuICAgIGlmIChzY29wZSkge1xuICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRNYXBwZWRTY29wZShzY29wZSk7XG4gICAgICBmbGF0dGVuU2NvcGVPclRyYW5zbGF0aW9uID0gZmxhdHRlbih7IFtrZXldOiB0cmFuc2xhdGlvbiB9KTtcbiAgICB9XG5cbiAgICBjb25zdCBjdXJyZW50TGFuZyA9IHNjb3BlID8gZ2V0TGFuZ0Zyb21TY29wZShsYW5nKSA6IGxhbmc7XG5cbiAgICBjb25zdCBtZXJnZWRUcmFuc2xhdGlvbiA9IHtcbiAgICAgIC4uLihtZXJnZWRPcHRpb25zLm1lcmdlICYmIHRoaXMuZ2V0VHJhbnNsYXRpb24oY3VycmVudExhbmcpKSxcbiAgICAgIC4uLmZsYXR0ZW5TY29wZU9yVHJhbnNsYXRpb24sXG4gICAgfTtcblxuICAgIGNvbnN0IGZsYXR0ZW5UcmFuc2xhdGlvbiA9IHRoaXMuY29uZmlnLmZsYXR0ZW4hLmFvdFxuICAgICAgPyBtZXJnZWRUcmFuc2xhdGlvblxuICAgICAgOiBmbGF0dGVuKG1lcmdlZFRyYW5zbGF0aW9uKTtcbiAgICBjb25zdCB3aXRoSG9vayA9IHRoaXMuaW50ZXJjZXB0b3IucHJlU2F2ZVRyYW5zbGF0aW9uKFxuICAgICAgZmxhdHRlblRyYW5zbGF0aW9uLFxuICAgICAgY3VycmVudExhbmdcbiAgICApO1xuICAgIHRoaXMudHJhbnNsYXRpb25zLnNldChjdXJyZW50TGFuZywgd2l0aEhvb2spO1xuICAgIG1lcmdlZE9wdGlvbnMuZW1pdENoYW5nZSAmJiB0aGlzLnNldEFjdGl2ZUxhbmcodGhpcy5nZXRBY3RpdmVMYW5nKCkpO1xuICB9XG5cbiAgLyoqXG4gICAqIFNldHMgdHJhbnNsYXRpb24ga2V5IHdpdGggZ2l2ZW4gdmFsdWVcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICpcbiAgICogc2V0VHJhbnNsYXRpb25LZXkoJ2tleScsICd2YWx1ZScpXG4gICAqIHNldFRyYW5zbGF0aW9uS2V5KCdrZXkubmVzdGVkJywgJ3ZhbHVlJylcbiAgICogc2V0VHJhbnNsYXRpb25LZXkoJ2tleS5uZXN0ZWQnLCAndmFsdWUnLCAnZW4nKVxuICAgKiBzZXRUcmFuc2xhdGlvbktleSgna2V5Lm5lc3RlZCcsICd2YWx1ZScsICdlbicsIHsgZW1pdENoYW5nZTogZmFsc2UgfSApXG4gICAqL1xuICBzZXRUcmFuc2xhdGlvbktleShcbiAgICBrZXk6IHN0cmluZyxcbiAgICB2YWx1ZTogc3RyaW5nLFxuICAgIC8vIFRvZG86IEFkZCB0aGUgbGFuZyB0byB0aGUgb3B0aW9ucyBpbiB2M1xuICAgIGxhbmcgPSB0aGlzLmdldEFjdGl2ZUxhbmcoKSxcbiAgICBvcHRpb25zOiBPbWl0PFNldFRyYW5zbGF0aW9uT3B0aW9ucywgJ21lcmdlJz4gPSB7fVxuICApIHtcbiAgICBjb25zdCB3aXRoSG9vayA9IHRoaXMuaW50ZXJjZXB0b3IucHJlU2F2ZVRyYW5zbGF0aW9uS2V5KGtleSwgdmFsdWUsIGxhbmcpO1xuICAgIGNvbnN0IG5ld1ZhbHVlID0ge1xuICAgICAgW2tleV06IHdpdGhIb29rLFxuICAgIH07XG5cbiAgICB0aGlzLnNldFRyYW5zbGF0aW9uKG5ld1ZhbHVlLCBsYW5nLCB7IC4uLm9wdGlvbnMsIG1lcmdlOiB0cnVlIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIFNldHMgdGhlIGZhbGxiYWNrIGxhbmcgZm9yIHRoZSBjdXJyZW50bHkgYWN0aXZlIGxhbmd1YWdlXG4gICAqIEBwYXJhbSBmYWxsYmFja0xhbmdcbiAgICovXG4gIHNldEZhbGxiYWNrTGFuZ0Zvck1pc3NpbmdUcmFuc2xhdGlvbih7XG4gICAgZmFsbGJhY2tMYW5nLFxuICB9OiBQaWNrPFRyYW5zbG9jb0NvbmZpZywgJ2ZhbGxiYWNrTGFuZyc+KSB7XG4gICAgY29uc3QgbGFuZyA9IEFycmF5LmlzQXJyYXkoZmFsbGJhY2tMYW5nKSA/IGZhbGxiYWNrTGFuZ1swXSA6IGZhbGxiYWNrTGFuZztcbiAgICBpZiAoZmFsbGJhY2tMYW5nICYmIHRoaXMudXNlRmFsbGJhY2tUcmFuc2xhdGlvbihsYW5nKSkge1xuICAgICAgdGhpcy5maXJzdEZhbGxiYWNrTGFuZyA9IGxhbmchO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAaW50ZXJuYWxcbiAgICovXG4gIF9oYW5kbGVNaXNzaW5nS2V5KGtleTogc3RyaW5nLCB2YWx1ZTogYW55LCBwYXJhbXM/OiBIYXNoTWFwKSB7XG4gICAgaWYgKHRoaXMuY29uZmlnLm1pc3NpbmdIYW5kbGVyIS5hbGxvd0VtcHR5ICYmIHZhbHVlID09PSAnJykge1xuICAgICAgcmV0dXJuICcnO1xuICAgIH1cblxuICAgIGlmICghdGhpcy5pc1Jlc29sdmVkTWlzc2luZ09uY2UgJiYgdGhpcy51c2VGYWxsYmFja1RyYW5zbGF0aW9uKCkpIHtcbiAgICAgIC8vIFdlIG5lZWQgdG8gc2V0IGl0IHRvIHRydWUgdG8gcHJldmVudCBhIGxvb3BcbiAgICAgIHRoaXMuaXNSZXNvbHZlZE1pc3NpbmdPbmNlID0gdHJ1ZTtcbiAgICAgIGNvbnN0IGZhbGxiYWNrVmFsdWUgPSB0aGlzLnRyYW5zbGF0ZShcbiAgICAgICAga2V5LFxuICAgICAgICBwYXJhbXMsXG4gICAgICAgIHRoaXMuZmlyc3RGYWxsYmFja0xhbmchXG4gICAgICApO1xuICAgICAgdGhpcy5pc1Jlc29sdmVkTWlzc2luZ09uY2UgPSBmYWxzZTtcblxuICAgICAgcmV0dXJuIGZhbGxiYWNrVmFsdWU7XG4gICAgfVxuXG4gICAgcmV0dXJuIHRoaXMubWlzc2luZ0hhbmRsZXIuaGFuZGxlKFxuICAgICAga2V5LFxuICAgICAgdGhpcy5nZXRNaXNzaW5nSGFuZGxlckRhdGEoKSxcbiAgICAgIHBhcmFtc1xuICAgICk7XG4gIH1cblxuICAvKipcbiAgICogQGludGVybmFsXG4gICAqL1xuICBfaXNMYW5nU2NvcGVkKGxhbmc6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLmdldEF2YWlsYWJsZUxhbmdzSWRzKCkuaW5kZXhPZihsYW5nKSA9PT0gLTE7XG4gIH1cblxuICAvKipcbiAgICogQ2hlY2tzIGlmIGEgZ2l2ZW4gc3RyaW5nIGlzIG9uZSBvZiB0aGUgc3BlY2lmaWVkIGF2YWlsYWJsZSBsYW5ndWFnZXMuXG4gICAqIEByZXR1cm5zXG4gICAqIFRydWUgaWYgdGhlIGdpdmVuIHN0cmluZyBpcyBhbiBhdmFpbGFibGUgbGFuZ3VhZ2UuXG4gICAqIEZhbHNlIGlmIHRoZSBnaXZlbiBzdHJpbmcgaXMgbm90IGFuIGF2YWlsYWJsZSBsYW5ndWFnZS5cbiAgICovXG4gIGlzTGFuZyhsYW5nOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICByZXR1cm4gdGhpcy5nZXRBdmFpbGFibGVMYW5nc0lkcygpLmluZGV4T2YobGFuZykgIT09IC0xO1xuICB9XG5cbiAgLyoqXG4gICAqIEBpbnRlcm5hbFxuICAgKlxuICAgKiBXZSBhbHdheXMgd2FudCB0byBtYWtlIHN1cmUgdGhlIGdsb2JhbCBsYW5nIGlzIGxvYWRlZFxuICAgKiBiZWZvcmUgbG9hZGluZyB0aGUgc2NvcGUgc2luY2UgeW91IGNhbiBhY2Nlc3MgYm90aCB2aWEgdGhlIHBpcGUvZGlyZWN0aXZlLlxuICAgKi9cbiAgX2xvYWREZXBlbmRlbmNpZXMoXG4gICAgcGF0aDogc3RyaW5nLFxuICAgIGlubGluZUxvYWRlcj86IElubGluZUxvYWRlclxuICApOiBPYnNlcnZhYmxlPFRyYW5zbGF0aW9uIHwgVHJhbnNsYXRpb25bXT4ge1xuICAgIGNvbnN0IG1haW5MYW5nID0gZ2V0TGFuZ0Zyb21TY29wZShwYXRoKTtcblxuICAgIGlmICh0aGlzLl9pc0xhbmdTY29wZWQocGF0aCkgJiYgIXRoaXMuaXNMb2FkZWRUcmFuc2xhdGlvbihtYWluTGFuZykpIHtcbiAgICAgIHJldHVybiBjb21iaW5lTGF0ZXN0KFtcbiAgICAgICAgdGhpcy5sb2FkKG1haW5MYW5nKSxcbiAgICAgICAgdGhpcy5sb2FkKHBhdGgsIHsgaW5saW5lTG9hZGVyIH0pLFxuICAgICAgXSk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLmxvYWQocGF0aCwgeyBpbmxpbmVMb2FkZXIgfSk7XG4gIH1cblxuICAvKipcbiAgICogQGludGVybmFsXG4gICAqL1xuICBfY29tcGxldGVTY29wZVdpdGhMYW5nKGxhbmdPclNjb3BlOiBzdHJpbmcpIHtcbiAgICBpZiAoXG4gICAgICB0aGlzLl9pc0xhbmdTY29wZWQobGFuZ09yU2NvcGUpICYmXG4gICAgICAhdGhpcy5pc0xhbmcoZ2V0TGFuZ0Zyb21TY29wZShsYW5nT3JTY29wZSkpXG4gICAgKSB7XG4gICAgICByZXR1cm4gYCR7bGFuZ09yU2NvcGV9LyR7dGhpcy5nZXRBY3RpdmVMYW5nKCl9YDtcbiAgICB9XG4gICAgcmV0dXJuIGxhbmdPclNjb3BlO1xuICB9XG5cbiAgLyoqXG4gICAqIEBpbnRlcm5hbFxuICAgKi9cbiAgX3NldFNjb3BlQWxpYXMoc2NvcGU6IHN0cmluZywgYWxpYXM6IHN0cmluZykge1xuICAgIGlmICghdGhpcy5jb25maWcuc2NvcGVNYXBwaW5nKSB7XG4gICAgICB0aGlzLmNvbmZpZy5zY29wZU1hcHBpbmcgPSB7fTtcbiAgICB9XG4gICAgdGhpcy5jb25maWcuc2NvcGVNYXBwaW5nW3Njb3BlXSA9IGFsaWFzO1xuICB9XG5cbiAgbmdPbkRlc3Ryb3koKSB7XG4gICAgaWYgKHRoaXMuc3Vic2NyaXB0aW9uKSB7XG4gICAgICB0aGlzLnN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpO1xuICAgICAgLy8gQ2FyZXRha2VyIG5vdGU6IGl0J3MgaW1wb3J0YW50IHRvIGNsZWFuIHVwIHJlZmVyZW5jZXMgdG8gc3Vic2NyaXB0aW9ucyBzaW5jZSB0aGV5IHNhdmUgdGhlIGBuZXh0YFxuICAgICAgLy8gY2FsbGJhY2sgd2l0aGluIGl0cyBgZGVzdGluYXRpb25gIHByb3BlcnR5LCBwcmV2ZW50aW5nIGNsYXNzZXMgZnJvbSBiZWluZyBHQydkLlxuICAgICAgdGhpcy5zdWJzY3JpcHRpb24gPSBudWxsO1xuICAgIH1cbiAgICAvLyBDYXJldGFrZXIgbm90ZTogc2luY2UgdGhpcyBpcyB0aGUgcm9vdCBwcm92aWRlciwgaXQnbGwgYmUgZGVzdHJveWVkIHdoZW4gdGhlIGBOZ01vZHVsZVJlZi5kZXN0cm95KClgIGlzIHJ1bi5cbiAgICAvLyBDYWNoZWQgdmFsdWVzIGNhcHR1cmUgYHRoaXNgLCB0aHVzIGxlYWRpbmcgdG8gYSBjaXJjdWxhciByZWZlcmVuY2UgYW5kIHByZXZlbnRpbmcgdGhlIGBUcmFuc2xvY29TZXJ2aWNlYCBmcm9tXG4gICAgLy8gYmVpbmcgR0MnZC4gVGhpcyB3b3VsZCBsZWFkIHRvIGEgbWVtb3J5IGxlYWsgd2hlbiBzZXJ2ZXItc2lkZSByZW5kZXJpbmcgaXMgdXNlZCBzaW5jZSB0aGUgc2VydmljZSBpcyBjcmVhdGVkXG4gICAgLy8gYW5kIGRlc3Ryb3llZCBwZXIgZWFjaCBIVFRQIHJlcXVlc3QsIGJ1dCBhbnkgc2VydmljZSBpcyBub3QgZ2V0dGluZyBHQydkLlxuICAgIHRoaXMuY2FjaGUuY2xlYXIoKTtcbiAgfVxuXG4gIHByaXZhdGUgaXNMb2FkZWRUcmFuc2xhdGlvbihsYW5nOiBzdHJpbmcpIHtcbiAgICByZXR1cm4gc2l6ZSh0aGlzLmdldFRyYW5zbGF0aW9uKGxhbmcpKTtcbiAgfVxuXG4gIHByaXZhdGUgZ2V0QXZhaWxhYmxlTGFuZ3NJZHMoKTogc3RyaW5nW10ge1xuICAgIGNvbnN0IGZpcnN0ID0gdGhpcy5nZXRBdmFpbGFibGVMYW5ncygpWzBdO1xuXG4gICAgaWYgKGlzU3RyaW5nKGZpcnN0KSkge1xuICAgICAgcmV0dXJuIHRoaXMuZ2V0QXZhaWxhYmxlTGFuZ3MoKSBhcyBzdHJpbmdbXTtcbiAgICB9XG5cbiAgICByZXR1cm4gKHRoaXMuZ2V0QXZhaWxhYmxlTGFuZ3MoKSBhcyBMYW5nRGVmaW5pdGlvbltdKS5tYXAoKGwpID0+IGwuaWQpO1xuICB9XG5cbiAgcHJpdmF0ZSBnZXRNaXNzaW5nSGFuZGxlckRhdGEoKTogVHJhbnNsb2NvTWlzc2luZ0hhbmRsZXJEYXRhIHtcbiAgICByZXR1cm4ge1xuICAgICAgLi4udGhpcy5jb25maWcsXG4gICAgICBhY3RpdmVMYW5nOiB0aGlzLmdldEFjdGl2ZUxhbmcoKSxcbiAgICAgIGF2YWlsYWJsZUxhbmdzOiB0aGlzLmF2YWlsYWJsZUxhbmdzLFxuICAgICAgZGVmYXVsdExhbmc6IHRoaXMuZGVmYXVsdExhbmcsXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBVc2UgYSBmYWxsYmFjayB0cmFuc2xhdGlvbiBzZXQgZm9yIG1pc3Npbmcga2V5cyBvZiB0aGUgcHJpbWFyeSBsYW5ndWFnZVxuICAgKiBUaGlzIGlzIHVucmVsYXRlZCB0byB0aGUgZmFsbGJhY2sgbGFuZ3VhZ2UgKHdoaWNoIGNoYW5nZXMgdGhlIGFjdGl2ZSBsYW5ndWFnZSlcbiAgICovXG4gIHByaXZhdGUgdXNlRmFsbGJhY2tUcmFuc2xhdGlvbihsYW5nPzogc3RyaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIHRoaXMuY29uZmlnLm1pc3NpbmdIYW5kbGVyIS51c2VGYWxsYmFja1RyYW5zbGF0aW9uICYmXG4gICAgICBsYW5nICE9PSB0aGlzLmZpcnN0RmFsbGJhY2tMYW5nXG4gICAgKTtcbiAgfVxuXG4gIHByaXZhdGUgaGFuZGxlU3VjY2VzcyhsYW5nOiBzdHJpbmcsIHRyYW5zbGF0aW9uOiBUcmFuc2xhdGlvbikge1xuICAgIHRoaXMuc2V0VHJhbnNsYXRpb24odHJhbnNsYXRpb24sIGxhbmcsIHsgZW1pdENoYW5nZTogZmFsc2UgfSk7XG4gICAgdGhpcy5ldmVudHMubmV4dCh7XG4gICAgICB3YXNGYWlsdXJlOiAhIXRoaXMuZmFpbGVkTGFuZ3Muc2l6ZSxcbiAgICAgIHR5cGU6ICd0cmFuc2xhdGlvbkxvYWRTdWNjZXNzJyxcbiAgICAgIHBheWxvYWQ6IGdldEV2ZW50UGF5bG9hZChsYW5nKSxcbiAgICB9KTtcbiAgICB0aGlzLmZhaWxlZExhbmdzLmZvckVhY2goKGwpID0+IHRoaXMuY2FjaGUuZGVsZXRlKGwpKTtcbiAgICB0aGlzLmZhaWxlZExhbmdzLmNsZWFyKCk7XG4gIH1cblxuICBwcml2YXRlIGhhbmRsZUZhaWx1cmUobGFuZzogc3RyaW5nLCBsb2FkT3B0aW9uczogTG9hZE9wdGlvbnMpIHtcbiAgICAvLyBXaGVuIHN0YXJ0aW5nIHRvIGxvYWQgYSBmaXJzdCBjaG9pY2UgbGFuZ3VhZ2UsIGluaXRpYWxpemVcbiAgICAvLyB0aGUgZmFpbGVkIGNvdW50ZXIgYW5kIHJlc29sdmUgdGhlIGZhbGxiYWNrIGxhbmdzLlxuICAgIGlmIChpc05pbChsb2FkT3B0aW9ucy5mYWlsZWRDb3VudGVyKSkge1xuICAgICAgbG9hZE9wdGlvbnMuZmFpbGVkQ291bnRlciA9IDA7XG5cbiAgICAgIGlmICghbG9hZE9wdGlvbnMuZmFsbGJhY2tMYW5ncykge1xuICAgICAgICBsb2FkT3B0aW9ucy5mYWxsYmFja0xhbmdzID0gdGhpcy5mYWxsYmFja1N0cmF0ZWd5LmdldE5leHRMYW5ncyhsYW5nKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBzcGxpdHRlZCA9IGxhbmcuc3BsaXQoJy8nKTtcbiAgICBjb25zdCBmYWxsYmFja3MgPSBsb2FkT3B0aW9ucy5mYWxsYmFja0xhbmdzO1xuICAgIGNvbnN0IG5leHRMYW5nID0gZmFsbGJhY2tzIVtsb2FkT3B0aW9ucy5mYWlsZWRDb3VudGVyIV07XG4gICAgdGhpcy5mYWlsZWRMYW5ncy5hZGQobGFuZyk7XG5cbiAgICAvLyBUaGlzIGhhbmRsZXMgdGhlIGNhc2Ugd2hlcmUgYSBsb2FkZWQgZmFsbGJhY2sgbGFuZ3VhZ2UgaXMgcmVxdWVzdGVkIGFnYWluXG4gICAgaWYgKHRoaXMuY2FjaGUuaGFzKG5leHRMYW5nKSkge1xuICAgICAgdGhpcy5oYW5kbGVTdWNjZXNzKG5leHRMYW5nLCB0aGlzLmdldFRyYW5zbGF0aW9uKG5leHRMYW5nKSk7XG4gICAgICByZXR1cm4gRU1QVFk7XG4gICAgfVxuXG4gICAgY29uc3QgaXNGYWxsYmFja0xhbmcgPSBuZXh0TGFuZyA9PT0gc3BsaXR0ZWRbc3BsaXR0ZWQubGVuZ3RoIC0gMV07XG5cbiAgICBpZiAoIW5leHRMYW5nIHx8IGlzRmFsbGJhY2tMYW5nKSB7XG4gICAgICBsZXQgbXNnID0gYFVuYWJsZSB0byBsb2FkIHRyYW5zbGF0aW9uIGFuZCBhbGwgdGhlIGZhbGxiYWNrIGxhbmd1YWdlc2A7XG4gICAgICBpZiAoc3BsaXR0ZWQubGVuZ3RoID4gMSkge1xuICAgICAgICBtc2cgKz0gYCwgZGlkIHlvdSBtaXNzcGVsbGVkIHRoZSBzY29wZSBuYW1lP2A7XG4gICAgICB9XG5cbiAgICAgIHRocm93IG5ldyBFcnJvcihtc2cpO1xuICAgIH1cblxuICAgIGxldCByZXNvbHZlTGFuZyA9IG5leHRMYW5nO1xuICAgIC8vIGlmIGl0J3Mgc2NvcGVkIGxhbmdcbiAgICBpZiAoc3BsaXR0ZWQubGVuZ3RoID4gMSkge1xuICAgICAgLy8gV2UgbmVlZCB0byByZXNvbHZlIGl0IHRvOlxuICAgICAgLy8gdG9kb3MvbGFuZ05vdEV4aXN0cyA9PiB0b2Rvcy9uZXh0TGFuZ1xuICAgICAgc3BsaXR0ZWRbc3BsaXR0ZWQubGVuZ3RoIC0gMV0gPSBuZXh0TGFuZztcbiAgICAgIHJlc29sdmVMYW5nID0gc3BsaXR0ZWQuam9pbignLycpO1xuICAgIH1cblxuICAgIGxvYWRPcHRpb25zLmZhaWxlZENvdW50ZXIhKys7XG4gICAgdGhpcy5ldmVudHMubmV4dCh7XG4gICAgICB0eXBlOiAndHJhbnNsYXRpb25Mb2FkRmFpbHVyZScsXG4gICAgICBwYXlsb2FkOiBnZXRFdmVudFBheWxvYWQobGFuZyksXG4gICAgfSk7XG5cbiAgICByZXR1cm4gdGhpcy5sb2FkKHJlc29sdmVMYW5nLCBsb2FkT3B0aW9ucyk7XG4gIH1cblxuICBwcml2YXRlIGdldE1hcHBlZFNjb3BlKHNjb3BlOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IHsgc2NvcGVNYXBwaW5nID0ge30gfSA9IHRoaXMuY29uZmlnO1xuICAgIHJldHVybiBzY29wZU1hcHBpbmdbc2NvcGVdIHx8IHRvQ2FtZWxDYXNlKHNjb3BlKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBJZiBsYW5nIGlzIHNjb3BlIHdlIG5lZWQgdG8gY2hlY2sgdGhlIGZvbGxvd2luZyBjYXNlczpcbiAgICogdG9kb3MvZXMgPT4gaW4gdGhpcyBjYXNlIHdlIHNob3VsZCB0YWtlIGBlc2AgYXMgbGFuZ1xuICAgKiB0b2RvcyA9PiBpbiB0aGlzIGNhc2Ugd2Ugc2hvdWxkIHNldCB0aGUgYWN0aXZlIGxhbmcgYXMgbGFuZ1xuICAgKi9cbiAgcHJpdmF0ZSByZXNvbHZlTGFuZ0FuZFNjb3BlKGxhbmc6IHN0cmluZykge1xuICAgIGxldCByZXNvbHZlTGFuZyA9IGxhbmc7XG4gICAgbGV0IHNjb3BlO1xuXG4gICAgaWYgKHRoaXMuX2lzTGFuZ1Njb3BlZChsYW5nKSkge1xuICAgICAgLy8gZW4gZm9yIGV4YW1wbGVcbiAgICAgIGNvbnN0IGxhbmdGcm9tU2NvcGUgPSBnZXRMYW5nRnJvbVNjb3BlKGxhbmcpO1xuICAgICAgLy8gZW4gaXMgbGFuZ1xuICAgICAgY29uc3QgaGFzTGFuZyA9IHRoaXMuaXNMYW5nKGxhbmdGcm9tU2NvcGUpO1xuICAgICAgLy8gdGFrZSBlblxuICAgICAgcmVzb2x2ZUxhbmcgPSBoYXNMYW5nID8gbGFuZ0Zyb21TY29wZSA6IHRoaXMuZ2V0QWN0aXZlTGFuZygpO1xuICAgICAgLy8gZmluZCB0aGUgc2NvcGVcbiAgICAgIHNjb3BlID0gdGhpcy5nZXRNYXBwZWRTY29wZShoYXNMYW5nID8gZ2V0U2NvcGVGcm9tTGFuZyhsYW5nKSA6IGxhbmcpO1xuICAgIH1cblxuICAgIHJldHVybiB7IHNjb3BlLCByZXNvbHZlTGFuZyB9O1xuICB9XG5cbiAgcHJpdmF0ZSBnZXRPYmplY3RCeUtleSh0cmFuc2xhdGlvbjogVHJhbnNsYXRpb24sIGtleT86IHN0cmluZykge1xuICAgIGNvbnN0IHJlc3VsdDogVHJhbnNsYXRpb24gPSB7fTtcbiAgICBjb25zdCBwcmVmaXggPSBgJHtrZXl9LmA7XG5cbiAgICBmb3IgKGNvbnN0IGN1cnJlbnRLZXkgaW4gdHJhbnNsYXRpb24pIHtcbiAgICAgIGlmIChjdXJyZW50S2V5LnN0YXJ0c1dpdGgocHJlZml4KSkge1xuICAgICAgICByZXN1bHRbY3VycmVudEtleS5yZXBsYWNlKHByZWZpeCwgJycpXSA9IHRyYW5zbGF0aW9uW2N1cnJlbnRLZXldO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cblxuICBwcml2YXRlIGdldEVudHJpZXMoa2V5OiBIYXNoTWFwIHwgTWFwPHN0cmluZywgSGFzaE1hcD4pIHtcbiAgICByZXR1cm4ga2V5IGluc3RhbmNlb2YgTWFwID8ga2V5LmVudHJpZXMoKSA6IE9iamVjdC5lbnRyaWVzKGtleSk7XG4gIH1cbn1cbiJdfQ==