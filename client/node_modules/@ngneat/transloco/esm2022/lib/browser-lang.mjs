import { isBrowser } from './helpers';
/**
 * Returns the language code name from the browser, e.g. "en"
 */
export function getBrowserLang() {
    let browserLang = getBrowserCultureLang();
    if (!browserLang || !isBrowser()) {
        return undefined;
    }
    if (browserLang.indexOf('-') !== -1) {
        browserLang = browserLang.split('-')[0];
    }
    if (browserLang.indexOf('_') !== -1) {
        browserLang = browserLang.split('_')[0];
    }
    return browserLang;
}
/**
 * Returns the culture language code name from the browser, e.g. "en-US"
 */
export function getBrowserCultureLang() {
    if (!isBrowser()) {
        return '';
    }
    const navigator = window.navigator;
    return navigator.languages?.[0] ?? navigator.language;
}
//# sourceMappingURL=data:application/json;base64,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