import { TemplateRef } from '@angular/core';
import { isString } from './helpers';
import { TranslocoLoaderComponent } from './loader-component.component';
export class TemplateHandler {
    view;
    vcr;
    constructor(view, vcr) {
        this.view = view;
        this.vcr = vcr;
    }
    attachView() {
        if (this.view instanceof TemplateRef) {
            this.vcr.createEmbeddedView(this.view);
        }
        else if (isString(this.view)) {
            const componentRef = this.vcr.createComponent(TranslocoLoaderComponent);
            componentRef.instance.html = this.view;
            componentRef.hostView.detectChanges();
        }
        else {
            this.vcr.createComponent(this.view);
        }
    }
    detachView() {
        this.vcr.clear();
    }
}
//# sourceMappingURL=data:application/json;base64,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