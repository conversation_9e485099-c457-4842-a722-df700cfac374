import { Inject, Injectable, InjectionToken } from '@angular/core';
import { TRANSLOCO_CONFIG } from './transloco.config';
import * as i0 from "@angular/core";
export const TRANSLOCO_FALLBACK_STRATEGY = new InjectionToken('TRANSLOCO_FALLBACK_STRATEGY');
export class DefaultFallbackStrategy {
    userConfig;
    constructor(userConfig) {
        this.userConfig = userConfig;
    }
    getNextLangs() {
        const fallbackLang = this.userConfig.fallbackLang;
        if (!fallbackLang) {
            throw new Error('When using the default fallback, a fallback language must be provided in the config!');
        }
        return Array.isArray(fallbackLang) ? fallbackLang : [fallbackLang];
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: DefaultFallbackStrategy, deps: [{ token: TRANSLOCO_CONFIG }], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: DefaultFallbackStrategy });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: DefaultFallbackStrategy, decorators: [{
            type: Injectable
        }], ctorParameters: function () { return [{ type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_CONFIG]
                }] }]; } });
//# sourceMappingURL=data:application/json;base64,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