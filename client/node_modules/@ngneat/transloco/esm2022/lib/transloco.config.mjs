import { InjectionToken } from '@angular/core';
export const TRANSLOCO_CONFIG = new InjectionToken('TRANSLOCO_CONFIG', {
    providedIn: 'root',
    factory: () => defaultConfig,
});
export const defaultConfig = {
    defaultLang: 'en',
    reRenderOnLangChange: false,
    prodMode: false,
    failedRetries: 2,
    fallbackLang: [],
    availableLangs: [],
    missingHandler: {
        logMissingKey: true,
        useFallbackTranslation: false,
        allowEmpty: false,
    },
    flatten: {
        aot: false,
    },
    interpolation: ['{{', '}}'],
};
export function translocoConfig(config = {}) {
    return {
        ...defaultConfig,
        ...config,
        missingHandler: {
            ...defaultConfig.missingHandler,
            ...config.missingHandler,
        },
        flatten: {
            ...defaultConfig.flatten,
            ...config.flatten,
        },
    };
}
//# sourceMappingURL=data:application/json;base64,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