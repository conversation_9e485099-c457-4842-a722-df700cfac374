import { from, map } from 'rxjs';
import { resolveLoader } from './resolve-loader';
export function getFallbacksLoaders({ mainLoader, path, data, fallbackPath, inlineLoader, }) {
    const paths = fallbackPath ? [path, fallbackPath] : [path];
    return paths.map((path) => {
        const loader = resolveLoader({ path, mainLoader, inlineLoader, data });
        return from(loader).pipe(map((translation) => ({
            translation,
            lang: path,
        })));
    });
}
//# sourceMappingURL=data:application/json;base64,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