import { isFunction } from './helpers';
export function resolveLoader(options) {
    const { path, inlineLoader, mainLoader, data } = options;
    if (inlineLoader) {
        const pathLoader = inlineLoader[path];
        if (isFunction(pathLoader) === false) {
            throw `You're using an inline loader but didn't provide a loader for ${path}`;
        }
        return inlineLoader[path]().then((res) => res.default ? res.default : res);
    }
    return mainLoader.getTranslation(path, data);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmVzb2x2ZS1sb2FkZXIuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9saWJzL3RyYW5zbG9jby9zcmMvbGliL3Jlc29sdmUtbG9hZGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBLE9BQU8sRUFBRSxVQUFVLEVBQUUsTUFBTSxXQUFXLENBQUM7QUFTdkMsTUFBTSxVQUFVLGFBQWEsQ0FBQyxPQUFnQjtJQUM1QyxNQUFNLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxVQUFVLEVBQUUsSUFBSSxFQUFFLEdBQUcsT0FBTyxDQUFDO0lBRXpELElBQUksWUFBWSxFQUFFO1FBQ2hCLE1BQU0sVUFBVSxHQUFHLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN0QyxJQUFJLFVBQVUsQ0FBQyxVQUFVLENBQUMsS0FBSyxLQUFLLEVBQUU7WUFDcEMsTUFBTSxpRUFBaUUsSUFBSSxFQUFFLENBQUM7U0FDL0U7UUFFRCxPQUFPLFlBQVksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRSxFQUFFLENBQ3ZDLEdBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FDaEMsQ0FBQztLQUNIO0lBRUQsT0FBTyxVQUFVLENBQUMsY0FBYyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQztBQUMvQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVHJhbnNsb2NvTG9hZGVyLCBUcmFuc2xvY29Mb2FkZXJEYXRhIH0gZnJvbSAnLi90cmFuc2xvY28ubG9hZGVyJztcbmltcG9ydCB7IElubGluZUxvYWRlciB9IGZyb20gJy4vdHlwZXMnO1xuaW1wb3J0IHsgaXNGdW5jdGlvbiB9IGZyb20gJy4vaGVscGVycyc7XG5cbmludGVyZmFjZSBPcHRpb25zIHtcbiAgaW5saW5lTG9hZGVyPzogSW5saW5lTG9hZGVyO1xuICBwYXRoOiBzdHJpbmc7XG4gIG1haW5Mb2FkZXI6IFRyYW5zbG9jb0xvYWRlcjtcbiAgZGF0YT86IFRyYW5zbG9jb0xvYWRlckRhdGE7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiByZXNvbHZlTG9hZGVyKG9wdGlvbnM6IE9wdGlvbnMpIHtcbiAgY29uc3QgeyBwYXRoLCBpbmxpbmVMb2FkZXIsIG1haW5Mb2FkZXIsIGRhdGEgfSA9IG9wdGlvbnM7XG5cbiAgaWYgKGlubGluZUxvYWRlcikge1xuICAgIGNvbnN0IHBhdGhMb2FkZXIgPSBpbmxpbmVMb2FkZXJbcGF0aF07XG4gICAgaWYgKGlzRnVuY3Rpb24ocGF0aExvYWRlcikgPT09IGZhbHNlKSB7XG4gICAgICB0aHJvdyBgWW91J3JlIHVzaW5nIGFuIGlubGluZSBsb2FkZXIgYnV0IGRpZG4ndCBwcm92aWRlIGEgbG9hZGVyIGZvciAke3BhdGh9YDtcbiAgICB9XG5cbiAgICByZXR1cm4gaW5saW5lTG9hZGVyW3BhdGhdKCkudGhlbigocmVzKSA9PlxuICAgICAgcmVzLmRlZmF1bHQgPyByZXMuZGVmYXVsdCA6IHJlc1xuICAgICk7XG4gIH1cblxuICByZXR1cm4gbWFpbkxvYWRlci5nZXRUcmFuc2xhdGlvbihwYXRoLCBkYXRhKTtcbn1cbiJdfQ==