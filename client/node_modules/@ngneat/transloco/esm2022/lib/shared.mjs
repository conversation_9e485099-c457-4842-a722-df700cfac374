import { take } from 'rxjs';
import { hasInlineLoader, isString } from './helpers';
/*
 * @example
 *
 * given: lazy-page/en => lazy-page
 *
 */
export function getScopeFromLang(lang) {
    if (!lang) {
        return '';
    }
    const split = lang.split('/');
    split.pop();
    return split.join('/');
}
/*
 * @example
 *
 * given: lazy-page/en => en
 *
 */
export function getLangFromScope(lang) {
    if (!lang) {
        return '';
    }
    return lang.split('/').pop();
}
/**
 * @example
 *
 * getPipeValue('todos|scoped', 'scoped') [true, 'todos']
 * getPipeValue('en|static', 'static') [true, 'en']
 * getPipeValue('en', 'static') [false, 'en']
 */
export function getPipeValue(str, value, char = '|') {
    if (isString(str)) {
        const splitted = str.split(char);
        const lastItem = splitted.pop();
        return lastItem === value ? [true, splitted.toString()] : [false, lastItem];
    }
    return [false, ''];
}
export function shouldListenToLangChanges(service, lang) {
    const [hasStatic] = getPipeValue(lang, 'static');
    if (!hasStatic) {
        // If we didn't get 'lang|static' check if it's set in the global level
        return !!service.config.reRenderOnLangChange;
    }
    // We have 'lang|static' so don't listen to lang changes
    return false;
}
export function listenOrNotOperator(listenToLangChange) {
    return listenToLangChange ? (source) => source : take(1);
}
function prependScope(inlineLoader, scope) {
    return Object.keys(inlineLoader).reduce((acc, lang) => {
        acc[`${scope}/${lang}`] = inlineLoader[lang];
        return acc;
    }, {});
}
export function resolveInlineLoader(providerScope, scope) {
    return hasInlineLoader(providerScope)
        ? prependScope(providerScope.loader, scope)
        : undefined;
}
export function getEventPayload(lang) {
    return {
        scope: getScopeFromLang(lang) || null,
        langName: getLangFromScope(lang),
    };
}
//# sourceMappingURL=data:application/json;base64,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