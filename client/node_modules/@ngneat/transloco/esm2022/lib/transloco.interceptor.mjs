import { InjectionToken, Injectable } from '@angular/core';
import * as i0 from "@angular/core";
export const TRANSLOCO_INTERCEPTOR = new InjectionToken('TRANSLOCO_INTERCEPTOR');
export class DefaultInterceptor {
    preSaveTranslation(translation) {
        return translation;
    }
    preSaveTranslationKey(_, value) {
        return value;
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: DefaultInterceptor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: DefaultInterceptor });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: DefaultInterceptor, decorators: [{
            type: Injectable
        }] });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHJhbnNsb2NvLmludGVyY2VwdG9yLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vbGlicy90cmFuc2xvY28vc3JjL2xpYi90cmFuc2xvY28uaW50ZXJjZXB0b3IudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsT0FBTyxFQUFFLGNBQWMsRUFBRSxVQUFVLEVBQUUsTUFBTSxlQUFlLENBQUM7O0FBSTNELE1BQU0sQ0FBQyxNQUFNLHFCQUFxQixHQUFHLElBQUksY0FBYyxDQUNyRCx1QkFBdUIsQ0FDeEIsQ0FBQztBQVNGLE1BQU0sT0FBTyxrQkFBa0I7SUFDN0Isa0JBQWtCLENBQUMsV0FBd0I7UUFDekMsT0FBTyxXQUFXLENBQUM7SUFDckIsQ0FBQztJQUVELHFCQUFxQixDQUFDLENBQVMsRUFBRSxLQUFhO1FBQzVDLE9BQU8sS0FBSyxDQUFDO0lBQ2YsQ0FBQzt1R0FQVSxrQkFBa0I7MkdBQWxCLGtCQUFrQjs7MkZBQWxCLGtCQUFrQjtrQkFEOUIsVUFBVSIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEluamVjdGlvblRva2VuLCBJbmplY3RhYmxlIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XG5cbmltcG9ydCB7IFRyYW5zbGF0aW9uIH0gZnJvbSAnLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBUUkFOU0xPQ09fSU5URVJDRVBUT1IgPSBuZXcgSW5qZWN0aW9uVG9rZW48VHJhbnNsb2NvSW50ZXJjZXB0b3I+KFxuICAnVFJBTlNMT0NPX0lOVEVSQ0VQVE9SJ1xuKTtcblxuZXhwb3J0IGludGVyZmFjZSBUcmFuc2xvY29JbnRlcmNlcHRvciB7XG4gIHByZVNhdmVUcmFuc2xhdGlvbih0cmFuc2xhdGlvbjogVHJhbnNsYXRpb24sIGxhbmc6IHN0cmluZyk6IFRyYW5zbGF0aW9uO1xuXG4gIHByZVNhdmVUcmFuc2xhdGlvbktleShrZXk6IHN0cmluZywgdmFsdWU6IHN0cmluZywgbGFuZzogc3RyaW5nKTogc3RyaW5nO1xufVxuXG5ASW5qZWN0YWJsZSgpXG5leHBvcnQgY2xhc3MgRGVmYXVsdEludGVyY2VwdG9yIGltcGxlbWVudHMgVHJhbnNsb2NvSW50ZXJjZXB0b3Ige1xuICBwcmVTYXZlVHJhbnNsYXRpb24odHJhbnNsYXRpb246IFRyYW5zbGF0aW9uKTogVHJhbnNsYXRpb24ge1xuICAgIHJldHVybiB0cmFuc2xhdGlvbjtcbiAgfVxuXG4gIHByZVNhdmVUcmFuc2xhdGlvbktleShfOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIHJldHVybiB2YWx1ZTtcbiAgfVxufVxuIl19