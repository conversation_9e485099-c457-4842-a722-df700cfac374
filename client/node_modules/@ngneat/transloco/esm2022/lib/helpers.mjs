import { flatten as _flatten, unflatten as _unflatten } from 'flat';
export function getValue(obj, path) {
    if (!obj) {
        return obj;
    }
    /* For cases where the key is like: 'general.something.thing' */
    if (Object.prototype.hasOwnProperty.call(obj, path)) {
        return obj[path];
    }
    return path.split('.').reduce((p, c) => p?.[c], obj);
}
export function setValue(obj, prop, val) {
    obj = { ...obj };
    const split = prop.split('.');
    const lastIndex = split.length - 1;
    split.reduce((acc, part, index) => {
        if (index === lastIndex) {
            acc[part] = val;
        }
        else {
            acc[part] = Array.isArray(acc[part])
                ? acc[part].slice()
                : { ...acc[part] };
        }
        return acc && acc[part];
    }, obj);
    return obj;
}
export function size(collection) {
    if (!collection) {
        return 0;
    }
    if (Array.isArray(collection)) {
        return collection.length;
    }
    if (isObject(collection)) {
        return Object.keys(collection).length;
    }
    return collection ? collection.length : 0;
}
export function isEmpty(collection) {
    return size(collection) === 0;
}
export function isFunction(val) {
    return typeof val === 'function';
}
export function isString(val) {
    return typeof val === 'string';
}
export function isNumber(val) {
    return typeof val === 'number';
}
export function isObject(item) {
    return !!item && typeof item === 'object' && !Array.isArray(item);
}
export function coerceArray(value) {
    return Array.isArray(value) ? value : [value];
}
/*
 * @example
 *
 * given: path-to-happiness => pathToHappiness
 * given: path_to_happiness => pathToHappiness
 * given: path-to_happiness => pathToHappiness
 *
 */
export function toCamelCase(str) {
    return str
        .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => index == 0 ? word.toLowerCase() : word.toUpperCase())
        .replace(/\s+|_|-|\//g, '');
}
export function isBrowser() {
    return typeof window !== 'undefined';
}
export function isNil(value) {
    return value === null || value === undefined;
}
export function isDefined(value) {
    return isNil(value) === false;
}
export function toNumber(value) {
    if (isNumber(value))
        return value;
    if (isString(value) && !isNaN(Number(value) - parseFloat(value))) {
        return Number(value);
    }
    return null;
}
export function isScopeObject(item) {
    return item && typeof item.scope === 'string';
}
export function isScopeArray(item) {
    return Array.isArray(item) && item.every(isScopeObject);
}
export function hasInlineLoader(item) {
    return item && isObject(item.loader);
}
export function unflatten(obj) {
    return _unflatten(obj);
}
export function flatten(obj) {
    return _flatten(obj, { safe: true });
}
//# sourceMappingURL=data:application/json;base64,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