import { makeEnvironmentProviders, } from '@angular/core';
import { TRANSLOCO_LOADER } from './transloco.loader';
import { TRANSLOCO_CONFIG, translocoConfig, } from './transloco.config';
import { TRANSLOCO_SCOPE } from './transloco-scope';
import { DefaultTranspiler, TRANSLOCO_TRANSPILER, } from './transloco.transpiler';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRANSLOCO_MISSING_HANDLER, } from './transloco-missing-handler';
import { DefaultInterceptor, TRANSLOCO_INTERCEPTOR, } from './transloco.interceptor';
import { DefaultFallbackStrategy, TRANSLOCO_FALLBACK_STRATEGY, } from './transloco-fallback-strategy';
import { TRANSLOCO_LOADING_TEMPLATE } from './transloco-loading-template';
import { TRANSLOCO_LANG } from './transloco-lang';
export function provideTransloco(options) {
    const providers = [
        provideTranslocoTranspiler(DefaultTranspiler),
        provideTransloco<PERSON><PERSON>ing<PERSON>and<PERSON>(DefaultHandler),
        provideTranslocoInterceptor(DefaultInterceptor),
        provideTranslocoFallbackStrategy(DefaultFallbackStrategy),
    ];
    if (options.config) {
        providers.push(provideTranslocoConfig(options.config));
    }
    if (options.loader) {
        providers.push(provideTranslocoLoader(options.loader));
    }
    return providers;
}
export function provideTranslocoConfig(config) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_CONFIG,
            useValue: translocoConfig(config),
        },
    ]);
}
export function provideTranslocoLoader(loader) {
    return makeEnvironmentProviders([
        { provide: TRANSLOCO_LOADER, useClass: loader },
    ]);
}
export function provideTranslocoScope(scope) {
    return {
        provide: TRANSLOCO_SCOPE,
        useValue: scope,
        multi: true,
    };
}
export function provideTranslocoLoadingTpl(content) {
    return {
        provide: TRANSLOCO_LOADING_TEMPLATE,
        useValue: content,
    };
}
export function provideTranslocoTranspiler(transpiler) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_TRANSPILER,
            useClass: transpiler,
            deps: [TRANSLOCO_CONFIG],
        },
    ]);
}
export function provideTranslocoFallbackStrategy(strategy) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_FALLBACK_STRATEGY,
            useClass: strategy,
            deps: [TRANSLOCO_CONFIG],
        },
    ]);
}
export function provideTranslocoMissingHandler(handler) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_MISSING_HANDLER,
            useClass: handler,
        },
    ]);
}
export function provideTranslocoInterceptor(interceptor) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_INTERCEPTOR,
            useClass: interceptor,
        },
    ]);
}
export function provideTranslocoLang(lang) {
    return {
        provide: TRANSLOCO_LANG,
        useValue: lang,
    };
}
//# sourceMappingURL=data:application/json;base64,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