import { InjectionToken, Injectable } from '@angular/core';
import * as i0 from "@angular/core";
export const TRANSLOCO_MISSING_HANDLER = new InjectionToken('TRANSLOCO_MISSING_HANDLER');
export class DefaultHandler {
    handle(key, config) {
        if (config.missingHandler.logMissingKey && !config.prodMode) {
            const msg = `Missing translation for '${key}'`;
            console.warn(`%c ${msg}`, 'font-size: 12px; color: red');
        }
        return key;
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: Default<PERSON><PERSON><PERSON>, deps: [], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: Default<PERSON><PERSON><PERSON> });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: Default<PERSON><PERSON><PERSON>, decorators: [{
            type: Injectable
        }] });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHJhbnNsb2NvLW1pc3NpbmctaGFuZGxlci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uL2xpYnMvdHJhbnNsb2NvL3NyYy9saWIvdHJhbnNsb2NvLW1pc3NpbmctaGFuZGxlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxPQUFPLEVBQUUsY0FBYyxFQUFFLFVBQVUsRUFBRSxNQUFNLGVBQWUsQ0FBQzs7QUFLM0QsTUFBTSxDQUFDLE1BQU0seUJBQXlCLEdBQ3BDLElBQUksY0FBYyxDQUE4QiwyQkFBMkIsQ0FBQyxDQUFDO0FBVy9FLE1BQU0sT0FBTyxjQUFjO0lBQ3pCLE1BQU0sQ0FBQyxHQUFXLEVBQUUsTUFBdUI7UUFDekMsSUFBSSxNQUFNLENBQUMsY0FBYyxDQUFDLGFBQWEsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLEVBQUU7WUFDM0QsTUFBTSxHQUFHLEdBQUcsNEJBQTRCLEdBQUcsR0FBRyxDQUFDO1lBQy9DLE9BQU8sQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLEVBQUUsRUFBRSw2QkFBNkIsQ0FBQyxDQUFDO1NBQzFEO1FBRUQsT0FBTyxHQUFHLENBQUM7SUFDYixDQUFDO3VHQVJVLGNBQWM7MkdBQWQsY0FBYzs7MkZBQWQsY0FBYztrQkFEMUIsVUFBVSIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEluamVjdGlvblRva2VuLCBJbmplY3RhYmxlIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XG5cbmltcG9ydCB7IFRyYW5zbG9jb0NvbmZpZyB9IGZyb20gJy4vdHJhbnNsb2NvLmNvbmZpZyc7XG5pbXBvcnQgeyBIYXNoTWFwIH0gZnJvbSAnLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBUUkFOU0xPQ09fTUlTU0lOR19IQU5ETEVSID1cbiAgbmV3IEluamVjdGlvblRva2VuPFRyYW5zbG9jb01pc3NpbmdIYW5kbGVyRGF0YT4oJ1RSQU5TTE9DT19NSVNTSU5HX0hBTkRMRVInKTtcblxuZXhwb3J0IGludGVyZmFjZSBUcmFuc2xvY29NaXNzaW5nSGFuZGxlckRhdGEgZXh0ZW5kcyBUcmFuc2xvY29Db25maWcge1xuICBhY3RpdmVMYW5nOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVHJhbnNsb2NvTWlzc2luZ0hhbmRsZXIge1xuICBoYW5kbGUoa2V5OiBzdHJpbmcsIGRhdGE6IFRyYW5zbG9jb01pc3NpbmdIYW5kbGVyRGF0YSwgcGFyYW1zPzogSGFzaE1hcCk6IGFueTtcbn1cblxuQEluamVjdGFibGUoKVxuZXhwb3J0IGNsYXNzIERlZmF1bHRIYW5kbGVyIGltcGxlbWVudHMgVHJhbnNsb2NvTWlzc2luZ0hhbmRsZXIge1xuICBoYW5kbGUoa2V5OiBzdHJpbmcsIGNvbmZpZzogVHJhbnNsb2NvQ29uZmlnKSB7XG4gICAgaWYgKGNvbmZpZy5taXNzaW5nSGFuZGxlci5sb2dNaXNzaW5nS2V5ICYmICFjb25maWcucHJvZE1vZGUpIHtcbiAgICAgIGNvbnN0IG1zZyA9IGBNaXNzaW5nIHRyYW5zbGF0aW9uIGZvciAnJHtrZXl9J2A7XG4gICAgICBjb25zb2xlLndhcm4oYCVjICR7bXNnfWAsICdmb250LXNpemU6IDEycHg7IGNvbG9yOiByZWQnKTtcbiAgICB9XG5cbiAgICByZXR1cm4ga2V5O1xuICB9XG59XG4iXX0=