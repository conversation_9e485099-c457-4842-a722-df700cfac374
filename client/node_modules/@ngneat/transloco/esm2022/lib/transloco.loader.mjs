import { InjectionToken } from '@angular/core';
import { of } from 'rxjs';
export class DefaultLoader {
    translations;
    constructor(translations) {
        this.translations = translations;
    }
    getTranslation(lang) {
        return of(this.translations.get(lang) || {});
    }
}
export const TRANSLOCO_LOADER = new InjectionToken('TRANSLOCO_LOADER');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHJhbnNsb2NvLmxvYWRlci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uL2xpYnMvdHJhbnNsb2NvL3NyYy9saWIvdHJhbnNsb2NvLmxvYWRlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxPQUFPLEVBQUUsY0FBYyxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBQy9DLE9BQU8sRUFBYyxFQUFFLEVBQUUsTUFBTSxNQUFNLENBQUM7QUFldEMsTUFBTSxPQUFPLGFBQWE7SUFDSjtJQUFwQixZQUFvQixZQUFzQztRQUF0QyxpQkFBWSxHQUFaLFlBQVksQ0FBMEI7SUFBRyxDQUFDO0lBRTlELGNBQWMsQ0FBQyxJQUFZO1FBQ3pCLE9BQU8sRUFBRSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO0lBQy9DLENBQUM7Q0FDRjtBQUVELE1BQU0sQ0FBQyxNQUFNLGdCQUFnQixHQUFHLElBQUksY0FBYyxDQUNoRCxrQkFBa0IsQ0FDbkIsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEluamVjdGlvblRva2VuIH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XG5pbXBvcnQgeyBPYnNlcnZhYmxlLCBvZiB9IGZyb20gJ3J4anMnO1xuXG5pbXBvcnQgeyBUcmFuc2xhdGlvbiB9IGZyb20gJy4vdHlwZXMnO1xuXG5leHBvcnQgaW50ZXJmYWNlIFRyYW5zbG9jb0xvYWRlciB7XG4gIGdldFRyYW5zbGF0aW9uKFxuICAgIGxhbmc6IHN0cmluZyxcbiAgICBkYXRhPzogVHJhbnNsb2NvTG9hZGVyRGF0YVxuICApOiBPYnNlcnZhYmxlPFRyYW5zbGF0aW9uPiB8IFByb21pc2U8VHJhbnNsYXRpb24+O1xufVxuXG5leHBvcnQgdHlwZSBUcmFuc2xvY29Mb2FkZXJEYXRhID0ge1xuICBzY29wZTogc3RyaW5nO1xufTtcblxuZXhwb3J0IGNsYXNzIERlZmF1bHRMb2FkZXIgaW1wbGVtZW50cyBUcmFuc2xvY29Mb2FkZXIge1xuICBjb25zdHJ1Y3Rvcihwcml2YXRlIHRyYW5zbGF0aW9uczogTWFwPHN0cmluZywgVHJhbnNsYXRpb24+KSB7fVxuXG4gIGdldFRyYW5zbGF0aW9uKGxhbmc6IHN0cmluZyk6IE9ic2VydmFibGU8VHJhbnNsYXRpb24+IHtcbiAgICByZXR1cm4gb2YodGhpcy50cmFuc2xhdGlvbnMuZ2V0KGxhbmcpIHx8IHt9KTtcbiAgfVxufVxuXG5leHBvcnQgY29uc3QgVFJBTlNMT0NPX0xPQURFUiA9IG5ldyBJbmplY3Rpb25Ub2tlbjxUcmFuc2xvY29Mb2FkZXI+KFxuICAnVFJBTlNMT0NPX0xPQURFUidcbik7XG4iXX0=