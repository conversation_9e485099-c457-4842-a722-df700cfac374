import { APP_INITIALIZER, Inject, Injectable, InjectionToken, NgModule, } from '@angular/core';
import { of } from 'rxjs';
import { TranslocoModule } from './transloco.module';
import { provideTransloco } from './transloco.providers';
import { TranslocoService } from './transloco.service';
import * as i0 from "@angular/core";
const TRANSLOCO_TEST_LANGS = new InjectionToken('TRANSLOCO_TEST_LANGS - Available testing languages');
const TRANSLOCO_TEST_OPTIONS = new InjectionToken('TRANSLOCO_TEST_OPTIONS - Testing options');
export class TestingLoader {
    langs;
    constructor(langs) {
        this.langs = langs;
    }
    getTranslation(lang) {
        return of(this.langs[lang]);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TestingLoader, deps: [{ token: TRANSLOCO_TEST_LANGS }], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TestingLoader });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TestingLoader, decorators: [{
            type: Injectable
        }], ctorParameters: function () { return [{ type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_TEST_LANGS]
                }] }]; } });
export function initTranslocoService(service, langs = {}, options) {
    const preloadAllLangs = () => options.preloadLangs
        ? Promise.all(Object.keys(langs).map((lang) => service.load(lang).toPromise()))
        : Promise.resolve();
    return preloadAllLangs;
}
export class TranslocoTestingModule {
    static forRoot(options) {
        return {
            ngModule: TranslocoTestingModule,
            providers: [
                provideTransloco({
                    loader: TestingLoader,
                    config: {
                        prodMode: true,
                        missingHandler: { logMissingKey: false },
                        ...options.translocoConfig,
                    },
                }),
                {
                    provide: TRANSLOCO_TEST_LANGS,
                    useValue: options.langs,
                },
                {
                    provide: TRANSLOCO_TEST_OPTIONS,
                    useValue: options,
                },
                {
                    provide: APP_INITIALIZER,
                    useFactory: initTranslocoService,
                    deps: [
                        TranslocoService,
                        TRANSLOCO_TEST_LANGS,
                        TRANSLOCO_TEST_OPTIONS,
                    ],
                    multi: true,
                },
            ],
        };
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });
    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: "14.0.0", version: "16.1.7", ngImport: i0, type: TranslocoTestingModule, exports: [TranslocoModule] });
    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoTestingModule, imports: [TranslocoModule] });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoTestingModule, decorators: [{
            type: NgModule,
            args: [{
                    exports: [TranslocoModule],
                }]
        }] });
//# sourceMappingURL=data:application/json;base64,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