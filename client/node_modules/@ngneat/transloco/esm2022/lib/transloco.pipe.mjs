import { ChangeDetectorRef, Inject, Optional, Pipe, } from '@angular/core';
import { switchMap, forkJoin } from 'rxjs';
import { TranslocoService } from './transloco.service';
import { TRANSLOCO_SCOPE } from './transloco-scope';
import { TRANSLOCO_LANG } from './transloco-lang';
import { listenOrNotOperator, resolveInlineLoader, shouldListenToLangChanges, } from './shared';
import { LangResolver } from './lang-resolver';
import { ScopeResolver } from './scope-resolver';
import * as i0 from "@angular/core";
import * as i1 from "./transloco.service";
export class TranslocoPipe {
    service;
    providerScope;
    providerLang;
    cdr;
    subscription = null;
    lastValue = '';
    lastKey;
    path;
    langResolver = new LangResolver();
    scopeResolver;
    constructor(service, providerScope, providerLang, cdr) {
        this.service = service;
        this.providerScope = providerScope;
        this.providerLang = providerLang;
        this.cdr = cdr;
        this.scopeResolver = new ScopeResolver(this.service);
    }
    // null is for handling strict mode + async pipe types https://github.com/ngneat/transloco/issues/311
    // null is for handling strict mode + optional chaining types https://github.com/ngneat/transloco/issues/488
    transform(key, params, inlineLang) {
        if (!key) {
            return key;
        }
        const keyName = params ? `${key}${JSON.stringify(params)}` : key;
        if (keyName === this.lastKey) {
            return this.lastValue;
        }
        this.lastKey = keyName;
        this.subscription?.unsubscribe();
        const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || inlineLang);
        this.subscription = this.service.langChanges$
            .pipe(switchMap((activeLang) => {
            const lang = this.langResolver.resolve({
                inline: inlineLang,
                provider: this.providerLang,
                active: activeLang,
            });
            return Array.isArray(this.providerScope)
                ? forkJoin(this.providerScope.map((providerScope) => this.resolveScope(lang, providerScope)))
                : this.resolveScope(lang, this.providerScope);
        }), listenOrNotOperator(listenToLangChange))
            .subscribe(() => this.updateValue(key, params));
        return this.lastValue;
    }
    ngOnDestroy() {
        this.subscription?.unsubscribe();
        // Caretaker note: it's important to clean up references to subscriptions since they save the `next`
        // callback within its `destination` property, preventing classes from being GC'd.
        this.subscription = null;
    }
    updateValue(key, params) {
        const lang = this.langResolver.resolveLangBasedOnScope(this.path);
        this.lastValue = this.service.translate(key, params, lang);
        this.cdr.markForCheck();
    }
    resolveScope(lang, providerScope) {
        const resolvedScope = this.scopeResolver.resolve({
            inline: undefined,
            provider: providerScope,
        });
        this.path = this.langResolver.resolveLangPath(lang, resolvedScope);
        const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);
        return this.service._loadDependencies(this.path, inlineLoader);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoPipe, deps: [{ token: i1.TranslocoService }, { token: TRANSLOCO_SCOPE, optional: true }, { token: TRANSLOCO_LANG, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Pipe });
    static ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: "14.0.0", version: "16.1.7", ngImport: i0, type: TranslocoPipe, isStandalone: true, name: "transloco", pure: false });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.1.7", ngImport: i0, type: TranslocoPipe, decorators: [{
            type: Pipe,
            args: [{
                    name: 'transloco',
                    pure: false,
                    standalone: true,
                }]
        }], ctorParameters: function () { return [{ type: i1.TranslocoService }, { type: undefined, decorators: [{
                    type: Optional
                }, {
                    type: Inject,
                    args: [TRANSLOCO_SCOPE]
                }] }, { type: undefined, decorators: [{
                    type: Optional
                }, {
                    type: Inject,
                    args: [TRANSLOCO_LANG]
                }] }, { type: i0.ChangeDetectorRef }]; } });
//# sourceMappingURL=data:application/json;base64,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