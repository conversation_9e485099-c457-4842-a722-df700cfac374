export { translate, translateObject, TranslocoService, } from './lib/transloco.service';
export { TranslocoDirective } from './lib/transloco.directive';
export { TranslocoPipe } from './lib/transloco.pipe';
export { TranslocoModule } from './lib/transloco.module';
export { TRANSLOCO_LOADER } from './lib/transloco.loader';
export { TRANSLOCO_CONFIG, defaultConfig, translocoConfig, } from './lib/transloco.config';
export { TRANSLOCO_TRANSPILER, DefaultTranspiler, FunctionalTranspiler, getFunctionArgs, } from './lib/transloco.transpiler';
export { TRANSLOCO_SCOPE } from './lib/transloco-scope';
export { TRANSLOCO_LOADING_TEMPLATE } from './lib/transloco-loading-template';
export { TRANSLOCO_LANG } from './lib/transloco-lang';
export { TestingLoader, TranslocoTestingModule, } from './lib/transloco-testing.module';
export { TRANSLOCO_INTERCEPTOR, } from './lib/transloco.interceptor';
export { TRANSLOCO_FALLBACK_STRATEGY, DefaultFallbackStrategy, } from './lib/transloco-fallback-strategy';
export { TRANSLOCO_MISSING_HANDLER, } from './lib/transloco-missing-handler';
export { getBrowserCultureLang, getBrowserLang } from './lib/browser-lang';
export { getPipeValue, getLangFromScope, getScopeFromLang } from './lib/shared';
export * from './lib/types';
export * from './lib/helpers';
export { provideTranslocoFallbackStrategy, provideTranslocoInterceptor, provideTranslocoTranspiler, provideTranslocoMissingHandler, provideTranslocoLoadingTpl, provideTransloco, provideTranslocoConfig, provideTranslocoLoader, provideTranslocoScope, provideTranslocoLang, } from './lib/transloco.providers';
//# sourceMappingURL=data:application/json;base64,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