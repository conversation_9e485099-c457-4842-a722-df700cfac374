# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [6.0.4](https://github.com/ngneat/transloco/compare/transloco-6.0.3...transloco-6.0.4) (2023-12-09)

### Bug Fixes

- **transloco:** 🐛 providing the FunctionalTranspiler ([308438f](https://github.com/ngneat/transloco/commit/308438f7ae93ec3f7733b9b2cb2278f3aecacd33)), closes [#733](https://github.com/ngneat/transloco/issues/733)

## [6.0.3](https://github.com/ngneat/transloco/compare/transloco-6.0.2...transloco-6.0.3) (2023-12-09)

### Bug Fixes

- **transloco:** 🐛 export TranslocoOptions ([396151f](https://github.com/ngneat/transloco/commit/396151f441d12e3856451377b5c21d3b0310475c)), closes [#709](https://github.com/ngneat/transloco/issues/709)

## [6.0.2](https://github.com/ngneat/transloco/compare/transloco-6.0.1...transloco-6.0.2) (2023-12-08)

## [6.0.1](https://github.com/ngneat/transloco/compare/transloco-6.0.0...transloco-6.0.1) (2023-12-05)

- **transloco:** 🐛 Angular 17 ng-add ([b69f61e8](https://github.com/ngneat/transloco/commit/b69f61e8e8bfa406655061ad48f5a3cc8648a1d4)), closes [#729](https://github.com/ngneat/transloco/issues/729)

# [6.0.0](https://github.com/ngneat/transloco/compare/transloco-5.0.10...transloco-6.0.0) (2023-09-21)

### BREAKING CHANGES

- **transloco:** 🧨 Update to flat v6 which is ESM

## [5.0.10](https://github.com/ngneat/transloco/compare/transloco-5.0.9...transloco-5.0.10) (2023-09-20)

This version contains breaking changes, please use 5.0.7 until you are ready to migrate to v6.

- **transloco:** 🐛 migrate schematic improve "translate" pipe regex ([1d5cb65](https://github.com/ngneat/transloco/commit/1d5cb6535f86fe7bfd9f8558a9b80f325849a12a)), closes [#701](https://github.com/ngneat/transloco/issues/701)
- **transloco:** 🐛 split schematic doesn't update subfolders ([841e2def](https://github.com/ngneat/transloco/commit/841e2def5d78319901c27ab6db7b79ffc52bd126)), closes [#695](https://github.com/ngneat/transloco/issues/695)

### BREAKING CHANGES

- **transloco:** 🧨 Update to flat v6 which is ESM

## [5.0.9](https://github.com/ngneat/transloco/compare/transloco-5.0.8...transloco-5.0.9) (2023-09-17)

### Bug Fixes

- **transloco:** 🐛 split scope schematics ([dfc5373](https://github.com/ngneat/transloco/commit/dfc537302564ec34f4f93bd974541e036d8c6983)), closes [#558](https://github.com/ngneat/transloco/issues/558)

This version contains breaking changes, please use 5.0.7 until you are ready to migrate to v6.

### BREAKING CHANGES

- **transloco:** 🧨 Update to flat v6 which is ESM

## [5.0.8](https://github.com/ngneat/transloco/compare/transloco-5.0.7...transloco-5.0.8) (2023-09-16)

### Bug Fixes

This version contains breaking changes, please use 5.0.7 until you are ready to migrate to v6.

- **transloco:** 🐛 update flat to v6 ([7657b3d](https://github.com/ngneat/transloco/commit/7657b3de185b011bd35f815436da759aae33fc26)), closes [#570](https://github.com/ngneat/transloco/issues/570)

### BREAKING CHANGES

- **transloco:** 🧨 Update to flat v6 which is ESM

## [5.0.7](https://github.com/ngneat/transloco/compare/transloco-5.0.6...transloco-5.0.7) (2023-08-22)

### Bug Fixes

- **transloco:** 🐛 replace structuredClone with json clone ([a76cd05](https://github.com/ngneat/transloco/commit/a76cd05fd71148c79cfc418e996ca908a47aeeb2)), closes [#688](https://github.com/ngneat/transloco/issues/688)

## [5.0.6](https://github.com/ngneat/transloco/compare/transloco-5.0.5...transloco-5.0.6) (2023-08-05)

## [5.0.5](https://github.com/ngneat/transloco/compare/transloco-5.0.4...transloco-5.0.5) (2023-08-05)

### Bug Fixes

- **transloco:** 🐛 ng add in NgModule app ([ad7f8bd5](https://github.com/ngneat/transloco/commit/ad7f8bd57f7dd05dd75ab2549ed69ba60948849f))

## [5.0.4](https://github.com/ngneat/transloco/compare/transloco-5.0.3...transloco-5.0.4) (2023-08-04)

### Bug Fixes

- **transloco:** 🐛 latest version not installed in fresh project ([c8bb426](https://github.com/ngneat/transloco/commit/c8bb426955f63a862ccb8155f43c635979190931))

## [5.0.3](https://github.com/ngneat/transloco/compare/transloco-5.0.2...transloco-5.0.3) (2023-08-02)

### Bug Fixes

- **transloco:** 🐛 allow multiple functions functional transpiler ([#667](https://github.com/ngneat/transloco/issues/667)) ([5095f96](https://github.com/ngneat/transloco/commit/5095f96f7bca90f61902b5d02f18b5a3ce8edf20))

## [5.0.2](https://github.com/ngneat/transloco/compare/transloco-5.0.1...transloco-5.0.2) (2023-07-31)

## [5.0.1](https://github.com/ngneat/transloco/compare/transloco-5.0.0...transloco-5.0.1) (2023-07-31)

### Bug Fixes

- **transloco:** 🐛 set transloco-utils dep version to 5 ([#675](https://github.com/ngneat/transloco/issues/675)) ([0463090](https://github.com/ngneat/transloco/commit/046309072d66115f6921019f04192978e7761b5e))

# [5.0.0](https://github.com/ngneat/transloco/compare/transloco-4.3.0...transloco-5.0.0) (2023-07-30)

### Bug Fixes

- **transloco:** 🐛 add missing provider + make environment ([71d61d7](https://github.com/ngneat/transloco/commit/71d61d7eb0c7dba9bc60185ddd093e57256c6d61))
- **transloco:** 🐛 move to inject pattern and fix build ([8f9fe59](https://github.com/ngneat/transloco/commit/8f9fe598f6418620d94a73b84778c30ebe3bcc18))
- **transloco:** 🐛 multi scope handling ([ec96ca6](https://github.com/ngneat/transloco/commit/ec96ca69e6f3ec504c3a398842aa36e2f6c3d1ab))

### chore

- 🤖 upgrade to angular v16 ([#661](https://github.com/ngneat/transloco/issues/661)) ([08db7e7](https://github.com/ngneat/transloco/commit/08db7e7d1f64846fa0b07123dee8ff5bff20b4f0))

### Code Refactoring

- **transloco:** 💡 remove type ([2f61221](https://github.com/ngneat/transloco/commit/2f61221bfe2b6eb5f58457d07f81677cda0b1853))

### Features

- **transloco:** 🎸 upgrade to v16 ([72381f5](https://github.com/ngneat/transloco/commit/72381f5bc05d1917a1e0c010d92366a24ae8cc64)), closes [#646](https://github.com/ngneat/transloco/issues/646)
- **transloco:** 🎸 upgrade to v16 and standalone ([5f9e687](https://github.com/ngneat/transloco/commit/5f9e687d25679829a438de04a0acd255198f2f9b))

### BREAKING CHANGES

- **transloco:** 🧨 Remove PersistStorage type
- **transloco:** 🧨 Minimum Angular version is now 16
- 🧨 Angular 16

# [4.3.0](https://github.com/ngneat/transloco/compare/transloco-4.2.7...transloco-4.3.0) (2023-06-10)

### Bug Fixes

- **transloco:** 🐛 providers typings ([38e4981](https://github.com/ngneat/transloco/commit/38e4981db2e2f82dfbf081b27f8eba0b68143fdb)), closes [#658](https://github.com/ngneat/transloco/issues/658)

### Features

- **transloco:** 🎸 providers functions ([528e548](https://github.com/ngneat/transloco/commit/528e548f747a76f027044fe4f97198e795872510))

## [4.2.7](https://github.com/ngneat/transloco/compare/transloco-4.2.6...transloco-4.2.7) (2023-05-18)

## [4.2.6](https://github.com/ngneat/transloco/compare/transloco-4.2.5...transloco-4.2.6) (2023-03-04)

### Bug Fixes

- **transloco:** 🐛 fix message format delay ([#634](https://github.com/ngneat/transloco/issues/634)) ([aa92418](https://github.com/ngneat/transloco/commit/aa92418ab319b42fc42b32e0884c6054fa7ccf2c))

## [4.2.5](https://github.com/ngneat/transloco/compare/transloco-4.2.4...transloco-4.2.5) (2023-02-21)

### Bug Fixes

- **transloco:** update fs-extra and glob dependencies ([#631](https://github.com/ngneat/transloco/issues/631)) ([e19057f](https://github.com/ngneat/transloco/commit/e19057f34e3daddc40dc7059974f721b3ea8a30e))

## [4.2.4](https://github.com/ngneat/transloco/compare/transloco-4.2.3...transloco-4.2.4) (2023-02-19)

## [4.2.3](https://github.com/ngneat/transloco/compare/transloco-4.2.2...transloco-4.2.3) (2023-02-19)

### Bug Fixes

- **transloco:** 🐛 add missing schematics deps ([581dda1](https://github.com/ngneat/transloco/commit/581dda1c4955ffa68f43ee0bfdef3ef2afc9f698)), closes [#623](https://github.com/ngneat/transloco/issues/623)

## [4.2.2](https://github.com/ngneat/transloco/compare/transloco-4.2.1...transloco-4.2.2) (2023-01-06)

### Bug Fixes

- **transloco:** add overload type in selectTranslateObject method ([#617](https://github.com/ngneat/transloco/issues/617)) ([071ca6b](https://github.com/ngneat/transloco/commit/071ca6b21bea872adaadba917484ca0190d4b051))

## [4.2.1](https://github.com/ngneat/transloco/compare/transloco-4.2.0...transloco-4.2.1) (2022-12-10)

### Bug Fixes

- **transloco**: 🐛 replace environment with isDveMode for v15 ([e4bc6c91](https://github.com/ngneat/transloco/commit/e4bc6c91cb04d8e2ff41269abb963f1c6eed0c93))

# [4.2.0](https://github.com/ngneat/transloco/compare/transloco-4.1.1...transloco-4.2.0) (2022-12-10)

### Features

- **transloco:** 🎸 add translation key to transpile method ([#569](https://github.com/ngneat/transloco/issues/569)) ([3a19ba6](https://github.com/ngneat/transloco/commit/3a19ba6cde6a96c8d4af893b824400dd7217cd71)), closes [ngneat#568](https://github.com/ngneat/issues/568)
  _Note:_ if you are using `@ngneat/transloco-messageformat` You'll need to update to 4.1.0 and above.

## [4.1.2](https://github.com/ngneat/transloco/compare/transloco-4.1.1...transloco-4.1.2) (2022-12-10)

### Bug Fixes

- **schematics:** set current directory as default project name ([#582](https://github.com/ngneat/transloco/issues/582)) ([89b213f9](https://github.com/ngneat/transloco/commit/89b213f9f3751d6414fb82aee0031e6863ed585f))

## [4.1.1](https://github.com/ngneat/transloco/compare/transloco-4.1.0...transloco-4.1.1) (2022-07-13)

### Bug Fixes

- **transloco:** key as array in translateObject throws ([#577](https://github.com/ngneat/transloco/issues/577)) ([5c8e90b](https://github.com/ngneat/transloco/commit/5c8e90b3ecf67106e3a22c33a9f82f990353d0bd))

# [4.1.0](https://github.com/ngneat/transloco/compare/transloco-4.0.0...transloco-4.1.0) (2022-06-04)

### Features

- **transloco:** 🎸 export translateObject as a proxy to the TranslocoService.translateObject method ([#565](https://github.com/ngneat/transloco/issues/565)) ([26ef1a0](https://github.com/ngneat/transloco/commit/26ef1a0fb7f9dc564d280ced3290be87ae2fedc8))

# [4.0.0](https://github.com/ngneat/transloco/compare/transloco-3.2.0...transloco-4.0.0) (2022-03-23)

### Features

- **transloco:** 🎸 upgrade to v13 ([d973668](https://github.com/ngneat/transloco/commit/d9736685caee6959b4af2b3659071e7a553d858c)), closes [#536](https://github.com/ngneat/transloco/issues/536)

### BREAKING CHANGES

- **transloco:** 🧨 Minimum Angular version is now 13

# [3.2.0](https://github.com/ngneat/transloco/compare/transloco-3.1.4...transloco-3.2.0) (2022-03-19)

### Features

- **transloco:** 🎸 add langChanged event ([50e374f](https://github.com/ngneat/transloco/commit/50e374f762665ba5d532cd3bd6e1653fe07420f6))

## [3.1.4](https://github.com/ngneat/transloco/compare/transloco-3.1.3...transloco-3.1.4) (2022-02-15)

### Bug Fixes

- **transloco:** 🐛 Add generic type default to Translate<T>() ([#545](https://github.com/ngneat/transloco/issues/545)) ([1a71d90](https://github.com/ngneat/transloco/commit/1a71d90b816a1297b1034dec48650a886a5caec2))

## [3.1.3](https://github.com/ngneat/transloco/compare/transloco-3.1.2...transloco-3.1.3) (2022-01-22)

## [3.1.2](https://github.com/ngneat/transloco/compare/transloco-3.1.1...transloco-3.1.2) (2022-01-20)

### Bug Fixes

- **transloco:** 🐛 structural directive redundant calls ([32230d6](https://github.com/ngneat/transloco/commit/32230d640c36c16e71f331451315de78527ceb0c))

## [3.1.1](https://github.com/ngneat/transloco/compare/transloco-3.1.0...transloco-3.1.1) (2021-11-19)

### Bug Fixes

- **transloco:** 🐛 regular directive with ssr ([28f14af](https://github.com/ngneat/transloco/commit/28f14affbca431a1017f6f95da9ee2a5d4cdfdb5))

# [3.1.0](https://github.com/ngneat/transloco/compare/transloco-3.0.7...transloco-3.1.0) (2021-11-09)

### Features

- **transloco:** 🎸 add a template context guard to directive ([#526](https://github.com/ngneat/transloco/issues/526)) ([48f924d](https://github.com/ngneat/transloco/commit/48f924d95b1761bab1cfe00686d96c787c7cf667))

## [3.0.7](https://github.com/ngneat/transloco/compare/transloco-3.0.6...transloco-3.0.7) (2021-10-28)

## [3.0.6](https://github.com/ngneat/transloco/compare/transloco-3.0.5...transloco-3.0.6) (2021-10-14)

## [3.0.5](https://github.com/ngneat/transloco/compare/transloco-3.0.4...transloco-3.0.5) (2021-10-12)

### Bug Fixes

- transloco fallback strategy class is not decorated ([#517](https://github.com/ngneat/transloco/issues/517)) ([e922982](https://github.com/ngneat/transloco/commit/e92298226995df953d7f91a3d1ee2cc0bf7b520f))

## [3.0.4](https://github.com/ngneat/transloco/compare/transloco-3.0.3...transloco-3.0.4) (2021-10-12)

## [3.0.3](https://github.com/ngneat/transloco/compare/transloco-3.0.2...transloco-3.0.3) (2021-10-10)

### Bug Fixes

- **transloco:** 🐛 fix schematics ([627a565](https://github.com/ngneat/transloco/commit/627a565591ff83bbd504c5d07ee534c7670a2556)), closes [#516](https://github.com/ngneat/transloco/issues/516)

## [3.0.2](https://github.com/ngneat/transloco/compare/transloco-3.0.1...transloco-3.0.2) (2021-10-07)

### Bug Fixes

- **transloco:** 🐛 unexpected peerDependencies ([672e529](https://github.com/ngneat/transloco/commit/672e5294fd25e456229c800fd1ebf49e39fc88e4)), closes [#513](https://github.com/ngneat/transloco/issues/513)

## [3.0.1](https://github.com/ngneat/transloco/compare/transloco-3.0.0...transloco-3.0.1) (2021-10-02)

### [3.0.0](https://github.com/ngneat/transloco/compare/v2.23.3...transloco-3.0.0) (2021-09-30)

### Bug Fixes

- 🐛 don't call the missing handler when given an empty key ([86fa12d](https://github.com/ngneat/transloco/commit/86fa12d09c235f5c34a81ee049f94b898e198280))

### Features

- 🎸 supported passing params when reusing keys ([6db0d74](https://github.com/ngneat/transloco/commit/6db0d74d14e449d4f4f703ef3ee8a7dd97198b81)), closes [#259](https://github.com/ngneat/transloco/issues/259)
- **messageformat:** 🎸 add cache support ([773bc5c](https://github.com/ngneat/transloco/commit/773bc5c227ba63b556b8277161dc598a0c630010)), closes [#358](https://github.com/ngneat/transloco/issues/358)

### [2.23.3](https://github.com/ngneat/transloco/compare/v2.23.2...v2.23.3) (2021-09-19)

### Bug Fixes

- allow classes to be GCd ([#505](https://github.com/ngneat/transloco/issues/505)) ([8dbec56](https://github.com/ngneat/transloco/commit/8dbec56))

### Tests

- 💍 fix pipe tests ([10c4e25](https://github.com/ngneat/transloco/commit/10c4e25))

### [2.23.2](https://github.com/ngneat/transloco/compare/v2.23.1...v2.23.2) (2021-09-13)

### Bug Fixes

- 🐛 load error not reported ([cd92b46](https://github.com/ngneat/transloco/commit/cd92b46)), closes [#290](https://github.com/ngneat/transloco/issues/290)
- 🐛 static lang is ignored in pipe ([28833b8](https://github.com/ngneat/transloco/commit/28833b8)), closes [#463](https://github.com/ngneat/transloco/issues/463)
- 🐛 support optional chaining ([e8e92ba](https://github.com/ngneat/transloco/commit/e8e92ba)), closes [#488](https://github.com/ngneat/transloco/issues/488)

### [2.23.1](https://github.com/ngneat/transloco/compare/v2.23.0...v2.23.1) (2021-09-11)

### Bug Fixes

- 🐛 ng-add wrong environment path ([75d4bd0](https://github.com/ngneat/transloco/commit/75d4bd0)), closes [#332](https://github.com/ngneat/transloco/issues/332)

## [2.23.0](https://github.com/ngneat/transloco/compare/v2.22.0...v2.23.0) (2021-09-07)

### Bug Fixes

- allow `TranslocoService` to be garbage collected ([#473](https://github.com/ngneat/transloco/issues/473)) ([3650240](https://github.com/ngneat/transloco/commit/3650240))
- parser lang ([#494](https://github.com/ngneat/transloco/issues/494)) ([ffeb05e](https://github.com/ngneat/transloco/commit/ffeb05e))

### Features

- add possibility of override `getTranslateFn` ([#492](https://github.com/ngneat/transloco/issues/492)) ([7b49bbd](https://github.com/ngneat/transloco/commit/7b49bbd))

## [2.22.0](https://github.com/ngneat/transloco/compare/v2.21.0...v2.22.0) (2021-07-01)

### Features

- 🎸 expose currentLang in structural directive ([#380](https://github.com/ngneat/transloco/issues/380)) ([77b0567](https://github.com/ngneat/transloco/commit/77b0567)), closes [ngneat/transloco#376](https://github.com/ngneat/transloco/issues/376)
- 🎸 update schematics id to v12 ([86e896d](https://github.com/ngneat/transloco/commit/86e896d))
- allow to specify options when setting translations by key ([#464](https://github.com/ngneat/transloco/issues/464)) ([e7d97e6](https://github.com/ngneat/transloco/commit/e7d97e6))

## [2.21.0](https://github.com/ngneat/transloco/compare/v2.20.1...v2.21.0) (2021-05-26)

### Bug Fixes

- 🐛 update angular peer dep ([6b96f12](https://github.com/ngneat/transloco/commit/6b96f12))
- directly specify location of package.json missing i18n config ([#401](https://github.com/ngneat/transloco/issues/401)) ([54ee98a](https://github.com/ngneat/transloco/commit/54ee98a))

### Features

- **locale:** support default currency config ([#423](https://github.com/ngneat/transloco/issues/423)) ([463b170](https://github.com/ngneat/transloco/commit/463b170))

### [2.20.1](https://github.com/ngneat/transloco/compare/v2.20.0...v2.20.1) (2021-02-28)

### Bug Fixes

- language fallback strategy ([#390](https://github.com/ngneat/transloco/issues/390)) ([92a28b4](https://github.com/ngneat/transloco/commit/92a28b4))

## [2.20.0](https://github.com/ngneat/transloco/compare/v2.19.2...v2.20.0) (2020-12-17)

### Bug Fixes

- 🐛 service not initialized in testing module ([#382](https://github.com/ngneat/transloco/issues/382)) ([08db253](https://github.com/ngneat/transloco/commit/08db253))
- 🐛 update peer to support angular 11 ([6f3f973](https://github.com/ngneat/transloco/commit/6f3f973)), closes [#378](https://github.com/ngneat/transloco/issues/378)
- schematics join ([#348](https://github.com/ngneat/transloco/issues/348)) ([174f178](https://github.com/ngneat/transloco/commit/174f178))

### Features

- 🎸 transpile array values ([#383](https://github.com/ngneat/transloco/issues/383)) ([fc139e0](https://github.com/ngneat/transloco/commit/fc139e0))

### [2.19.3](https://github.com/ngneat/transloco/compare/v2.19.2...v2.19.3) (2020-12-06)

### Bug Fixes

- 🐛 update peer to support angular 11 ([6f3f973](https://github.com/ngneat/transloco/commit/6f3f973)), closes [#378](https://github.com/ngneat/transloco/issues/378)

### [2.19.2](https://github.com/ngneat/transloco/compare/v2.19.1...v2.19.2) (2020-12-01)

### Bug Fixes

- 🐛 schematics keys manager install, support yarn ([d923542](https://github.com/ngneat/transloco/commit/d923542))
- **tests:** fix failing unit and e2e tests ([#361](https://github.com/ngneat/transloco/issues/361)) ([43ff362](https://github.com/ngneat/transloco/commit/43ff362))

### [2.19.1](https://github.com/ngneat/transloco/compare/v2.19.0...v2.19.1) (2020-08-22)

### Bug Fixes

- 🐛 pipe key type in strict mode ([faa58a5](https://github.com/ngneat/transloco/commit/faa58a5)), closes [#311](https://github.com/ngneat/transloco/issues/311)
- 🐛 select translation on lang change ([f2e5415](https://github.com/ngneat/transloco/commit/f2e5415))

### Tests

- 💍 update tests ([427c99f](https://github.com/ngneat/transloco/commit/427c99f))

## [2.19.0](https://github.com/ngneat/transloco/compare/v2.18.4...v2.19.0) (2020-08-18)

### Features

- 🎸 Add interpolation to configuration ([#330](https://github.com/ngneat/transloco/issues/330)) ([cf07bfc](https://github.com/ngneat/transloco/commit/cf07bfc))

### [2.18.4](https://github.com/ngneat/transloco/compare/v2.18.3...v2.18.4) (2020-08-07)

### Bug Fixes

- 🐛 Put transloco.module in its own folder ([#319](https://github.com/ngneat/transloco/issues/319)) ([b54be7e](https://github.com/ngneat/transloco/commit/b54be7e))

### [2.18.3](https://github.com/ngneat/transloco/compare/v2.18.2...v2.18.3) (2020-07-22)

### Bug Fixes

- 🐛 remove unused group naming in regex ([0258c07](https://github.com/ngneat/transloco/commit/0258c07)), closes [#313](https://github.com/ngneat/transloco/issues/313)

### [2.18.2](https://github.com/ngneat/transloco/compare/v2.18.1...v2.18.2) (2020-07-16)

### Bug Fixes

- 🐛 add injectable decorator ([94bb9ee](https://github.com/ngneat/transloco/commit/94bb9ee))

### [2.18.1](https://github.com/ngneat/transloco/compare/v2.18.0...v2.18.1) (2020-07-16)

### Bug Fixes

- 🐛 export the functional transpiler ([2fc1a4f](https://github.com/ngneat/transloco/commit/2fc1a4f))

## [2.18.0](https://github.com/ngneat/transloco/compare/v2.17.5...v2.18.0) (2020-07-16)

### Features

- 🎸 functional transpiler ([d3c5262](https://github.com/ngneat/transloco/commit/d3c5262))

### [2.17.5](https://github.com/ngneat/transloco/compare/v2.17.4...v2.17.5) (2020-07-11)

### Bug Fixes

- 🐛 update keys manager installation to latest version ([#308](https://github.com/ngneat/transloco/issues/308)) ([d6f6526](https://github.com/ngneat/transloco/commit/d6f6526))

### [2.17.4](https://github.com/ngneat/transloco/compare/v2.17.2...v2.17.4) (2020-07-07)

### Bug Fixes

- 🐛 angular cli installing old transloco version ([79f8e10](https://github.com/ngneat/transloco/commit/79f8e10)), closes [#304](https://github.com/ngneat/transloco/issues/304)
- 🐛 Angular v10 BC fixes ([84ad417](https://github.com/ngneat/transloco/commit/84ad417))

### [2.17.3](https://github.com/ngneat/transloco/compare/v2.17.2...v2.17.3) (2020-06-28)

### Bug Fixes

- 🐛 Angular v10 BC fixes ([84ad417](https://github.com/ngneat/transloco/commit/84ad417))

### [2.17.2](https://github.com/ngneat/transloco/compare/v2.17.1...v2.17.2) (2020-06-04)

### Bug Fixes

- 🐛 getBrowserLang throws error when browser has no lang ([52c86c0](https://github.com/ngneat/transloco/commit/52c86c0)), closes [#289](https://github.com/ngneat/transloco/issues/289)

### [2.17.1](https://github.com/ngneat/transloco/compare/v2.17.0...v2.17.1) (2020-05-01)

### Bug Fixes

- 🐛 migration script throws error when no files found ([db82974](https://github.com/ngneat/transloco/commit/db82974))

## [2.17.0](https://github.com/ngneat/transloco/compare/v2.16.1...v2.17.0) (2020-04-24)

### Bug Fixes

- 🐛 don't display loading template if translations were load ([#273](https://github.com/ngneat/transloco/issues/273)) ([c9214ad](https://github.com/ngneat/transloco/commit/c9214ad))

### Features

- 🎸 support translating key-params map ([b261688](https://github.com/ngneat/transloco/commit/b261688)), closes [#272](https://github.com/ngneat/transloco/issues/272)

### [2.16.1](https://github.com/ngneat/transloco/compare/v2.15.0...v2.16.1) (2020-04-23)

## [2.14.0](https://github.com/ngneat/transloco/compare/v2.13.5...v2.14.0) (2020-03-14)

### Features

- 🎸 Add to missingHandler the translation's parameter(s) ([#255](https://github.com/ngneat/transloco/issues/255)) ([73256ce](https://github.com/ngneat/transloco/commit/73256ce))

### [2.13.5](https://github.com/ngneat/transloco/compare/v2.13.4...v2.13.5) (2020-03-01)

### Bug Fixes

- 🐛 ngx migration support new line in pipes ([00f1eae](https://github.com/ngneat/transloco/commit/00f1eae)), closes [#244](https://github.com/ngneat/transloco/issues/244)
- 🐛 set fallback translation ([ff34773](https://github.com/ngneat/transloco/commit/ff34773)), closes [#232](https://github.com/ngneat/transloco/issues/232)

### [2.13.4](https://github.com/ngneat/transloco/compare/v2.13.3...v2.13.4) (2020-02-29)

### Bug Fixes

- **types:** Add additional formatting options ([#247](https://github.com/ngneat/transloco/issues/247)) ([05b6d79](https://github.com/ngneat/transloco/commit/05b6d79))
- 🐛 update peed dependencies ([a1a813a](https://github.com/ngneat/transloco/commit/a1a813a))

### [2.13.3](https://github.com/ngneat/transloco/compare/v2.13.2...v2.13.3) (2020-02-12)

### [2.13.2](https://github.com/ngneat/transloco/compare/v2.13.1...v2.13.2) (2020-02-03)

### [2.13.1](https://github.com/ngneat/transloco/compare/v2.13.0...v2.13.1) (2020-02-02)

### Bug Fixes

- 🐛 testing module should work with ivy ([2a68be7](https://github.com/ngneat/transloco/commit/2a68be7))

## [2.13.0](https://github.com/ngneat/transloco/compare/v2.12.4...v2.13.0) (2020-01-25)

### Features

- 🎸 add support for getting config from path ([31c180a](https://github.com/ngneat/transloco/commit/31c180a))
- 🎸 support inline loader for select translate ([#225](https://github.com/ngneat/transloco/issues/225)) ([a2b1867](https://github.com/ngneat/transloco/commit/a2b1867))

### [2.12.3](https://github.com/ngneat/transloco/compare/v2.12.2...v2.12.3) (2020-01-01)

### Bug Fixes

- 🐛 same fallback lang should not load again ([#211](https://github.com/ngneat/transloco/issues/211)) ([5498dee](https://github.com/ngneat/transloco/commit/5498dee)), closes [#210](https://github.com/ngneat/transloco/issues/210)

### [2.12.2](https://github.com/ngneat/transloco/compare/v2.12.1...v2.12.2) (2019-12-24)

### Bug Fixes

- 🐛 select translate object not working with scopes ([32e1ad7](https://github.com/ngneat/transloco/commit/32e1ad7)), closes [#207](https://github.com/ngneat/transloco/issues/207)

### [2.12.1](https://github.com/ngneat/transloco/compare/v2.12.0...v2.12.1) (2019-12-12)

### Bug Fixes

- 🐛 deprecate provideTranslocoConfig ([#198](https://github.com/ngneat/transloco/issues/198)) ([a8957d2](https://github.com/ngneat/transloco/commit/a8957d2))

## [2.12.0](https://github.com/ngneat/transloco/compare/v2.11.0...v2.12.0) (2019-12-11)

### Bug Fixes

- 🐛 pipe return empty string by default ([#192](https://github.com/ngneat/transloco/issues/192)) ([156cd62](https://github.com/ngneat/transloco/commit/156cd62))
- 🐛 select translations with scope should listen to lang change ([6031046](https://github.com/ngneat/transloco/commit/6031046))

### Features

- **transloco:** implement provideTranslocoConfig method ([#196](https://github.com/ngneat/transloco/issues/196)) ([2a755ae](https://github.com/ngneat/transloco/commit/2a755ae)), closes [#195](https://github.com/ngneat/transloco/issues/195)

### [2.11.2](https://github.com/ngneat/transloco/compare/v2.11.0...v2.11.2) (2019-12-09)

### Bug Fixes

- 🐛 pipe return empty string by default ([#192](https://github.com/ngneat/transloco/issues/192)) ([156cd62](https://github.com/ngneat/transloco/commit/156cd62))
- 🐛 select translations with scope should listen to lang change ([6031046](https://github.com/ngneat/transloco/commit/6031046))

### [2.11.1](https://github.com/ngneat/transloco/compare/v2.11.0...v2.11.1) (2019-12-07)

### Bug Fixes

- 🐛 select translations with scope should listen to lang change ([6031046](https://github.com/ngneat/transloco/commit/6031046))

## [2.11.0](https://github.com/ngneat/transloco/compare/v2.8.0...v2.11.0) (2019-12-06)

### Bug Fixes

- 🐛 preSaveTranslation should get the flattend object ([9174141](https://github.com/ngneat/transloco/commit/9174141)), closes [#145](https://github.com/ngneat/transloco/issues/145)
- 🐛 schematics scope useValue type ([2a2873c](https://github.com/ngneat/transloco/commit/2a2873c))
- export TranslocoMissingHandlerData ([#174](https://github.com/ngneat/transloco/issues/174)) ([c32d589](https://github.com/ngneat/transloco/commit/c32d589))
- Provide multiple values for TRANSLOCO_SCOPE to support lazy loading multiple json files ([#184](https://github.com/ngneat/transloco/issues/184)) ([fa11baa](https://github.com/ngneat/transloco/commit/fa11baa))

### Features

- 🎸 add inline loader flag to scope ([#175](https://github.com/ngneat/transloco/issues/175)) ([8c1383e](https://github.com/ngneat/transloco/commit/8c1383e))
- 🎸 schmeatics:join support ignore/include default lang ([7e48271](https://github.com/ngneat/transloco/commit/7e48271))

## [2.10.0](https://github.com/ngneat/transloco/compare/v2.8.0...v2.10.0) (2019-12-03)

### Bug Fixes

- 🐛 preSaveTranslation should get the flattend object ([9174141](https://github.com/ngneat/transloco/commit/9174141)), closes [#145](https://github.com/ngneat/transloco/issues/145)
- 🐛 schematics scope useValue type ([2a2873c](https://github.com/ngneat/transloco/commit/2a2873c))
- export TranslocoMissingHandlerData ([#174](https://github.com/ngneat/transloco/issues/174)) ([c32d589](https://github.com/ngneat/transloco/commit/c32d589))

### Features

- 🎸 add inline loader flag to scope ([#175](https://github.com/ngneat/transloco/issues/175)) ([8c1383e](https://github.com/ngneat/transloco/commit/8c1383e))

## [2.9.0](https://github.com/ngneat/transloco/compare/v2.8.0...v2.9.0) (2019-11-27)

### Features

- 🎸 add inline loader flag to scope ([#175](https://github.com/ngneat/transloco/issues/175)) ([8c1383e](https://github.com/ngneat/transloco/commit/8c1383e))

## [2.8.0](https://github.com/ngneat/transloco/compare/v2.7.0...v2.8.0) (2019-11-26)

### Features

- 🎸 pass scope name to loader ([c32b10c](https://github.com/ngneat/transloco/commit/c32b10c))

## [2.7.0](https://github.com/ngneat/transloco/compare/v2.6.0...v2.7.0) (2019-11-25)

### Bug Fixes

- 🐛 ng-add: skip translation files creation if exist ([#163](https://github.com/ngneat/transloco/issues/163)) ([a9c1bcf](https://github.com/ngneat/transloco/commit/a9c1bcf))
- 🐛 remove missing keys warnings in tests ([#169](https://github.com/ngneat/transloco/issues/169)) ([741d356](https://github.com/ngneat/transloco/commit/741d356))

### Features

- access to active language and up-to-date config in MissingHandler ([#167](https://github.com/ngneat/transloco/issues/167)) ([30af5c5](https://github.com/ngneat/transloco/commit/30af5c5)), closes [#164](https://github.com/ngneat/transloco/issues/164)

### [2.6.2](https://github.com/ngneat/transloco/compare/v2.6.1...v2.6.2) (2019-11-23)

### Bug Fixes

- 🐛 remove missing keys warnings in tests ([#169](https://github.com/ngneat/transloco/issues/169)) ([741d356](https://github.com/ngneat/transloco/commit/741d356))

### [2.6.1](https://github.com/ngneat/transloco/compare/v2.6.0...v2.6.1) (2019-11-20)

### Bug Fixes

- 🐛 ng-add: skip translation files creation if exist ([#163](https://github.com/ngneat/transloco/issues/163)) ([a9c1bcf](https://github.com/ngneat/transloco/commit/a9c1bcf))

## [2.6.0](https://github.com/ngneat/transloco/compare/v2.5.2...v2.6.0) (2019-11-17)

### Features

- 🎸 add schematics migration script from angular ([#151](https://github.com/ngneat/transloco/issues/151)) ([bc07dfa](https://github.com/ngneat/transloco/commit/bc07dfa))
- 🎸 allow changing the fallback languages dynamically ([2719981](https://github.com/ngneat/transloco/commit/2719981))

### [2.5.2](https://github.com/ngneat/transloco/compare/v2.5.1...v2.5.2) (2019-11-05)

### Bug Fixes

- 🐛 remove default langs ([d9b6b26](https://github.com/ngneat/transloco/commit/d9b6b26))

### [2.5.1](https://github.com/ngneat/transloco/compare/v2.5.0...v2.5.1) (2019-11-05)

### Bug Fixes

- 🐛 add utils ([c6233b7](https://github.com/ngneat/transloco/commit/c6233b7))

## [2.5.0](https://github.com/ngneat/transloco/compare/v2.4.0...v2.5.0) (2019-11-05)

### Bug Fixes

- 🐛 schematics scope ignore routing.module file ([57dbcd5](https://github.com/ngneat/transloco/commit/57dbcd5))

### Features

- 🎸 inline-loader support any kind of promise ([34bc788](https://github.com/ngneat/transloco/commit/34bc788))
- 🎸 schematics command to install keys manager ([38c885b](https://github.com/ngneat/transloco/commit/38c885b))

## [2.4.0](https://github.com/ngneat/transloco/compare/v2.3.1...v2.4.0) (2019-11-01)

### Features

- 🎸 add scope to event payload ([f70db69](https://github.com/ngneat/transloco/commit/f70db69))

### [2.3.1](https://github.com/ngneat/transloco/compare/v2.3.0...v2.3.1) (2019-10-30)

### Bug Fixes

- 🐛 predavetranslation should always be flat ([2f12e71](https://github.com/ngneat/transloco/commit/2f12e71)), closes [#145](https://github.com/ngneat/transloco/issues/145)

## [2.3.0](https://github.com/ngneat/transloco/compare/v2.2.2...v2.3.0) (2019-10-25)

### Features

- 🎸 add logMissingKey option to missing handler ([c2d368b](https://github.com/ngneat/transloco/commit/c2d368b))

### [2.2.2](https://github.com/ngneat/transloco/compare/v2.2.1...v2.2.2) (2019-10-21)

### Bug Fixes

- 🐛 remove redundant dep ([dad9269](https://github.com/ngneat/transloco/commit/dad9269))

### [2.2.1](https://github.com/ngneat/transloco/compare/v2.2.0...v2.2.1) (2019-10-17)

### Bug Fixes

- 🐛 protect unsubscribing in pipe ([b55bd59](https://github.com/ngneat/transloco/commit/b55bd59)), closes [#137](https://github.com/ngneat/transloco/issues/137)

## [2.2.0](https://github.com/ngneat/transloco/compare/v2.1.2...v2.2.0) (2019-10-14)

### Features

- 🎸 add merge translation files command ([28f1af9](https://github.com/ngneat/transloco/commit/28f1af9))
- 🎸 add spill schematics command ([f3ad130](https://github.com/ngneat/transloco/commit/f3ad130))
- 🎸 inline loaders ([1648de4](https://github.com/ngneat/transloco/commit/1648de4))

### Tests

- 💍 schematics spill command ([d57fa82](https://github.com/ngneat/transloco/commit/d57fa82))
- 💍 test schematics merge command ([5484520](https://github.com/ngneat/transloco/commit/5484520))

### [2.1.2](https://github.com/ngneat/transloco/compare/v2.1.1...v2.1.2) (2019-10-11)

### Bug Fixes

- 🐛 inline lang for pipe ([72082c0](https://github.com/ngneat/transloco/commit/72082c0))

### Tests

- 💍 refactor and add specs ([ae7210a](https://github.com/ngneat/transloco/commit/ae7210a))

### [2.1.1](https://github.com/ngneat/transloco/compare/v2.1.0...v2.1.1) (2019-10-05)

### Bug Fixes

- 🐛 add key prefix when translating scope ([#116](https://github.com/ngneat/transloco/issues/116)) ([a84ceb3](https://github.com/ngneat/transloco/commit/a84ceb3))

## [2.1.0](https://github.com/ngneat/transloco/compare/v2.0.4...v2.1.0) (2019-10-04)

### Features

- 🎸 alias pr merge fix ([7e3ab3f](https://github.com/ngneat/transloco/commit/7e3ab3f))

### [2.0.4](https://github.com/ngneat/transloco/compare/v2.0.3...v2.0.4) (2019-10-04)

### Bug Fixes

- 🐛 fix type in the structural directive ([3144b51](https://github.com/ngneat/transloco/commit/3144b51))

### Tests

- 💍 fix messageformat spec ([eebd8a4](https://github.com/ngneat/transloco/commit/eebd8a4))

### [2.0.3](https://github.com/ngneat/transloco/compare/v2.0.2...v2.0.3) (2019-10-03)

### [2.0.2](https://github.com/ngneat/transloco/compare/v2.0.1...v2.0.2) (2019-10-03)

### Bug Fixes

- 🐛 fix v2 upgrade script ([e390ae4](https://github.com/ngneat/transloco/commit/e390ae4))

### [2.0.1](https://github.com/ngneat/transloco/compare/v2.0.0...v2.0.1) (2019-10-02)

### Bug Fixes

- 🐛 missing handler fix allow empty values in pipe ([2efbe6c](https://github.com/ngneat/transloco/commit/2efbe6c))

## [2.0.0](https://github.com/ngneat/transloco/compare/v1.7.8...v2.0.0) (2019-10-02)

### Bug Fixes

- 🐛 ng-add: fix hard coded fallbackLang ([#90](https://github.com/ngneat/transloco/issues/90)) ([9b1239f](https://github.com/ngneat/transloco/commit/9b1239f))
- schematics ng-add availabeLangs ([8b6424d](https://github.com/ngneat/transloco/commit/8b6424d))

### Features

- 🎸 change structural to function ([1945686](https://github.com/ngneat/transloco/commit/1945686))
- 🎸 missing translation fallback ([8151c24](https://github.com/ngneat/transloco/commit/8151c24))
- 🎸 v2 changes ([430fadf](https://github.com/ngneat/transloco/commit/430fadf))
- 🤖 Add schematics for transloco scope ([#79](https://github.com/ngneat/transloco/issues/79)) ([aea9495](https://github.com/ngneat/transloco/commit/aea9495))

### Tests

- 💍 add fallback specs ([d5d91e6](https://github.com/ngneat/transloco/commit/d5d91e6))
- 💍 update lazy spec ([ab2e627](https://github.com/ngneat/transloco/commit/ab2e627))

### BREAKING CHANGES

- See in BREAKING_CHANGES.md file

### [1.7.9](https://github.com/ngneat/transloco/compare/v1.7.8...v1.7.9) (2019-09-17)

### [1.7.8](https://github.com/ngneat/transloco/compare/v1.7.7...v1.7.8) (2019-09-11)

### Bug Fixes

- 🐛 update migration script ([c7510f7](https://github.com/ngneat/transloco/commit/c7510f7))

### [1.7.7](https://github.com/ngneat/transloco/compare/v1.7.6...v1.7.7) (2019-09-11)

### Bug Fixes

- **testing:** set prodmode to true by default ([5995b75](https://github.com/ngneat/transloco/commit/5995b75))

### [1.7.6](https://github.com/ngneat/transloco/compare/v1.7.5...v1.7.6) (2019-09-09)

### Bug Fixes

- 🐛 allow pass config in testing module ([caeaa8c](https://github.com/ngneat/transloco/commit/caeaa8c))

### [1.7.5](https://github.com/ngneat/transloco/compare/v1.7.4...v1.7.5) (2019-09-08)

### Bug Fixes

- **core:** lack of unsubscriptions ([#65](https://github.com/ngneat/transloco/issues/65)) ([67f5d3f](https://github.com/ngneat/transloco/commit/67f5d3f))
- 🐛 pipe should support any type ([bf3f11c](https://github.com/ngneat/transloco/commit/bf3f11c)), closes [#70](https://github.com/ngneat/transloco/issues/70)

### [1.7.4](https://github.com/ngneat/transloco/compare/v1.7.3...v1.7.4) (2019-09-07)

### Bug Fixes

- 🐛 scope mapping ([a3545a1](https://github.com/ngneat/transloco/commit/a3545a1)), closes [#69](https://github.com/ngneat/transloco/issues/69)

### [1.7.3](https://github.com/ngneat/transloco/compare/v1.7.2...v1.7.3) (2019-09-06)

### Bug Fixes

- 🐛 transpiler protection ([9471f17](https://github.com/ngneat/transloco/commit/9471f17))

### [1.7.2](https://github.com/ngneat/transloco/compare/v1.7.1...v1.7.2) (2019-09-05)

### Bug Fixes

- 🐛 update migration script ([b712bd7](https://github.com/ngneat/transloco/commit/b712bd7))

### [1.7.1](https://github.com/ngneat/transloco/compare/v1.7.0...v1.7.1) (2019-09-03)

### Bug Fixes

- 🐛 selectTranslate ([a7af5c6](https://github.com/ngneat/transloco/commit/a7af5c6))

## [1.7.0](https://github.com/ngneat/transloco/compare/v1.6.0...v1.7.0) (2019-09-01)

### Bug Fixes

- remove extension from missing handler export ([e756079](https://github.com/ngneat/transloco/commit/e756079))
- **readme:** add missing @ ([#51](https://github.com/ngneat/transloco/issues/51)) ([2216378](https://github.com/ngneat/transloco/commit/2216378))

### Features

- 🎸 multi-lang ([8d91b61](https://github.com/ngneat/transloco/commit/8d91b61))
- 🎸 mutli-langs ([0e58324](https://github.com/ngneat/transloco/commit/0e58324))
- 🎸 transpilers ([198eb6d](https://github.com/ngneat/transloco/commit/198eb6d))
- expose missing handler token and interface ([1a56908](https://github.com/ngneat/transloco/commit/1a56908))

## [1.6.0](https://github.com/ngneat/transloco/compare/v1.5.0...v1.6.0) (2019-08-28)

### Features

- limit nested translations ([71e5488](https://github.com/ngneat/transloco/commit/71e5488))
- **directive:** add limit property ([7715deb](https://github.com/ngneat/transloco/commit/7715deb))
- **directive:** add limit property ([f17f417](https://github.com/ngneat/transloco/commit/f17f417))
- **directive:** read nested property ([b51359e](https://github.com/ngneat/transloco/commit/b51359e))
- **directive:** set context to a nested property ([561cba4](https://github.com/ngneat/transloco/commit/561cba4))

## [1.5.0](https://github.com/ngneat/transloco/compare/v1.4.2...v1.5.0) (2019-08-28)

### Bug Fixes

- 🐛 translate types ([fe51317](https://github.com/ngneat/transloco/commit/fe51317))

### Features

- 🎸 missing-handler ([23da8c5](https://github.com/ngneat/transloco/commit/23da8c5)), closes [#42](https://github.com/ngneat/transloco/issues/42)
- 🎸 missing-handler ([#43](https://github.com/ngneat/transloco/issues/43)) ([5f082fc](https://github.com/ngneat/transloco/commit/5f082fc))
- **loader:** allow it to be optional ([2a7fd83](https://github.com/ngneat/transloco/commit/2a7fd83))
- 🎸 service ([317bfc8](https://github.com/ngneat/transloco/commit/317bfc8)), closes [#46](https://github.com/ngneat/transloco/issues/46)

### Tests

- 💍 allow-empty ([ba33ea1](https://github.com/ngneat/transloco/commit/ba33ea1))

### [1.4.2](https://github.com/ngneat/transloco/compare/v1.4.0...v1.4.2) (2019-08-27)

### Bug Fixes

- 🐛 message-format ([ff835c3](https://github.com/ngneat/transloco/commit/ff835c3)), closes [#40](https://github.com/ngneat/transloco/issues/40)
- 🐛 migration script support for get function ([67e112d](https://github.com/ngneat/transloco/commit/67e112d))
- 🐛 translate ([620b151](https://github.com/ngneat/transloco/commit/620b151)), closes [#40](https://github.com/ngneat/transloco/issues/40)

### [1.4.1](https://github.com/ngneat/transloco/compare/v1.4.0...v1.4.1) (2019-08-27)

### Bug Fixes

- 🐛 message-format ([ff835c3](https://github.com/ngneat/transloco/commit/ff835c3)), closes [#40](https://github.com/ngneat/transloco/issues/40)
- 🐛 migration script support for get function ([67e112d](https://github.com/ngneat/transloco/commit/67e112d))
- 🐛 translate ([620b151](https://github.com/ngneat/transloco/commit/620b151)), closes [#40](https://github.com/ngneat/transloco/issues/40)

## [1.4.0](https://github.com/ngneat/transloco/compare/v1.2.0...v1.4.0) (2019-08-27)

### Bug Fixes

- 🎸 support using ng-add for libraries ([8ba6f25](https://github.com/ngneat/transloco/commit/8ba6f25))
- 🐛 global scoep translations in pipe and directive ([c4d0993](https://github.com/ngneat/transloco/commit/c4d0993))
- 🐛 global scope might not be loaded when sharing scope ([d094764](https://github.com/ngneat/transloco/commit/d094764))
- **api:** expose helpers and types ([19e1a7f](https://github.com/ngneat/transloco/commit/19e1a7f))

### Features

- 🎸 scope strategy - shared ([84eb7ec](https://github.com/ngneat/transloco/commit/84eb7ec))

### Tests

- 💍 finish testing ([104ecd3](https://github.com/ngneat/transloco/commit/104ecd3))
- 💍 fix cypress specs ([7fdfe11](https://github.com/ngneat/transloco/commit/7fdfe11))
- 💍 shared scope - update specs ([50f3362](https://github.com/ngneat/transloco/commit/50f3362))

## [1.3.0](https://github.com/ngneat/transloco/compare/v1.2.0...v1.3.0) (2019-08-26)

### Bug Fixes

- 🐛 global scoep translations in pipe and directive ([c4d0993](https://github.com/ngneat/transloco/commit/c4d0993))
- 🐛 global scope might not be loaded when sharing scope ([d094764](https://github.com/ngneat/transloco/commit/d094764))

### Features

- 🎸 scope strategy - shared ([84eb7ec](https://github.com/ngneat/transloco/commit/84eb7ec))

### Tests

- 💍 finish testing ([104ecd3](https://github.com/ngneat/transloco/commit/104ecd3))
- 💍 fix cypress specs ([7fdfe11](https://github.com/ngneat/transloco/commit/7fdfe11))
- 💍 shared scope - update specs ([50f3362](https://github.com/ngneat/transloco/commit/50f3362))

## [1.2.0](https://github.com/ngneat/transloco/compare/v1.0.1...v1.2.0) (2019-08-23)

### Bug Fixes

- 🐛 fix format message import ([f8815dc](https://github.com/ngneat/transloco/commit/f8815dc))
- 🐛 schematics ng-add import-default bug ([#29](https://github.com/ngneat/transloco/issues/29)) ([35229e0](https://github.com/ngneat/transloco/commit/35229e0))

### Build System

- add peerDependencies to package.json ([22b0af5](https://github.com/ngneat/transloco/commit/22b0af5))

### Features

- **lib:** 🎸 add messageformat support through custom transpiler ([#11](https://github.com/ngneat/transloco/issues/11)) ([1feb309](https://github.com/ngneat/transloco/commit/1feb309))
- 🎸 added ssr question to ng-add ([#30](https://github.com/ngneat/transloco/issues/30)) ([7bcd15d](https://github.com/ngneat/transloco/commit/7bcd15d))
- 🎸 lib ([ab5a6fb](https://github.com/ngneat/transloco/commit/ab5a6fb))

### [1.1.3](https://github.com/ngneat/transloco/compare/v1.0.1...v1.1.3) (2019-08-22)

### Bug Fixes

- 🐛 fix format message import ([f8815dc](https://github.com/ngneat/transloco/commit/f8815dc))
- 🐛 schematics ng-add import-default bug ([#29](https://github.com/ngneat/transloco/issues/29)) ([35229e0](https://github.com/ngneat/transloco/commit/35229e0))

### Build System

- add peerDependencies to package.json ([22b0af5](https://github.com/ngneat/transloco/commit/22b0af5))

### Features

- 🎸 added ssr question to ng-add ([#30](https://github.com/ngneat/transloco/issues/30)) ([7bcd15d](https://github.com/ngneat/transloco/commit/7bcd15d))
- **lib:** 🎸 add messageformat support through custom transpiler ([#11](https://github.com/ngneat/transloco/issues/11)) ([1feb309](https://github.com/ngneat/transloco/commit/1feb309))

### [1.1.2](https://github.com/ngneat/transloco/compare/v1.0.1...v1.1.2) (2019-08-22)

### Bug Fixes

- 🐛 fix format message import ([f8815dc](https://github.com/ngneat/transloco/commit/f8815dc))
- 🐛 schematics ng-add import-default bug ([#29](https://github.com/ngneat/transloco/issues/29)) ([35229e0](https://github.com/ngneat/transloco/commit/35229e0))

### Build System

- add peerDependencies to package.json ([22b0af5](https://github.com/ngneat/transloco/commit/22b0af5))

### Features

- 🎸 added ssr question to ng-add ([#30](https://github.com/ngneat/transloco/issues/30)) ([7bcd15d](https://github.com/ngneat/transloco/commit/7bcd15d))
- **lib:** 🎸 add messageformat support through custom transpiler ([#11](https://github.com/ngneat/transloco/issues/11)) ([1feb309](https://github.com/ngneat/transloco/commit/1feb309))

## [1.1.0](https://github.com/ngneat/transloco/compare/v1.0.1...v1.1.0) (2019-08-16)

### Features

- **lib:** 🎸 add messageformat support through custom transpiler ([#11](https://github.com/ngneat/transloco/issues/11)) ([1feb309](https://github.com/ngneat/transloco/commit/1feb309))

## 1.0.0 (2019-08-16)

### Bug Fixes

- 🐛 fix ngx-translate migration script ([dfe47bf](https://github.com/ngneat/transloco/commit/dfe47bf))
- 🐛 get value function ([2a9d0a4](https://github.com/ngneat/transloco/commit/2a9d0a4))
- 🐛 migration script special chars ([9f9a917](https://github.com/ngneat/transloco/commit/9f9a917))
- 🐛 missing dependencies, angular schematics path ([fb94489](https://github.com/ngneat/transloco/commit/fb94489))
- 🐛 missing handler should return the missing key ([05297f1](https://github.com/ngneat/transloco/commit/05297f1))
- 🐛 service load issues ([82b9321](https://github.com/ngneat/transloco/commit/82b9321))
- 🐛 update dependencies ([530bb30](https://github.com/ngneat/transloco/commit/530bb30))
- **migration:** fix script ([6fc9e7b](https://github.com/ngneat/transloco/commit/6fc9e7b))
- **query:** fix query params getter ([63a8224](https://github.com/ngneat/transloco/commit/63a8224))
- **schematics:** ng-add schematics fixes ([#35](https://github.com/ngneat/transloco/issues/35)) ([10d4146](https://github.com/ngneat/transloco/commit/10d4146))
- **schematics:** when the user chose http as loader add HttpClien… ([#54](https://github.com/ngneat/transloco/issues/54)) ([66b07f6](https://github.com/ngneat/transloco/commit/66b07f6))
- **schematics:** when the user chose http as loader add HttpClientModule ([e5e0766](https://github.com/ngneat/transloco/commit/e5e0766))
- **transloco-service:** pr fixes ([4f276d1](https://github.com/ngneat/transloco/commit/4f276d1))
- fix tests ([fc841bd](https://github.com/ngneat/transloco/commit/fc841bd))
- handle parser error when couldn't parse ([a016030](https://github.com/ngneat/transloco/commit/a016030))

### Build System

- **lock file:** update lock file ([f410e17](https://github.com/ngneat/transloco/commit/f410e17))

### Features

- **schematics:** added generator for transloco component ([#61](https://github.com/ngneat/transloco/issues/61)) ([82d8df3](https://github.com/ngneat/transloco/commit/82d8df3))
- **schematics:** added ng-add schematics command ([#32](https://github.com/ngneat/transloco/issues/32)) ([71c53f1](https://github.com/ngneat/transloco/commit/71c53f1))
- **schematics:** support project in ng-add ([#5](https://github.com/ngneat/transloco/issues/5)) ([3fbdd41](https://github.com/ngneat/transloco/commit/3fbdd41))
- **specs:** add cypress ([a2605dc](https://github.com/ngneat/transloco/commit/a2605dc))
- **specs:** add specs coverage ([7016f88](https://github.com/ngneat/transloco/commit/7016f88))
- **transloco-pipe:** support providerLang ([bc3e809](https://github.com/ngneat/transloco/commit/bc3e809))
- **transloco-service:** dependentless translate ([9ad22ad](https://github.com/ngneat/transloco/commit/9ad22ad))
- **transloco-service:** getTranslation should support get all ([e12e5f6](https://github.com/ngneat/transloco/commit/e12e5f6)), closes [#58](https://github.com/ngneat/transloco/issues/58)
- **transloco-service:** support multiple fallback langs ([0e630bc](https://github.com/ngneat/transloco/commit/0e630bc)), closes [#40](https://github.com/ngneat/transloco/issues/40)
- **transloco-service:** support translating using a callback ([067ad98](https://github.com/ngneat/transloco/commit/067ad98)), closes [#56](https://github.com/ngneat/transloco/issues/56)
- added global loading template token ([e4a87eb](https://github.com/ngneat/transloco/commit/e4a87eb))
- added global loading template token ([#55](https://github.com/ngneat/transloco/issues/55)) ([34183dd](https://github.com/ngneat/transloco/commit/34183dd))
- added setTranslation && setTranslationKey methods ([fdf2ade](https://github.com/ngneat/transloco/commit/fdf2ade))
- added support for multi key translation ([#46](https://github.com/ngneat/transloco/issues/46)) ([15378c5](https://github.com/ngneat/transloco/commit/15378c5))
- lazy load ([6dc1f72](https://github.com/ngneat/transloco/commit/6dc1f72))
- lazy load ([ee68219](https://github.com/ngneat/transloco/commit/ee68219))

### Tests

- 💍 add key is path test ([2406bd8](https://github.com/ngneat/transloco/commit/2406bd8))
- 💍 cypress ([fd3622b](https://github.com/ngneat/transloco/commit/fd3622b))
- 💍 update specs ([5f0629c](https://github.com/ngneat/transloco/commit/5f0629c))
- **transloco-transpiler:** add spec for nested lang keys ([9dc7875](https://github.com/ngneat/transloco/commit/9dc7875))
- add missing coverage specs ([101f1f1](https://github.com/ngneat/transloco/commit/101f1f1))
- added e2e test to lazy page ([f06c922](https://github.com/ngneat/transloco/commit/f06c922)), closes [#33](https://github.com/ngneat/transloco/issues/33)
- created change-translation e2e tests ([e693c12](https://github.com/ngneat/transloco/commit/e693c12))
- cypress prod url ([4c464a6](https://github.com/ngneat/transloco/commit/4c464a6))
- **transloco-service:** add specs ([3af51c6](https://github.com/ngneat/transloco/commit/3af51c6))
- **transloco-service:** fix pr ([58b6624](https://github.com/ngneat/transloco/commit/58b6624))
- test params pipe ([331b7e0](https://github.com/ngneat/transloco/commit/331b7e0))
- test params pipe ([#27](https://github.com/ngneat/transloco/issues/27)) ([1ffa3a2](https://github.com/ngneat/transloco/commit/1ffa3a2))
