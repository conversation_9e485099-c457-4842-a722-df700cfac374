<br />
<p align="center">
 <img width="50%" height="50%" src="./logo.svg">
</p>

> The internationalization (i18n) library for Angular

Transloco allows you to define translations for your content in different languages and switch between them easily in runtime.
It exposes a rich API to manage translations efficiently and cleanly. It provides multiple plugins that will improve your development experience. Here is a small taste of the features it offers:

✅ &nbsp;Clean and DRY templates  
✅ &nbsp;Support for Lazy Load  
✅ &nbsp;Support for Multiple Languages Simultaneously  
✅ &nbsp;Support for Multiple Fallbacks  
✅ &nbsp;Support for Testing  
✅ &nbsp;Support for SSR  
✅ &nbsp;Support for L10N  
✅ &nbsp;Rich Plugins  
✅ &nbsp;Hackable  
✅ &nbsp;Schematics  
✅ &nbsp;Standalone support

<hr />

[![Build Status](https://github.com/ngneat/transloco/actions/workflows/ci.yml/badge.svg)]()
[![commitizen](https://img.shields.io/badge/commitizen-friendly-brightgreen.svg?style=flat-square)]()
[![PRs](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)]()
[![coc-badge](https://img.shields.io/badge/codeof-conduct-ff69b4.svg?style=flat-square)]()
[![semantic-release](https://img.shields.io/badge/%20%20%F0%9F%93%A6%F0%9F%9A%80-semantic--release-e5079.svg?style=flat-square)](https://github.com/semantic-release/semantic-release)
[![styled with prettier](https://img.shields.io/badge/styled_with-prettier-ff69b4.svg?style=flat-square)](https://github.com/prettier/prettier)
[![spectator](https://img.shields.io/badge/tested%20with-spectator-2196F3.svg?style=flat-square)](https://github.com/ngneat/spectator)
[![Join the chat at https://gitter.im/ngneat-transloco](https://badges.gitter.im/gitterHQ/gitter.svg)](https://gitter.im/ngneat-transloco/lobby?source=orgpage)

- 🤓 &nbsp;Learn about it on the [docs site](https://ngneat.github.io/transloco/)
- 🎥 &nbsp;Watch our instructional [video guides](https://www.youtube.com/watch?v=MYkYcafJdGw&list=PLTuTW7EgL6ouXk5BqE4zWdDJkAuC4HTWi)
- 🚀 &nbsp;See it in action on [CodeSandbox](https://codesandbox.io/s/ngneat-transloco-kn52hs)
- 😎 &nbsp;Use [schematics](https://ngneat.github.io/transloco/docs/schematics)
- 👉 &nbsp;Checkout the [live application](https://ngneat.github.io/transloco/live-app)
- 📖 &nbsp;Read the blog [posts](https://ngneat.github.io/transloco/docs/blog-posts)
- 🍄 &nbsp;Join Transloco's [Gitter](https://gitter.im/ngneat-transloco/lobby?source=orgpage) room
- ❓ &nbsp;Find answers in our [FAQ](https://ngneat.github.io/transloco/docs/faq) section

## Core Team

<table>
  <tr>
    <td align="center"><a href="https://www.netbasal.com"><img src="https://avatars1.githubusercontent.com/u/6745730?v=4" width="100px;" alt="Netanel Basal"/><br /><sub><b>Netanel Basal</b></sub></a><br /></td>
    <td align="center"><a href="https://github.com/shaharkazaz"><img src="https://avatars2.githubusercontent.com/u/17194830?v=4" width="100px;" alt="Shahar Kazaz"/><br /><sub><b>Shahar Kazaz</b></sub></a><br /></td>
    </tr>
</table>

## Contributors ✨

Thank goes to all these wonderful [people who contributed](https://github.com/ngneat/transloco/graphs/contributors) ❤️
