/* auto-generated by NAPI-RS */
/* eslint-disable */
/**
 * This function get the priority of the current process.
 * On Unix, it uses the [`getpriority(2)`](https://linux.die.net/man/2/getpriority).
 *
 * On Windows, it uses the [`GetThreadPriority`](https://docs.microsoft.com/en-us/windows/win32/api/processthreadsapi/nf-processthreadsapi-getthreadpriority) function.
 *
 * | Priority Constant                  | Value     | Description                                                                                                                                                                                                                       |
 * |------------------------------------|-----------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
 * | THREAD_MODE_BACKGROUND_BEGIN       | 0x00010000| Begin background processing mode. The system lowers the resource scheduling priorities of the thread so that it can perform background work without significantly affecting activity in the foreground.                              |
 * |                                    |           | This value can be specified only if hThread is a handle to the current thread. The function fails if the thread is already in background processing mode.                                                                           |
 * |                                    |           | Windows Server 2003: This value is not supported.                                                                                                                                                                                  |
 * | THREAD_MODE_BACKGROUND_END         | 0x00020000| End background processing mode. The system restores the resource scheduling priorities of the thread as they were before the thread entered background processing mode.                                                            |
 * |                                    |           | This value can be specified only if hThread is a handle to the current thread. The function fails if the thread is not in background processing mode.                                                                               |
 * |                                    |           | Windows Server 2003: This value is not supported.                                                                                                                                                                                  |
 * | THREAD_PRIORITY_ABOVE_NORMAL       | 1         | Priority 1 point above the priority class.                                                                                                                                                                                         |
 * | THREAD_PRIORITY_BELOW_NORMAL       | -1        | Priority 1 point below the priority class.                                                                                                                                                                                         |
 * | THREAD_PRIORITY_HIGHEST            | 2         | Priority 2 points above the priority class.                                                                                                                                                                                        |
 * | THREAD_PRIORITY_IDLE               | -15       | Base priority of 1 for IDLE_PRIORITY_CLASS, BELOW_NORMAL_PRIORITY_CLASS, NORMAL_PRIORITY_CLASS, ABOVE_NORMAL_PRIORITY_CLASS, or HIGH_PRIORITY_CLASS processes, and a base priority of 16 for REALTIME_PRIORITY_CLASS processes.      |
 * | THREAD_PRIORITY_LOWEST             | -2        | Priority 2 points below the priority class.                                                                                                                                                                                        |
 * | THREAD_PRIORITY_NORMAL             | 0         | Normal priority for the priority class.                                                                                                                                                                                            |
 * | THREAD_PRIORITY_TIME_CRITICAL      | 15        | Base priority of 15 for IDLE_PRIORITY_CLASS, BELOW_NORMAL_PRIORITY_CLASS, NORMAL_PRIORITY_CLASS, ABOVE_NORMAL_PRIORITY_CLASS, or HIGH_PRIORITY_CLASS processes, and a base priority of 31 for REALTIME_PRIORITY_CLASS processes.     |
 */
export declare function getCurrentProcessPriority(): number

/**
 * This function set the priority of the current process.
 * On Unix, it uses the [`nice`](https://linux.die.net/man/2/nice) function.
 *
 * On Windows, it uses the [`SetThreadPriority`](https://docs.microsoft.com/en-us/windows/win32/api/processthreadsapi/nf-processthreadsapi-setthreadpriority) function.
 */
export declare function nice(incr?: number | undefined | null): number

export declare const enum WindowsThreadPriority {
  ThreadModeBackgroundBegin = 65536,
  ThreadModeBackgroundEnd = 131072,
  ThreadPriorityAboveNormal = 1,
  ThreadPriorityBelowNormal = -1,
  ThreadPriorityHighest = 2,
  ThreadPriorityIdle = -15,
  ThreadPriorityLowest = -2,
  ThreadPriorityNormal = 0,
  ThreadPriorityTimeCritical = 15
}

