import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { fuseAnimations } from 'src/app/modules/fuse/animations/animations';
import { TranslationService } from 'src/app/modules/translation/providers/translation.service';
import { TranslatableComponent } from 'src/app/modules/translation/types/translatable-component';

@Component({
    selector: 'app-features',
    templateUrl: './features.component.html',
    encapsulation: ViewEncapsulation.None,
    standalone: false,
    animations: fuseAnimations
})

export class FeaturesComponent extends TranslatableComponent implements OnInit {
    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector);
    }
    ngOnInit(): void { }
}
