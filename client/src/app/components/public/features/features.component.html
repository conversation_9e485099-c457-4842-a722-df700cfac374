<div class="dark w-full text-gray-100 min-h-screen">
    <!-- Header -->
    <auth-header></auth-header>

    <!-- Features Section -->
    <section id="features" class="py-16 sm:py-24 dark w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div @fadeInTop class="text-center mb-12 sm:mb-16">
                <h2 class="text-3xl sm:text-4xl font-extrabold text-gray-100">{{ 'WHY_CHOOSE_US' | translate | async }}</h2>
                <p class="mt-4 text-lg text-gray-300">
                    {{ 'PLATFORM_FEATURES_DESCRIPTION' | translate | async }}
                </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-12">
                <!-- Feature 1 -->
                <div @fadeInTop
                    class="dark brightness-125 p-8 rounded-lg shadow-xl hover:shadow-2xl transition-shadow duration-300">
                    <div class="flex items-center justify-center h-12 w-12 rounded-full bg-primary-500 text-white mb-6">
                        <div class="w-8 h-8">
                            <img src="assets/images/logo/logo.svg" class="filter invert brightness-0 w-full h-full" />
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-100 mb-3">{{ 'FEATURE_1_TITLE' | translate | async }}</h3>
                    <p class="text-gray-300 text-sm leading-relaxed">
                        {{ 'FEATURE_1_DESCRIPTION' | translate | async }}
                    </p>
                </div>
                <!-- Feature 2 -->
                <div @fadeInTop
                    class="dark brightness-125 p-8 rounded-lg shadow-xl hover:shadow-2xl transition-shadow duration-300">
                    <div class="flex items-center justify-center h-12 w-12 rounded-full bg-primary-500 text-white mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-100 mb-3">{{ 'FEATURE_2_TITLE' | translate | async }}</h3>
                    <p class="text-gray-300 text-sm leading-relaxed">
                        {{ 'FEATURE_2_DESCRIPTION' | translate | async }}
                    </p>
                </div>
                <!-- Feature 3 -->
                <div @fadeInTop
                    class="dark brightness-125 p-8 rounded-lg shadow-xl hover:shadow-2xl transition-shadow duration-300">
                    <div class="flex items-center justify-center h-12 w-12 rounded-full bg-primary-500 text-white mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-100 mb-3">{{ 'FEATURE_3_TITLE' | translate | async }}</h3>
                    <p class="text-gray-300 text-sm leading-relaxed">
                        {{ 'FEATURE_3_DESCRIPTION' | translate | async }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section id="cta" class="py-16 sm:py-24 dark brightness-90 w-full">
        <div class="mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl sm:text-4xl font-extrabold text-gray-100">
                {{ 'CTA_TITLE' | translate | async }}
            </h2>
            <p class="mt-4 text-lg text-gray-300 leading-relaxed">
                {{ 'CTA_DESCRIPTION' | translate | async }}
            </p>
            <div class="mt-10">
                <button @fadeIn routerLink="/pricing"
                    class="bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-10 rounded-md text-lg transition-colors duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-opacity-50 shadow-lg hover:shadow-xl">
                    {{ 'CTA_BUTTON' | translate | async }}
                </button>
            </div>
        </div>
    </section>
</div>
