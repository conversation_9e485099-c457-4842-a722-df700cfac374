import { Component, OnInit, AfterViewInit, ChangeDetectorRef, ViewEncapsulation } from '@angular/core';
import { fuseAnimations } from '../../../modules/fuse/animations/animations';
import { TranslationService } from '../../../modules/translation/providers/translation.service';
import { TranslatableComponent } from '../../../modules/translation/types/translatable-component';

@Component({
    selector: 'auth-page-not-found',
    templateUrl: './page-not-found.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: false,
})
export class PageNotFoundComponent extends TranslatableComponent implements OnInit, AfterViewInit {
    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit() {
    }

    ngAfterViewInit() {
        window.scroll(0, 0);
    }
}
