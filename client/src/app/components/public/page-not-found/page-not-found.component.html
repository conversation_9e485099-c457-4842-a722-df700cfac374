<style>
    @keyframes SCALE-BG {
        0% {
            background-size: 100%;
        }
        50% { 
            background-size: 200%;
        }
        100% {
            background-size: 100%;
        }
    }

    #animated-background {
        background-image: url('src/assets/images/backgrounds/page-not-found.avif');
        background-size: 100%;
        background-repeat: repeat;
        background-position: center;
        
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;

        z-index: 99;

        animation-name: SCALE-BG;
        animation-duration: 30s;
        animation-timing-function: ease-in-out;
        animation-iteration-count: infinite;
    }

    #page-not-found-container {
        background: rgba(30, 30, 30, 0.7);
        padding-top: 12rem;
        margin-bottom: -8rem;
        text-align: center;

        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;

        z-index: 100;

    }

    #very-large-text {
        font-size: 12rem;
        color: rgba(255, 255, 255, 0.9);
    }

    .separator {
        margin: 0 auto;
        margin-top: 1rem;
        margin-bottom: 1rem;
        width: 60%;
        height: 1px;
        background: #fff;
    }

    #large-text {
        margin-top: 2rem;
        font-size: 3rem;
        color: rgba(255, 255, 255, 0.8);
    }

    #small-text {
        font-size: 1.2rem;
        color: #aaa;
    }

    a,
    a:link,
    a:active,
    a:visited {
        color: hsl(214, 100%, 79%);
        text-decoration: none;
    }

    a:hover {
        color: hsl(214, 100%, 91%);
    }
</style>

<div id="animated-background">
    <div id="page-not-found-container">
        <span id="very-large-text">404</span>
        <div class="separator"></div>
        <div id="large-text">{{ 'Requested page was not found' | translate | async }}</div>
        <br />
        <br />
        <div id="small-text">{{ 'You can return to ' | translate | async }} <a [routerLink]="''">{{ 'Homepage' | translate | async }}</a></div>
    </div>
</div>