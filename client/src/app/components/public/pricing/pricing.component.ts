import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Observable } from 'rxjs';
import { fuseAnimations } from 'src/app/modules/fuse/animations/animations';
import { TranslationService } from 'src/app/modules/translation/providers/translation.service';
import { TranslatableComponent } from 'src/app/modules/translation/types/translatable-component';

export interface PricingPlan {
    name: Observable<string>;
    price: number;
    currency: string;
    period: Observable<string>;
    description: Observable<string>;
    features: Observable<string>[];
    highlighted: boolean;
    buttonText: Observable<string>;
    buttonLink: string;
}

@Component({
    selector: 'app-pricing',
    templateUrl: './pricing.component.html',
    encapsulation: ViewEncapsulation.None,
    standalone: false,
    animations: fuseAnimations,
})
export class PricingComponent extends TranslatableComponent implements OnInit {

    pricingPlans: PricingPlan[] = [
        {
            name: this.translate('PRICING_PLAN_STARTER_NAME'),
            price: 9,
            currency: '€',
            period: this.translate('PRICING_PERIOD_MONTH'),
            description: this.translate('PRICING_PLAN_STARTER_DESCRIPTION'),
            features: [
                this.translate('PRICING_FEATURE_UP_TO_5_PROJECTS'),
                this.translate('PRICING_FEATURE_10GB_STORAGE'),
                this.translate('PRICING_FEATURE_EMAIL_SUPPORT'),
                this.translate('PRICING_FEATURE_BASIC_FEATURES'),
                this.translate('PRICING_FEATURE_MOBILE_APP')
            ],
            highlighted: false,
            buttonText: this.translate('PRICING_PLAN_STARTER_BUTTON'),
            buttonLink: '/sign-in'
        },
        {
            name: this.translate('PRICING_PLAN_PROFESSIONAL_NAME'),
            price: 29,
            currency: '€',
            period: this.translate('PRICING_PERIOD_MONTH'),
            description: this.translate('PRICING_PLAN_PROFESSIONAL_DESCRIPTION'),
            features: [
                this.translate('PRICING_FEATURE_UNLIMITED_PROJECTS'),
                this.translate('PRICING_FEATURE_100GB_STORAGE'),
                this.translate('PRICING_FEATURE_PRIORITY_SUPPORT'),
                this.translate('PRICING_FEATURE_ADVANCED_FEATURES'),
                this.translate('PRICING_FEATURE_TEAM_COLLABORATION'),
                this.translate('PRICING_FEATURE_API_ACCESS'),
                this.translate('PRICING_FEATURE_CUSTOM_INTEGRATIONS')
            ],
            highlighted: true,
            buttonText: this.translate('PRICING_PLAN_PROFESSIONAL_BUTTON'),
            buttonLink: '/sign-in'
        },
        {
            name: this.translate('PRICING_PLAN_ENTERPRISE_NAME'),
            price: 99,
            currency: '€',
            period: this.translate('PRICING_PERIOD_MONTH'),
            description: this.translate('PRICING_PLAN_ENTERPRISE_DESCRIPTION'),
            features: [
                this.translate('PRICING_FEATURE_EVERYTHING_PROFESSIONAL'),
                this.translate('PRICING_FEATURE_UNLIMITED_STORAGE'),
                this.translate('PRICING_FEATURE_24_7_SUPPORT'),
                this.translate('PRICING_FEATURE_WHITE_LABEL'),
                this.translate('PRICING_FEATURE_ON_PREMISE'),
                this.translate('PRICING_FEATURE_SLA_GUARANTEE'),
                this.translate('PRICING_FEATURE_CUSTOM_DEVELOPMENT')
            ],
            highlighted: false,
            buttonText: this.translate('PRICING_PLAN_ENTERPRISE_BUTTON'),
            buttonLink: '/contact'
        }
    ];

    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void { }
}
