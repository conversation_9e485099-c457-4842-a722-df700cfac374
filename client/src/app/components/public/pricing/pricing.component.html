<div class="dark w-full text-gray-100 min-h-screen overflow-x-hidden">
    <!-- Header -->
    <auth-header></auth-header>

    <!-- Hero Section -->
    <section id="pricing-hero" class="py-16 sm:py-24 dark w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div @fadeInTop class="text-center mb-12 sm:mb-16">
                <h1 class="text-4xl sm:text-5xl font-extrabold text-gray-100 mb-6">
                    {{ 'PRICING_HERO_TITLE' | translate | async }} <span class="text-primary-400">Plan</span>
                </h1>
                <p class="mt-4 text-lg sm:text-xl text-gray-300 mx-auto leading-relaxed">
                    {{ 'PRICING_HERO_DESCRIPTION' | translate | async }}
                </p>
            </div>
        </div>
    </section>

    <!-- Pricing Cards Section -->
    <section id="pricing-plans" class="py-16 sm:py-24 dark brightness-90 w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-12 mx-auto">

                <!-- Pricing Plan Card -->
                @for (plan of pricingPlans; track $index) {
                <div @fadeInTop [style.animation-delay]="($index * 150) + 'ms'"
                    class="relative dark brightness-125 rounded-lg shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
                    [class.ring-4]="plan.highlighted" [class.ring-primary-500]="plan.highlighted"
                    [class.ring-opacity-50]="plan.highlighted">

                    <!-- Popular Badge -->
                    @if (plan.highlighted) {
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-primary-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                            {{ 'PRICING_POPULAR_BADGE' | translate | async }}
                        </span>
                    </div>
                    }

                    <div class="p-6 sm:p-8">
                        <!-- Plan Header -->
                        <div class="text-center mb-6 sm:mb-8">
                            <h3 class="text-xl sm:text-2xl font-bold text-gray-100 mb-2">{{ plan.name | async }}</h3>
                            <p class="text-gray-300 text-sm sm:text-base mb-4 sm:mb-6 px-2">{{ plan.description | async }}</p>

                            <!-- Price -->
                            <div class="flex items-baseline justify-center mb-4 sm:mb-6">
                                <span class="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-gray-100">{{
                                    plan.currency }}{{ plan.price }}</span>
                                <span class="text-gray-300 ml-2 text-sm sm:text-base">/ {{ plan.period | async }}</span>
                            </div>
                        </div>

                        <!-- Features List -->
                        <ul class="space-y-4 mb-8">
                            @for (feature of plan.features; track feature) {
                            <li class="flex items-start">
                                <svg class="flex-shrink-0 w-5 h-5 text-primary-500 mt-0.5 mr-3" fill="currentColor"
                                    viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300 text-sm">{{ feature | async }}</span>
                            </li>
                            }
                        </ul>

                        <!-- CTA Button -->
                        <div class="text-center">
                            <button [routerLink]="plan.buttonLink"
                                [class]="plan.highlighted ?
                  'w-full bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-md text-lg transition-colors duration-300 ease-in-out focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-opacity-50 shadow-lg hover:shadow-xl' :
                  'w-full bg-gray-700 hover:bg-gray-600 text-gray-100 font-semibold py-3 px-6 rounded-md text-lg transition-colors duration-300 ease-in-out focus:outline-none focus:ring-4 focus:ring-gray-500 focus:ring-opacity-50 shadow-lg hover:shadow-xl'">
                                {{ plan.buttonText | async }}
                            </button>
                        </div>
                    </div>
                </div>
                }

            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="pricing-faq" class="py-16 sm:py-24 dark w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div @fadeInTop class="text-center mb-12 sm:mb-16">
                <h2 class="text-3xl sm:text-4xl font-extrabold text-gray-100">{{ 'PRICING_FAQ_TITLE' | translate | async }}</h2>
                <p class="mt-4 text-lg text-gray-300">
                    {{ 'PRICING_FAQ_DESCRIPTION' | translate | async }}
                </p>
            </div>

            <div class="space-y-8">
                <div @fadeInTop class="dark brightness-125 p-6 rounded-lg shadow-xl">
                    <h3 class="text-xl font-semibold text-gray-100 mb-3">{{ 'PRICING_FAQ_1_QUESTION' | translate | async }}</h3>
                    <p class="text-gray-300 leading-relaxed">
                        {{ 'PRICING_FAQ_1_ANSWER' | translate | async }}
                    </p>
                </div>

                <div @fadeInTop class="dark brightness-125 p-6 rounded-lg shadow-xl">
                    <h3 class="text-xl font-semibold text-gray-100 mb-3">{{ 'PRICING_FAQ_2_QUESTION' | translate | async }}</h3>
                    <p class="text-gray-300 leading-relaxed">
                        {{ 'PRICING_FAQ_2_ANSWER' | translate | async }}
                    </p>
                </div>

                <div @fadeInTop class="dark brightness-125 p-6 rounded-lg shadow-xl">
                    <h3 class="text-xl font-semibold text-gray-100 mb-3">{{ 'PRICING_FAQ_3_QUESTION' | translate | async }}</h3>
                    <p class="text-gray-300 leading-relaxed">
                        {{ 'PRICING_FAQ_3_ANSWER' | translate | async }}
                    </p>
                </div>
            </div>

            <div @fadeInTop class="text-center mt-12">
                <a routerLink="/faq"
                    class="inline-flex items-center text-primary-400 hover:text-primary-500 font-medium text-lg transition-colors">
                    {{ 'PRICING_FAQ_LINK' | translate | async }}
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>
</div>
