<div class="dark w-full px-0 py-0 sm:bg-card">
    <!-- Header -->
    <auth-header></auth-header>

    <!-- Hero Section -->
    <section id="hero"
        class="relative min-h-[97vh] flex flex-col items-center justify-center text-white overflow-hidden p-0 md:p-0">
        <div class="absolute inset-0 -z-10 bg-cover bg-center transition-transform duration-100 ease-out"
            [style.background-image]="'url(\'assets/images/backgrounds/home-hero.jpg\')'"
            [style.transform]="'translateY(' + parallaxOffset + 'px)'">
        </div>
        <div class="relative z-10 flex flex-col items-center text-center max-w-4xl mx-auto">
            <h1 @fadeInTop
                class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight tracking-tight">
                {{ 'HOME_HERO_TITLE' | translate | async }} <span class="text-primary-400">Fuse</span>
            </h1>
            <p @fadeInTop class="text-lg sm:text-xl md:text-2xl mb-10 text-gray-200 font-normal max-w-2xl">
                {{ heroQuote | async }} {{ 'HOME_HERO_DESCRIPTION' | translate | async }}
            </p>
            <button @fadeIn routerLink="/features"
                class="bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-10 rounded-md text-lg transition-colors duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-opacity-50 shadow-lg hover:shadow-xl">
                {{ 'HOME_HERO_BUTTON' | translate | async }}
            </button>
        </div>
        <div @fadeIn class="absolute bottom-10 text-center w-full">
            <a (click)="smoothScrollTo('mission-section', $event)" class="inline-block cursor-pointer group"
                aria-label="Zur nächsten Sektion scrollen">
                <svg class="w-8 h-8 animate-bounce text-gray-400 group-hover:text-primary-400 transition-colors duration-300"
                    fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path d="M19 9l-7 7-7-7"></path>
                </svg>
            </a>
        </div>
    </section>

    <!-- Sektion 1 -->
    <section id="mission-section" class="dark w-full px-4 py-8 sm:bg-card brightness-90">
        <div class="container mx-auto px-6 lg:px-8 text-center max-w-3xl">
            <h2 @fadeInTop class="text-3xl md:text-4xl font-semibold mb-6 tracking-tight text-gray-100">{{ 'HOME_MISSION_TITLE' | translate | async }}
            </h2>
            <p @fadeInTop class="text-lg md:text-xl mb-8 text-gray-300 leading-relaxed">
                {{ 'HOME_MISSION_DESCRIPTION' | translate | async }}
            </p>
            <a @fadeIn href="mailto:<EMAIL>"
                class="text-primary-400 hover:text-primary-500 font-medium text-lg transition-colors">
                {{ 'HOME_MISSION_LINK' | translate | async }} &rarr;
            </a>
        </div>
    </section>

    <!-- Sektion 2: Testimonials -->
    <section class="dark w-full px-4 py-8 sm:bg-card ">
        <div class="container mx-auto px-6 lg:px-8">
            <h2 @fadeInTop class="text-3xl md:text-4xl font-semibold mb-12 tracking-tight text-center text-gray-100">{{ 'HOME_TESTIMONIALS_TITLE' | translate | async }}</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div @fadeInTop
                    class="dark sm:bg-card brightness-125 p-8 rounded-lg shadow-xl hover:shadow-2xl transition-shadow duration-300">
                    <p class="italic text-gray-300 mb-4 text-lg">"{{ 'HOME_TESTIMONIAL_1_TEXT' | translate | async }}"</p>
                    <p class="font-semibold text-gray-100">- {{ 'HOME_TESTIMONIAL_1_AUTHOR' | translate | async }}</p>
                </div>
                <div @fadeInTop
                    class="dark sm:bg-card brightness-125 p-8 rounded-lg shadow-xl hover:shadow-2xl transition-shadow duration-300"
                    style="animation-delay: 150ms;">
                    <p class="italic text-gray-300 mb-4 text-lg">"{{ 'HOME_TESTIMONIAL_2_TEXT' | translate | async }}"</p>
                    <p class="font-semibold text-gray-100">- {{ 'HOME_TESTIMONIAL_2_AUTHOR' | translate | async }}</p>
                </div>
                <div @fadeInTop
                    class="dark sm:bg-card brightness-125 p-8 rounded-lg shadow-xl hover:shadow-2xl transition-shadow duration-300"
                    style="animation-delay: 300ms;">
                    <p class="italic text-gray-300 mb-4 text-lg">"{{ 'HOME_TESTIMONIAL_3_TEXT' | translate | async }}"</p>
                    <p class="font-semibold text-gray-100">- {{ 'HOME_TESTIMONIAL_3_AUTHOR' | translate | async }}</p>
                </div>
            </div>
        </div>
    </section>
</div>
