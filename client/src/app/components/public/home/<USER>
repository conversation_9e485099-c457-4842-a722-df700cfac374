import { Component, HostListener, OnInit, Inject, PLATFORM_ID, ViewEncapsulation, ChangeDetectorRef } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Observable } from 'rxjs';
import { fuseAnimations } from 'src/app/modules/fuse/animations/animations';
import { TranslatableComponent } from 'src/app/modules/translation/types/translatable-component';
import { TranslationService } from 'src/app/modules/translation/providers/translation.service';

@Component({
    selector: 'app-home',
    standalone: false,
    templateUrl: './home.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
})
export class HomeComponent extends TranslatableComponent implements OnInit {
    parallaxOffset: number = 0;
    heroQuote: Observable<string> = this.translate('HOME_HERO_QUOTE');

    constructor(
        @Inject(PLATFORM_ID) private platformId: Object,
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector);
    }
    ngOnInit(): void {
    }

    @HostListener('window:scroll', ['$event'])
    onWindowScroll() {
        if (isPlatformBrowser(this.platformId)) {
            this.parallaxOffset = window.pageYOffset * 0.4;
        }
    }

    smoothScrollTo(elementId: string, event?: MouseEvent): void {
        if (event) {
            event.preventDefault();
        }
        if (isPlatformBrowser(this.platformId)) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
            }
        }
    }
}
