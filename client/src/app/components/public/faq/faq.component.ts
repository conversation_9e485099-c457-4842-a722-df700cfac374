import { Component, OnInit, ViewEncapsulation, PLATFORM_ID, Inject, ChangeDetectorRef } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { fuseAnimations } from 'src/app/modules/fuse/animations/animations';
import { TranslatableComponent } from 'src/app/modules/translation/types/translatable-component';
import { TranslationService } from 'src/app/modules/translation/providers/translation.service';

interface FaqCategory {
    name: string;
    icon: string;
    questions: FaqItem[];
}

interface FaqItem {
    question: string;
    answer: string;
    expanded?: boolean;
}

@Component({
    selector: 'app-faq',
    templateUrl: './faq.component.html',
    encapsulation: ViewEncapsulation.None,
    standalone: false,
    animations: fuseAnimations,
})
export class FaqComponent extends TranslatableComponent implements OnInit {

    faqCategories: FaqCategory[] = [
        {
            name: 'FAQ_CATEGORY_GENERAL',
            icon: 'heroicons_outline:information-circle',
            questions: [
                {
                    question: 'FAQ_GENERAL_Q1',
                    answer: 'FAQ_GENERAL_A1'
                },
                {
                    question: 'FAQ_GENERAL_Q2',
                    answer: 'FAQ_GENERAL_A2'
                },
                {
                    question: 'FAQ_GENERAL_Q3',
                    answer: 'FAQ_GENERAL_A3'
                }
            ]
        },
        {
            name: 'FAQ_CATEGORY_PRICING',
            icon: 'heroicons_outline:currency-euro',
            questions: [
                {
                    question: 'FAQ_PRICING_Q1',
                    answer: 'FAQ_PRICING_A1'
                },
                {
                    question: 'FAQ_PRICING_Q2',
                    answer: 'FAQ_PRICING_A2'
                },
                {
                    question: 'FAQ_PRICING_Q3',
                    answer: 'FAQ_PRICING_A3'
                },
                {
                    question: 'FAQ_PRICING_Q4',
                    answer: 'FAQ_PRICING_A4'
                }
            ]
        },
        {
            name: 'FAQ_CATEGORY_FEATURES',
            icon: 'heroicons_outline:wrench-screwdriver',
            questions: [
                {
                    question: 'FAQ_FEATURES_Q1',
                    answer: 'FAQ_FEATURES_A1'
                },
                {
                    question: 'FAQ_FEATURES_Q2',
                    answer: 'FAQ_FEATURES_A2'
                },
                {
                    question: 'FAQ_FEATURES_Q3',
                    answer: 'FAQ_FEATURES_A3'
                }
            ]
        },
        {
            name: 'FAQ_CATEGORY_SECURITY',
            icon: 'heroicons_outline:shield-check',
            questions: [
                {
                    question: 'FAQ_SECURITY_Q1',
                    answer: 'FAQ_SECURITY_A1'
                },
                {
                    question: 'FAQ_SECURITY_Q2',
                    answer: 'FAQ_SECURITY_A2'
                },
                {
                    question: 'FAQ_SECURITY_Q3',
                    answer: 'FAQ_SECURITY_A3'
                }
            ]
        }
    ];

    constructor(
        @Inject(PLATFORM_ID) private platformId: Object,
        private route: ActivatedRoute,
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void {
        if (isPlatformBrowser(this.platformId)) {
            this.route.fragment.subscribe(fragment => {
                if (fragment) {
                    setTimeout(() => {
                        this.smoothScrollTo(fragment);
                    }, 300);
                }
            });
        }
    }

    toggleQuestion(question: FaqItem): void {
        question.expanded = !question.expanded;
    }

    smoothScrollTo(elementId: string, event?: MouseEvent): void {
        if (event) {
            event.preventDefault();
        }

        if (isPlatformBrowser(this.platformId)) {
            const element = document.getElementById(elementId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
            }
        }
    }

    getCategoryId(name: string): string {
        // Convert translation key to ID (e.g., 'FAQ_CATEGORY_GENERAL' -> 'faq-category-general')
        return name.toLowerCase().replace(/_/g, '-');
    }
}
