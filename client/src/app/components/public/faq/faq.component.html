<div class="dark w-full text-gray-100 min-h-screen overflow-x-hidden">
    <!-- Header -->
    <auth-header></auth-header>

    <!-- Hero Section -->
    <section id="faq-hero" class="py-16 sm:py-24 dark w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div @fadeInTop class="text-center mb-12 sm:mb-16">
                <h1 class="text-4xl sm:text-5xl font-extrabold text-gray-100 mb-6">
                    {{ 'FAQ_HERO_TITLE' | translate | async }}
                </h1>
                <p class="mt-4 text-lg sm:text-xl text-gray-300 mx-auto leading-relaxed max-w-3xl">
                    {{ 'FAQ_HERO_DESCRIPTION' | translate | async }}
                </p>
            </div>
        </div>
    </section>

    <!-- FAQ Categories Section -->
    <section id="faq-categories" class="py-16 sm:py-24 dark brightness-90 w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12 mx-auto mb-16">

                @for (category of faqCategories; track $index) {
                <div @fadeInTop [style.animation-delay]="($index * 150) + 'ms'"
                    class="dark brightness-125 p-6 rounded-lg shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 cursor-pointer"
                    (click)="smoothScrollTo(getCategoryId(category.name), $event)">

                    <div class="flex items-center mb-4">
                        <div
                            class="flex items-center justify-center h-12 w-12 rounded-full bg-primary-500 text-white mr-4">
                            <mat-icon [svgIcon]="category.icon"></mat-icon>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-100">{{ category.name | translate | async }}</h3>
                    </div>

                    <p class="text-gray-300 text-sm">
                        {{ category.questions.length }} {{ 'FAQ_QUESTIONS_COUNT' | translate | async }}
                    </p>
                </div>
                }

            </div>

            <!-- FAQ Questions and Answers -->
            @for (category of faqCategories; track $index) {
            <div @fadeInTop [id]="getCategoryId(category.name)" class="mb-16">

                <div class="flex items-center mb-8">
                    <div class="flex items-center justify-center h-12 w-12 rounded-full bg-primary-500 text-white mr-4">
                        <mat-icon [svgIcon]="category.icon"></mat-icon>
                    </div>
                    <h2 class="text-2xl sm:text-3xl font-bold text-gray-100">{{ category.name | translate | async }}</h2>
                </div>

                <div class="space-y-6">
                    @for (question of category.questions; track $index) {
                    <div class="dark brightness-125 rounded-lg shadow-xl overflow-hidden">
                        <!-- Question Header -->
                        <div class="p-6 flex justify-between items-center cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
                            (click)="toggleQuestion(question)">
                            <h3 class="text-xl font-semibold text-gray-100">{{ question.question | translate | async }}</h3>
                            <mat-icon class="text-primary-400 transition-transform duration-300"
                                [ngClass]="{'rotate-180': question.expanded}"
                                [svgIcon]="'heroicons_outline:chevron-down'">
                            </mat-icon>
                        </div>

                        <!-- Answer -->
                        <div [@expandCollapse]="question.expanded ? 'expanded' : 'collapsed'" class="px-6 pb-6">
                            <p class="text-gray-300 leading-relaxed">
                                {{ question.answer | translate | async }}
                            </p>
                        </div>
                    </div>
                    }
                </div>
            </div>
            }

        </div>
    </section>

    <!-- Call to Action Section -->
    <section id="faq-cta" class="py-16 sm:py-24 dark w-full">
        <div class="mx-auto text-center px-4 sm:px-6 lg:px-8 max-w-4xl">
            <h2 @fadeInTop class="text-3xl sm:text-4xl font-extrabold text-gray-100">
                {{ 'FAQ_CTA_TITLE' | translate | async }}
            </h2>
            <p @fadeInTop class="mt-4 text-lg text-gray-300 leading-relaxed">
                {{ 'FAQ_CTA_DESCRIPTION' | translate | async }}
            </p>
            <div
                class="mt-10 flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                <button @fadeIn href="mailto:<EMAIL>"
                    class="bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-10 rounded-md text-lg transition-colors duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-opacity-50 shadow-lg hover:shadow-xl w-full sm:w-auto">
                    {{ 'FAQ_CTA_CONTACT_BUTTON' | translate | async }}
                </button>
                <button @fadeIn style="animation-delay: 200ms;" routerLink="/pricing"
                    class="bg-gray-700 hover:bg-gray-600 text-gray-100 font-semibold py-3 px-10 rounded-md text-lg transition-colors duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-gray-500 focus:ring-opacity-50 shadow-lg hover:shadow-xl w-full sm:w-auto">
                    {{ 'FAQ_CTA_PRICING_BUTTON' | translate | async }}
                </button>
            </div>
        </div>
    </section>
</div>
