import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { TranslationService } from 'src/app/modules/translation/providers/translation.service';
import { TranslatableComponent } from 'src/app/modules/translation/types/translatable-component';

@Component({
    selector: 'app-tos',
    templateUrl: './tos.component.html',
    encapsulation: ViewEncapsulation.None,
    standalone: false,
})
export class TosComponent extends TranslatableComponent implements OnInit {
    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector)
    }

    ngOnInit(): void { }
}
