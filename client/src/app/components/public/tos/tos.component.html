<div class="dark w-full text-gray-100 min-h-screen overflow-x-hidden">
    <!-- Header -->
    <auth-header></auth-header>

    <!-- Hero Section -->
    <section id="tos-hero" class="py-16 sm:py-24 dark w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16">
                <h1 class="text-4xl sm:text-5xl font-extrabold text-gray-100 mb-6">
                    {{ 'TOS_HERO_TITLE' | translate | async }}
                </h1>
                <p class="mt-4 text-lg sm:text-xl text-gray-300 mx-auto leading-relaxed max-w-3xl">
                    {{ 'TOS_HERO_SUBTITLE' | translate | async }}
                </p>
            </div>
        </div>
    </section>

    <!-- Content Section -->
    <section class="py-8 sm:py-12 dark brightness-90 w-full">
        <div class="w-full mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl">
            <div class="prose prose-lg prose-invert mx-auto">

                <h2>{{ 'TOS_SECTION_1_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_1_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_1_CONTENT_2' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_2_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_2_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_2_CONTENT_2' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_2_CONTENT_3' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_3_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_3_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_3_CONTENT_2' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_3_CONTENT_3' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_4_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_4_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_4_CONTENT_2' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_4_CONTENT_3' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_5_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_5_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_5_CONTENT_2' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_5_CONTENT_3' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_6_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_6_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_6_CONTENT_2' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_6_CONTENT_3' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_6_CONTENT_4' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_7_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_7_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_7_CONTENT_2' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_7_CONTENT_3' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_8_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_8_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_8_CONTENT_2' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_8_CONTENT_3' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_8_CONTENT_4' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_9_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_9_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_9_CONTENT_2' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_10_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_10_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_10_CONTENT_2' | translate | async }}
                </p>

                <h2>{{ 'TOS_SECTION_11_TITLE' | translate | async }}</h2>
                <p>
                    {{ 'TOS_SECTION_11_CONTENT_1' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_11_CONTENT_2' | translate | async }}
                </p>
                <p>
                    {{ 'TOS_SECTION_11_CONTENT_3' | translate | async }}
                </p>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section id="tos-cta" class="py-16 sm:py-24 dark w-full">
        <div class="mx-auto text-center px-4 sm:px-6 lg:px-8 max-w-4xl">
            <h2 class="text-3xl sm:text-4xl font-extrabold text-gray-100">
                {{ 'TOS_CTA_TITLE' | translate | async }}
            </h2>
            <p class="mt-4 text-lg text-gray-300 leading-relaxed">
                {{ 'TOS_CTA_DESCRIPTION' | translate | async }}
            </p>
            <div
                class="mt-10 flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                <button href="mailto:<EMAIL>"
                    class="bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-10 rounded-md text-lg transition-colors duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary-500 focus:ring-opacity-50 shadow-lg hover:shadow-xl w-full sm:w-auto">
                    {{ 'TOS_CTA_BUTTON' | translate | async }}
                </button>
            </div>
        </div>
    </section>
</div>
