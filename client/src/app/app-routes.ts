import { Route } from '@angular/router';
import { DEFAULT_APP_ROUTE } from './app.constants';
import { AUTH_ROUTES } from './modules/auth/auth.module';
import { PageNotFoundComponent } from './components/public/page-not-found/page-not-found.component';
import { HomeComponent } from './components/public/home/<USER>';
import { FeaturesComponent } from './components/public/features/features.component';
import { PricingComponent } from './components/public/pricing/pricing.component';
import { FaqComponent } from './components/public/faq/faq.component';
import { TosComponent } from './components/public/tos/tos.component';
import { ImprintComponent } from './components/public/imprint/imprint.component';
import { PrivacyPolicyComponent } from './components/public/privacy-policy/privacy-policy.component';
import { LayoutComponent } from './modules/layout/components/layout/layout.component';

export const APP_ROUTES: Route[] = [
    {
        path: '',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: '', component: HomeComponent }
        ]
    },
    {
        path: 'features',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: '', component: FeaturesComponent }
        ]
    },
    {
        path: 'pricing',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: '', component: PricingComponent }
        ]
    },
    {
        path: 'faq',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: '', component: FaqComponent }
        ]
    },
    {
        path: 'terms-and-conditions',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: '', component: TosComponent }
        ]
    },
    {
        path: 'imprint',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: '', component: ImprintComponent }
        ]
    },
    {
        path: 'privacy-policy',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [
            { path: '', component: PrivacyPolicyComponent }
        ]
    },

    ...AUTH_ROUTES,

    { path: '404', component: PageNotFoundComponent },

    // Unknown pages
    {
        path: '**',
        redirectTo: '/404'
    },
];
