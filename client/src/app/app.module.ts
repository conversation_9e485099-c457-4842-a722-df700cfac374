import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule, provideAnimations } from '@angular/platform-browser/animations';
import { ExtraOptions, PreloadAllModules, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Bootstrap app
import { AppComponent } from './app.component';
import { APP_ROUTES } from './app-routes';

// Shared modules
import { ApiModule } from './modules/api/api.module';
import { AuthModule } from './modules/auth/auth.module';
import { LayoutModule } from './modules/layout/layout.module';
import { MaterialModule } from './modules/material/material.module';
import { TranslationModule } from './modules/translation/translation.module';
import { FuseModule } from './modules/fuse/fuse.module';

// Example app
import { PageNotFoundComponent } from './components/public/page-not-found/page-not-found.component';
import { HomeComponent } from './components/public/home/<USER>';
import { FeaturesComponent } from './components/public/features/features.component';
import { PricingComponent } from './components/public/pricing/pricing.component';
import { FaqComponent } from './components/public/faq/faq.component';
import { TosComponent } from './components/public/tos/tos.component';
import { ImprintComponent } from './components/public/imprint/imprint.component';
import { PrivacyPolicyComponent } from './components/public/privacy-policy/privacy-policy.component';

const routerConfig: ExtraOptions = {
    preloadingStrategy: PreloadAllModules,
    scrollPositionRestoration: 'enabled',
};

const COMPONENTS: any[] = [
    AppComponent,
    PageNotFoundComponent,
    HomeComponent,
    FeaturesComponent,
    PricingComponent,
    FaqComponent,
    TosComponent,
    ImprintComponent,
    PrivacyPolicyComponent,
];

@NgModule({
    declarations: [
        ...COMPONENTS,
    ],
    imports: [
        BrowserModule,
        BrowserAnimationsModule,
        RouterModule.forRoot(APP_ROUTES, routerConfig),
        CommonModule,
        FormsModule,
        ReactiveFormsModule,

        // Bootstrap app modules
        ApiModule,
        AuthModule,
        LayoutModule,
        MaterialModule,
        TranslationModule,
        FuseModule,
    ],
    providers: [
        provideAnimations(),
    ],
    bootstrap: [
        AppComponent,
    ]
})

export class AppModule { }
