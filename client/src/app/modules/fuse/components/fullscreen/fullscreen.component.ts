import { DOCUMENT } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, Input, TemplateRef, ViewEncapsulation } from '@angular/core';


@Component({
    selector: 'fuse-fullscreen',
    templateUrl: './fullscreen.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false,
  
})
export class FuseFullscreenComponent {

    constructor(
        @Inject(DOCUMENT) private document: Document,
    ) {
    }
   
    @Input() iconTpl: TemplateRef<any> | null = null;
    @Input() tooltip: string = '';

    toggleFullscreen(): void {
        if (!this.document.fullscreenEnabled) {
            console.log('Fullscreen is not available in this browser.');
            return;
        }

        const fullScreen = this.document.fullscreenElement;

        if (fullScreen) {
            this.document.exitFullscreen();
        } else {
            this.document.documentElement.requestFullscreen().catch(() => {
                console.error('Entering fullscreen mode failed.');
            });
        }
    }
}
