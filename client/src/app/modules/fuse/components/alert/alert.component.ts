import { BooleanInput, coerceBooleanProperty } from '@angular/cdk/coercion';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, HostBinding, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { Subject, filter, takeUntil } from 'rxjs';
import { fuseAnimations } from '../../animations/animations';
import { FuseAlertService } from './alert.service';
import { FuseAlertAppearance } from './types/alert.appearance';
import { FuseAlertType } from './types/alert.types';
import { FuseUtilsService } from '../../providers/utils.service';

@Component({
    selector: 'fuse-alert',
    templateUrl: './alert.component.html',
    styleUrls: ['./alert.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    standalone: false,
})
export class FuseAlertComponent implements OnChanges, OnInit, OnDestroy {

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        private fuseAlertService: FuseAlertService,
        private fuseUtilsService: FuseUtilsService,
    ) {
    }

    static ngAcceptInputType_dismissible: BooleanInput;
    static ngAcceptInputType_dismissed: BooleanInput;
    static ngAcceptInputType_showIcon: BooleanInput;

    @Input() appearance: FuseAlertAppearance = 'soft';
    @Input() dismissed: boolean = false;
    @Input() dismissible: boolean = false;
    @Input() name: string = this.fuseUtilsService.randomId();
    @Input() showIcon: boolean = true;
    @Input() type: FuseAlertType = 'primary';
    @Output() readonly dismissedChanged: EventEmitter<boolean> = new EventEmitter<boolean>();

    private unsubscribeAll: Subject<any> = new Subject<any>();

    @HostBinding('class') get classList(): any {
        return {
            'fuse-alert-appearance-border': this.appearance === 'border',
            'fuse-alert-appearance-fill': this.appearance === 'fill',
            'fuse-alert-appearance-outline': this.appearance === 'outline',
            'fuse-alert-appearance-soft': this.appearance === 'soft',
            'fuse-alert-dismissed': this.dismissed,
            'fuse-alert-dismissible': this.dismissible,
            'fuse-alert-show-icon': this.showIcon,
            'fuse-alert-type-primary': this.type === 'primary',
            'fuse-alert-type-accent': this.type === 'accent',
            'fuse-alert-type-warn': this.type === 'warn',
            'fuse-alert-type-basic': this.type === 'basic',
            'fuse-alert-type-info': this.type === 'info',
            'fuse-alert-type-success': this.type === 'success',
            'fuse-alert-type-warning': this.type === 'warning',
            'fuse-alert-type-error': this.type === 'error',
        };
    }

    ngOnChanges(changes: SimpleChanges): void {
        if ('dismissed' in changes) {
            this.dismissed = coerceBooleanProperty(changes['dismissed'].currentValue);
            this.toggleDismiss(this.dismissed);
        }

        if ('dismissible' in changes) {
            // Coerce the value to a boolean
            this.dismissible = coerceBooleanProperty(changes['dismissible'].currentValue);
        }

        if ('showIcon' in changes) {
            // Coerce the value to a boolean
            this.showIcon = coerceBooleanProperty(changes['showIcon'].currentValue);
        }
    }

    ngOnInit(): void {
        this.fuseAlertService.onDismiss
            .pipe(
                filter((name) => this.name === name),
                takeUntil(this.unsubscribeAll)
            )
            .subscribe(() => {
                this.dismiss();
            });

        this.fuseAlertService.onShow
            .pipe(
                filter((name) => this.name === name),
                takeUntil(this.unsubscribeAll)
            )
            .subscribe(() => {
                this.show();
            });
    }

    dismiss(): void {
        if (this.dismissed) {
            return;
        }

        this.toggleDismiss(true);
    }

    show(): void {
        if (!this.dismissed) {
            return;
        }

        this.toggleDismiss(false);
    }

    private toggleDismiss(dismissed: boolean): void {
        if (!this.dismissible) {
            return;
        }

        this.dismissed = dismissed;
        this.dismissedChanged.next(this.dismissed);

        this.changeDetectorRef.markForCheck();
    }

    ngOnDestroy(): void {
        this.unsubscribeAll.next(null);
        this.unsubscribeAll.complete();
    }
}