import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, EmbeddedViewRef, Input, OnChanges, SecurityContext, SimpleChanges, TemplateRef, ViewChild, ViewContainerRef, ViewEncapsulation } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { FuseHighlightService } from './highlight.service';

@Component({
    selector: 'textarea[fuse-highlight]',
    templateUrl: './highlight.component.html',
    styleUrls: ['./highlight.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false,
})
export class FuseHighlightComponent implements OnChanges, AfterViewInit {

    constructor(
        private domSanitizer: DomSanitizer,
        private elementRef: ElementRef,
        private fuseHighlightService: FuseHighlightService,
        private viewContainerRef: ViewContainerRef,
    ) {
    }
   
    @Input() code: string = '';
    @Input() lang: string = '';
    @ViewChild(TemplateRef) templateRef: TemplateRef<any> | null = null;

    highlightedCode: string | null = null;
    private viewRef: EmbeddedViewRef<any> | null = null;

    ngOnChanges(changes: SimpleChanges): void {
        if ('code' in changes || 'lang' in changes) {
            if (!this.viewContainerRef.length) {
                return;
            }

            this.highlightAndInsert();
        }
    }

    ngAfterViewInit(): void {
        if (!this.lang) {
            return;
        }

        if (!this.code) {
            this.code = this.elementRef.nativeElement.value;
        }

        this.highlightAndInsert();
    }

    private highlightAndInsert(): void {
        if (!this.templateRef) {
            return;
        }

        if (!this.code || !this.lang) {
            return;
        }

        if (this.viewRef) {
            this.viewRef.destroy();
            this.viewRef = null;
        }

        // Highlight and sanitize the code just in case
        this.highlightedCode = this.domSanitizer.sanitize(
            SecurityContext.HTML,
            this.fuseHighlightService.highlight(this.code, this.lang)
        );

        if (this.highlightedCode === null) {
            return;
        }

        this.viewRef = this.viewContainerRef.createEmbeddedView(
            this.templateRef,
            {
                highlightedCode: this.highlightedCode,
                lang: this.lang,
            }
        );

        this.viewRef.detectChanges();
    }
}
