import { Injectable } from '@angular/core';
import { FuseDrawerComponent } from './drawer.component';

@Injectable({ providedIn: 'root' })
export class FuseDrawerService {
    private componentRegistry: Map<string, FuseDrawerComponent> = new Map<string, FuseDrawerComponent>();

    registerComponent(name: string, component: FuseDrawerComponent): void {
        this.componentRegistry.set(name, component);
    }

    deregisterComponent(name: string): void {
        this.componentRegistry.delete(name);
    }

    getComponent(name: string): FuseDrawerComponent | undefined {
        return this.componentRegistry.get(name);
    }
}
