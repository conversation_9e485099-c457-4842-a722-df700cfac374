import { animate, AnimationBuilder, AnimationPlayer, style } from '@angular/animations';
import { BooleanInput, coerceBooleanProperty } from '@angular/cdk/coercion';
import { Component, ElementRef, EventEmitter, HostBinding, HostListener, Input, OnChanges, OnDestroy, OnInit, Output, Renderer2, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FuseUtilsService } from '../../providers/utils.service';
import { FuseDrawerService } from './drawer.service';
import { FuseDrawerMode } from './types/drawer.mode';
import { FuseDrawerPosition } from './types/drawer.position';


@Component({
    selector: 'fuse-drawer',
    templateUrl: './drawer.component.html',
    styleUrls: ['./drawer.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false,
})
export class FuseDrawerComponent implements OnChanges, OnInit, On<PERSON><PERSON><PERSON> {

    constructor(
        private animationBuilder: AnimationBuilder,
        private elementRef: ElementRef,
        private renderer2: Renderer2,
        private fuseDrawerService: FuseDrawerService,
        private fuseUtilsService: FuseUtilsService,
    ) {
    }
   
    static ngAcceptInputType_fixed: BooleanInput;
    static ngAcceptInputType_opened: BooleanInput;
    static ngAcceptInputType_transparentOverlay: BooleanInput;

    @Input() fixed: boolean = false;
    @Input() mode: FuseDrawerMode = 'side';
    @Input() name: string = this.fuseUtilsService.randomId();
    @Input() opened: boolean = false;
    @Input() position: FuseDrawerPosition = 'left';
    @Input() transparentOverlay: boolean = false;
    @Output() readonly fixedChanged: EventEmitter<boolean> =
        new EventEmitter<boolean>();
    @Output() readonly modeChanged: EventEmitter<FuseDrawerMode> =
        new EventEmitter<FuseDrawerMode>();
    @Output() readonly openedChanged: EventEmitter<boolean> =
        new EventEmitter<boolean>();
    @Output() readonly positionChanged: EventEmitter<FuseDrawerPosition> =
        new EventEmitter<FuseDrawerPosition>();

    private animationsEnabled: boolean = false;
    private readonly handleOverlayClick = (): void => this.close();
    private hovered: boolean = false;
    private overlay: HTMLElement | null = null;
    private player: AnimationPlayer | null = null;

    @HostBinding('class') get classList(): any {
        return {
            'fuse-drawer-animations-enabled': this.animationsEnabled,
            'fuse-drawer-fixed': this.fixed,
            'fuse-drawer-hover': this.hovered,
            [`fuse-drawer-mode-${this.mode}`]: true,
            'fuse-drawer-opened': this.opened,
            [`fuse-drawer-position-${this.position}`]: true,
        };
    }

    @HostBinding('style') get styleList(): any {
        return {
            visibility: this.opened ? 'visible' : 'hidden',
        };
    }

    @HostListener('mouseenter')
    private onMouseenter(): void {
        this.enableAnimations();
        this.hovered = true;
    }

    @HostListener('mouseleave')
    private onMouseleave(): void {
        this.enableAnimations();
        this.hovered = false;
    }

    ngOnInit(): void {
        this.fuseDrawerService.registerComponent(this.name, this);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if ('fixed' in changes) {
            // Coerce the value to a boolean
            this.fixed = coerceBooleanProperty(changes['fixed'].currentValue);

            this.fixedChanged.next(this.fixed);
        }

        if ('mode' in changes) {
            // Get the previous and current values
            const previousMode = changes['mode'].previousValue;
            const currentMode = changes['mode'].currentValue;

            this.disableAnimations();

            if (previousMode === 'over' && currentMode === 'side') {
                this.hideOverlay();
            }

            if (previousMode === 'side' && currentMode === 'over') {
                if (this.opened) {
                    this.showOverlay();
                }
            }

            this.modeChanged.next(currentMode);

            // Enable the animations after a delay
            // The delay must be bigger than the current transition-duration
            // to make sure nothing will be animated while the mode is changing
            setTimeout(() => {
                this.enableAnimations();
            }, 500);
        }

        if ('opened' in changes) {
            // Coerce the value to a boolean
            const open = coerceBooleanProperty(changes['opened'].currentValue);

            this.toggleOpened(open);
        }

        if ('position' in changes) {
            // Execute the observable
            this.positionChanged.next(this.position);
        }

        if ('transparentOverlay' in changes) {
            // Coerce the value to a boolean
            this.transparentOverlay = coerceBooleanProperty(
                changes['transparentOverlay'].currentValue
            );
        }
    }

    open(): void {
        if (this.opened) {
            return;
        }

        this.toggleOpened(true);
    }

    close(): void {
        if (!this.opened) {
            return;
        }

        this.toggleOpened(false);
    }

    toggle(): void {
        if (this.opened) {
            this.close();
        } else {
            this.open();
        }
    }

    private enableAnimations(): void {
        if (this.animationsEnabled) {
            return;
        }

        this.animationsEnabled = true;
    }

    private disableAnimations(): void {
        if (!this.animationsEnabled) {
            return;
        }

        this.animationsEnabled = false;
    }

    private showOverlay(): void {
        this.overlay = this.renderer2.createElement('div');
        this.overlay?.classList.add('fuse-drawer-overlay');

        if (this.fixed) {
            this.overlay?.classList.add('fuse-drawer-overlay-fixed');
        }

        if (this.transparentOverlay) {
            this.overlay?.classList.add('fuse-drawer-overlay-transparent');
        }

        this.renderer2.appendChild(
            this.elementRef.nativeElement.parentElement,
            this.overlay
        );

        // Create enter animation and attach it to the player
        this.player = this.animationBuilder
            .build([
                style({ opacity: 0 }),
                animate(
                    '300ms cubic-bezier(0.25, 0.8, 0.25, 1)',
                    style({ opacity: 1 })
                ),
            ])
            .create(this.overlay);

        this.player.play();
        this.overlay?.addEventListener('click', this.handleOverlayClick);
    }

    private hideOverlay(): void {
        if (!this.overlay) {
            return;
        }

        // Create the leave animation and attach it to the player
        this.player = this.animationBuilder
            .build([
                animate(
                    '300ms cubic-bezier(0.25, 0.8, 0.25, 1)',
                    style({ opacity: 0 })
                ),
            ])
            .create(this.overlay);

        this.player.play();

        this.player.onDone(() => {
            if (this.overlay) {
                this.overlay.removeEventListener(
                    'click',
                    this.handleOverlayClick
                );

                this.overlay.parentNode?.removeChild(this.overlay);
                this.overlay = null;
            }
        });
    }

    private toggleOpened(open: boolean): void {
        this.opened = open;
        this.enableAnimations();

        if (this.mode === 'over') {
            if (open) {
                this.showOverlay();
            }
            else {
                this.hideOverlay();
            }
        }

        this.openedChanged.next(open);
    }

    ngOnDestroy(): void {
        if (this.player) {
            this.player.finish();
        }

        this.fuseDrawerService.deregisterComponent(this.name);
    }
}
