import { coerceBooleanProperty } from '@angular/cdk/coercion';

import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { FuseLoadingService } from '../../providers/loading/loading.service';

@Component({
    selector: 'fuse-loading-bar',
    templateUrl: './loading-bar.component.html',
    styleUrls: ['./loading-bar.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false,
})
export class FuseLoadingBarComponent implements OnChanges, OnInit, OnDestroy {

    constructor(
        private fuseLoadingService: FuseLoadingService,
    ) {
    }
    
    @Input() autoMode: boolean = true;

    mode: 'determinate' | 'indeterminate' = 'indeterminate';
    progress: number | null = null;
    show: boolean = false;

    private unsubscribeAll: Subject<any> = new Subject<any>();

    ngOnChanges(changes: SimpleChanges): void {
        if ('autoMode' in changes) {
            // Set the auto mode in the service
            this.fuseLoadingService.setAutoMode(
                coerceBooleanProperty(changes['autoMode'].currentValue)
            );
        }
    }

    ngOnInit(): void {
        this.fuseLoadingService.mode$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe((value) => {
                this.mode = value;
            });

        this.fuseLoadingService.progress$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe((value) => {
                this.progress = value;
            });

        this.fuseLoadingService.show$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe((value) => {
                this.show = value;
            });
    }

    ngOnDestroy(): void {
        this.unsubscribeAll.next(null);
        this.unsubscribeAll.complete();
    }
}
