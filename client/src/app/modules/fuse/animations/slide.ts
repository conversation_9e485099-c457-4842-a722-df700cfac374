import { animate, state, style, transition, trigger } from '@angular/animations';
import { FuseAnimationDurations, FuseAnimationCurves } from './defaults';

const slideInTop = trigger('slideInTop', [
    state(
        'void',
        style({
            transform: 'translate3d(0, -100%, 0)',
        })
    ),

    state(
        '*',
        style({
            transform: 'translate3d(0, 0, 0)',
        })
    ),

    // Prevent the transition if the state is false
    transition('void => false', []),

    // Transition
    transition('void => *', animate('{{timings}}'), {
        params: {
            timings: `${ FuseAnimationDurations.entering } ${ FuseAnimationCurves.deceleration }`,
        },
    }),
]);

const slideInBottom = trigger('slideInBottom', [
    state(
        'void',
        style({
            transform: 'translate3d(0, 100%, 0)',
        })
    ),

    state(
        '*',
        style({
            transform: 'translate3d(0, 0, 0)',
        })
    ),

    // Prevent the transition if the state is false
    transition('void => false', []),

    // Transition
    transition('void => *', animate('{{timings}}'), {
        params: {
            timings: `${FuseAnimationDurations.entering} ${FuseAnimationCurves.deceleration}`,
        },
    }),
]);

const slideInLeft = trigger('slideInLeft', [
    state(
        'void',
        style({
            transform: 'translate3d(-100%, 0, 0)',
        })
    ),

    state(
        '*',
        style({
            transform: 'translate3d(0, 0, 0)',
        })
    ),

    // Prevent the transition if the state is false
    transition('void => false', []),

    // Transition
    transition('void => *', animate('{{timings}}'), {
        params: {
            timings: `${FuseAnimationDurations.entering} ${FuseAnimationCurves.deceleration}`,
        },
    }),
]);

const slideInRight = trigger('slideInRight', [
    state(
        'void',
        style({
            transform: 'translate3d(100%, 0, 0)',
        })
    ),

    state(
        '*',
        style({
            transform: 'translate3d(0, 0, 0)',
        })
    ),

    // Prevent the transition if the state is false
    transition('void => false', []),

    // Transition
    transition('void => *', animate('{{timings}}'), {
        params: {
            timings: `${FuseAnimationDurations.entering} ${FuseAnimationCurves.deceleration}`,
        },
    }),
]);

const slideOutTop = trigger('slideOutTop', [
    state(
        '*',
        style({
            transform: 'translate3d(0, 0, 0)',
        })
    ),

    state(
        'void',
        style({
            transform: 'translate3d(0, -100%, 0)',
        })
    ),

    // Prevent the transition if the state is false
    transition('false => void', []),

    // Transition
    transition('* => void', animate('{{timings}}'), {
        params: {
            timings: `${FuseAnimationDurations.exiting} ${FuseAnimationCurves.acceleration}`,
        },
    }),
]);

const slideOutBottom = trigger('slideOutBottom', [
    state(
        '*',
        style({
            transform: 'translate3d(0, 0, 0)',
        })
    ),

    state(
        'void',
        style({
            transform: 'translate3d(0, 100%, 0)',
        })
    ),

    // Prevent the transition if the state is false
    transition('false => void', []),

    // Transition
    transition('* => void', animate('{{timings}}'), {
        params: {
            timings: `${FuseAnimationDurations.exiting} ${FuseAnimationCurves.acceleration}`,
        },
    }),
]);

const slideOutLeft = trigger('slideOutLeft', [
    state(
        '*',
        style({
            transform: 'translate3d(0, 0, 0)',
        })
    ),

    state(
        'void',
        style({
            transform: 'translate3d(-100%, 0, 0)',
        })
    ),

    // Prevent the transition if the state is false
    transition('false => void', []),

    // Transition
    transition('* => void', animate('{{timings}}'), {
        params: {
            timings: `${FuseAnimationDurations.exiting} ${FuseAnimationCurves.acceleration}`,
        },
    }),
]);

const slideOutRight = trigger('slideOutRight', [
    state(
        '*',
        style({
            transform: 'translate3d(0, 0, 0)',
        })
    ),

    state(
        'void',
        style({
            transform: 'translate3d(100%, 0, 0)',
        })
    ),

    // Prevent the transition if the state is false
    transition('false => void', []),

    // Transition
    transition('* => void', animate('{{timings}}'), {
        params: {
            timings: `${FuseAnimationDurations.exiting} ${FuseAnimationCurves.acceleration}`,
        },
    }),
]);

export {
    slideInBottom,
    slideInLeft,
    slideInRight,
    slideInTop,
    slideOutBottom,
    slideOutLeft,
    slideOutRight,
    slideOutTop,
};
