import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MaterialModule } from '../../../material/material.module';

// Fuse-Navigation Components
import { FuseHorizontalNavigationBasicItemComponent } from './horizontal/components/basic/basic.component';
import { FuseHorizontalNavigationBranchItemComponent } from './horizontal/components/branch/branch.component';
import { FuseHorizontalNavigationDividerItemComponent } from './horizontal/components/divider/divider.component';
import { FuseHorizontalNavigationSpacerItemComponent } from './horizontal/components/spacer/spacer.component';
import { FuseVerticalNavigationAsideItemComponent } from './vertical/components/aside/aside.component';
import { FuseVerticalNavigationBasicItemComponent } from './vertical/components/basic/basic.component';
import { FuseVerticalNavigationCollapsableItemComponent } from './vertical/components/collapsable/collapsable.component';
import { FuseVerticalNavigationDividerItemComponent } from './vertical/components/divider/divider.component';
import { FuseVerticalNavigationGroupItemComponent } from './vertical/components/group/group.component';
import { FuseVerticalNavigationSpacerItemComponent } from './vertical/components/spacer/spacer.component';
import { FuseScrollbarDirective } from './directives/scrollbar/scrollbar.directive';
import { FuseScrollResetDirective } from './directives/scroll-reset.directive';
import { FuseNavigationService } from './navigation.service';
import { FuseHorizontalNavigationComponent } from './horizontal/horizontal.component';
import { FuseVerticalNavigationComponent } from './vertical/vertical.component';

const INNER_COMPONENTS: any[] = [
    FuseHorizontalNavigationBasicItemComponent,
    FuseHorizontalNavigationBranchItemComponent,
    FuseHorizontalNavigationDividerItemComponent,
    FuseHorizontalNavigationSpacerItemComponent,
    FuseVerticalNavigationAsideItemComponent,
    FuseVerticalNavigationBasicItemComponent,
    FuseVerticalNavigationCollapsableItemComponent,
    FuseVerticalNavigationDividerItemComponent,
    FuseVerticalNavigationGroupItemComponent,
    FuseVerticalNavigationSpacerItemComponent,
];

const EXPORTED_COMPONENTS: any[] = [
    FuseHorizontalNavigationComponent,
    FuseVerticalNavigationComponent,
];

const DIRECTIVES: any[] = [
    FuseScrollbarDirective,
    FuseScrollResetDirective,
];

@NgModule({
    declarations: [
        ...INNER_COMPONENTS,
        ...EXPORTED_COMPONENTS,
        ...DIRECTIVES,
        
    ],
    providers: [
        FuseNavigationService,
    ],
    imports: [
        CommonModule,
        RouterModule,
        MaterialModule,
    ],
    exports: [
        ...EXPORTED_COMPONENTS,
        ...DIRECTIVES,
    ]
})
export class FuseNavigationModule {}