import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter, take } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class FuseSplashScreenService {
    constructor(
        @Inject(DOCUMENT) private document: Document,
        private router: Router,
    ) {
        // Hide it on the first NavigationEnd event
        this.router.events
            .pipe(
                filter((event) => event instanceof NavigationEnd),
                take(1)
            )
            .subscribe(() => {
                this.hide();
            });
    }

    /**
     * Show the splash screen
     */
    show(): void {
        this.document.body.classList.remove('fuse-splash-screen-hidden');
    }

    /**
     * Hide the splash screen
     */
    hide(): void {
        this.document.body.classList.add('fuse-splash-screen-hidden');
    }
}

