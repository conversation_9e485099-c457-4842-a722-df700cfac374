import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Route, RouterModule } from '@angular/router';
import { MaterialModule } from '../material/material.module';
import { TranslationModule } from '../translation/translation.module';
import { ExampleComponent } from './components/example/example.component';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { XComponentsModule } from '../x-components/x-components.module';

const COMPONENTS: any[] = [
    ExampleComponent,
];

const PROVIDERS: any[] = [];

const PIPES: any[] = [];

export const EXAMPLE_ROUTES: Route[] = [
    {
        path: 'home',
        component: LayoutComponent,
        data: { layout: 'classy' },
        // canActivate: [AuthGuard], // TODO adjust as needed
    },
    {
        path: 'example',
        component: LayoutComponent,
        data: { layout: 'classy' },
        // canActivate: [AuthGuard], // TODO adjust as needed
        children: [{ path: '', component: ExampleComponent }]
    },
];

@NgModule({
    declarations: [
        ...COMPONENTS,
        ...PIPES,
    ],
    imports: [
        RouterModule.forChild(EXAMPLE_ROUTES),
        CommonModule,
        MaterialModule,
        XComponentsModule,
        TranslationModule,
    ],
    providers: [
        ...PROVIDERS,

    ],
    exports: [
        ...COMPONENTS,
        ...PIPES,
    ],
})
export class ExampleModule {}
