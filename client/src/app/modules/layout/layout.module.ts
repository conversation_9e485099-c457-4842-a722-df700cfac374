import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

// Layout Components
import { LayoutComponent } from './components/layout/layout.component';
import { EmptyLayoutComponent } from './components/layouts/empty/empty.component';
import { CenteredLayoutComponent } from './components/layouts/horizontal/centered/centered.component';
import { EnterpriseLayoutComponent } from './components/layouts/horizontal/enterprise/enterprise.component';
import { MaterialLayoutComponent } from './components/layouts/horizontal/material/material.component';
import { ModernLayoutComponent } from './components/layouts/horizontal/modern/modern.component';
import { ClassicLayoutComponent } from './components/layouts/vertical/classic/classic.component';
import { ClassyLayoutComponent } from './components/layouts/vertical/classy/classy.component';
import { CompactLayoutComponent } from './components/layouts/vertical/compact/compact.component';
import { DenseLayoutComponent } from './components/layouts/vertical/dense/dense.component';
import { FuturisticLayoutComponent } from './components/layouts/vertical/futuristic/futuristic.component';
import { ThinLayoutComponent } from './components/layouts/vertical/thin/thin.component';
import { SettingsComponent } from './components/settings/settings.component';

// Providers
import { AppNavigationService } from './providers/app-navigation.service';

// Imported modules
import { FuseModule } from '../fuse/fuse.module';
import { MaterialModule } from '../material/material.module';
import { WidgetModule } from '../widget/widget.module';
import { AuthModule } from '../auth/auth.module';
import { ImageCropperDialogComponent } from './providers/image-cropper/dialog/image-cropper.dialog.component';
import { ImageCropperService } from './providers/image-cropper/image-cropper.service';
import { ImageCropperModule } from 'ngx-image-cropper';
import { UpdateUserRequestService } from './providers/update-user-request.service';

const COMPONENTS: any[] = [
    LayoutComponent,
    EmptyLayoutComponent,
    CenteredLayoutComponent,
    EnterpriseLayoutComponent,
    MaterialLayoutComponent,
    ModernLayoutComponent,
    ClassicLayoutComponent,
    ClassyLayoutComponent,
    CompactLayoutComponent,
    DenseLayoutComponent,
    FuturisticLayoutComponent,
    ThinLayoutComponent,
    SettingsComponent,
    ImageCropperDialogComponent,
];

@NgModule({
    declarations: [
        ...COMPONENTS,
    ],
    imports: [
        RouterModule,
        CommonModule,
        FuseModule,
        AuthModule,
        MaterialModule,
        WidgetModule,
        ImageCropperModule,
    ],
    providers: [
        AppNavigationService,
        ImageCropperService,
        UpdateUserRequestService,
    ],
    exports: [
        ...COMPONENTS,
    ],
})
export class LayoutModule {}