import { Component, Inject, OnInit, ViewEncapsulation } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ImageCroppedEvent, LoadedImage } from 'ngx-image-cropper';
import { ImageCropperConfig } from '../image-crooper.types';


@Component({
    selector: 'imagecropper-dialog',
    templateUrl: './image-cropper.dialog.component.html',
    styles: [
        /* language=SCSS */
        `
            .imagecropper-dialog-panel {
                justify-content: center !important;
                @screen md {
                    @apply w-128;
                }

                .mat-dialog-container {
                    padding: 0 !important;
                }
            }
        `
    ],
    standalone: false,
    encapsulation: ViewEncapsulation.None
})
export class ImageCropperDialogComponent implements OnInit {
    imageSelectedEvent: any = '';
    cancelled: boolean = false;
    croppedImage: string = '';

    constructor(
        @Inject(MAT_DIALOG_DATA) public data: ImageCropperConfig & { fileInputEvent: any },
        public matDialogRef: MatDialogRef<ImageCropperDialogComponent>
    ) {
        this.imageSelectedEvent = data.fileInputEvent;
    }

    ngOnInit(): void {
    }

    imageCropped(event: ImageCroppedEvent): void {
        this.croppedImage = event.base64 || ''; // Fallback to empty string if base64 is null or undefined
    }

    imageLoaded(): void {
    }

    imageCropperReady(): void {
    }

    loadImageFailed(): void {
        this.matDialogRef.close('undefined');
    }
}
