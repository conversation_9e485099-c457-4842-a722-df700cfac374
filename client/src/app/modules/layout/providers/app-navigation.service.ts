import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Navigation } from '../../fuse/modules/navigation/types/navigation';

@Injectable({ providedIn: 'root' })
export class AppNavigationService {
    private navigation: Navigation = {
        default: [
            {
                id: 'home',
                title: 'Home',
                type: 'basic',
                link: '/'
            },
            {
                id: 'questionnaire',
                title: 'Parfum Finder',
                type: 'basic',
                link: '/questionnaire'
            },
            {
                id: 'faq',
                title: 'FAQ',
                type: 'basic',
                link: '/faq'
            }
        ],
        compact: [],
        futuristic: [],
        horizontal: [],
    };

    get navigation$(): Observable<Navigation> {
        return of(this.navigation);
    }

    get(): Observable<Navigation> {
        return of(this.navigation);
    }
}