import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Navigation } from '../../fuse/modules/navigation/types/navigation';
import { defaultNavigation, compactNavigation, futuristicNavigation, horizontalNavigation } from '../../task-fuse/task-fuse.navigation';

@Injectable({ providedIn: 'root' })
export class AppNavigationService {
    private navigation: Navigation = {
        default: defaultNavigation,
        compact: compactNavigation,
        futuristic: futuristicNavigation,
        horizontal: horizontalNavigation,
    };

    get navigation$(): Observable<Navigation> {
        return of(this.navigation);
    }

    get(): Observable<Navigation> {
        return of(this.navigation);
    }
}