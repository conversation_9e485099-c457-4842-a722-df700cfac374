import { gql } from 'apollo-angular';
import { DocumentNode } from 'graphql';

import { REQUESTRESULT_FRAGMENT, DEEP_USER_FRAGMENT, DEEP_FILE_FRAGMENT, SEARCHRESULT_FRAGMENT_STRING, DEEP_TASK_FRAGMENT, DEEP_CUSTOMER_FRAGMENT, DEEP_ADDRESS_FRAGMENT, DEEP_PROJECT_FRAGMENT, DEEP_PARTNER_FRAGMENT, DEEP_OFFER_FRAGMENT, DEEP_PRODUCTPACKAGE_FRAGMENT, DEEP_PRODUCTITEM_FRAGMENT, DEEP_PRODUCT_FRAGMENT } from "./fragments.graphql";
import { UserInput, UserRelation, getFragmentsForUserRelations, PARTIAL_USER_FRAGMENT } from "../entities/user";
import { DEEP_FILE_FRAGMENT_STRING, FileInput, FileRelation, getFragmentsForFileRelations, PARTIAL_FILE_FRAGMENT } from "../entities/file";
import { EntityType } from '../enums/entitytype';
import { UserPermissions } from '../types/user-permissions';
import { CustomerUserInput } from '../types/customer-user-input';
import { CustomerInput, CustomerRelation, getFragmentsForCustomerRelations, PARTIAL_CUSTOMER_FRAGMENT } from '../entities/customer';
import { DEEP_PARTNER_FRAGMENT_STRING, PartnerInput } from '../entities/partner';
import { PartnerUserInput } from '../types/partner-user.input';
import { TaskChecklist } from '../types/taskchecklist';
import { getFragmentsForTaskRelations, PARTIAL_TASK_FRAGMENT, TaskInput, TaskRelation } from '../entities/task';
import { Util } from 'src/app/util/util';
import { AddressInput, AddressRelation, getFragmentsForAddressRelations, PARTIAL_ADDRESS_FRAGMENT } from '../entities/address';
import { getFragmentsForProjectRelations, PARTIAL_PROJECT_FRAGMENT, ProjectInput, ProjectRelation } from '../entities/project';
import { InvoiceData, InvoiceDataItem } from '../types/invoicedata';
import { DEFAULT_INVOICE_ITEM_DATA, DEFAULT_TASKCHECKLIST } from '../api-service.constants';
import { getFragmentsForOfferRelations, OfferInput, OfferRelation, PARTIAL_OFFER_FRAGMENT } from '../entities/offer';
import { DEEP_CUSTOMIZATION_FRAGMENT_STRING } from '../entities/customization';
import { getFragmentsForProductRelations, PARTIAL_PRODUCT_FRAGMENT, ProductInput, ProductRelation } from '../entities/product';
import { getFragmentsForProductItemRelations, PARTIAL_PRODUCTITEM_FRAGMENT, ProductItemInput, ProductItemRelation } from '../entities/productitem';
import { getFragmentsForProductPackageRelations, PARTIAL_PRODUCTPACKAGE_FRAGMENT, ProductPackageInput, ProductPackageRelation } from '../entities/productpackage';



const escape = (input: string): string => input ? input.replace(new RegExp('"', 'g'), '&quot;').replace(new RegExp('\r\n', 'g'), '&linebreak;').replace(new RegExp('\n', 'g'), '&linebreak;').trim() : '';

export class Documents {

    static stringifyTaskChecklist(checklist: TaskChecklist): string {
        let dataString: string = JSON.stringify(JSON.stringify(checklist) !== '{}' && JSON.stringify(checklist) !== '' ? checklist : DEFAULT_TASKCHECKLIST).replace(new RegExp('"', 'g'), '~');
        return dataString;
    }

    static stringifyInvoiceItemData(data: InvoiceData): string {
        data.items = data.items.map((item: InvoiceDataItem) => {
            return {
                ...item,
                description: typeof item.description === 'string'
                    ? item.description
                        .replace(new RegExp('"', 'g'), '&quot;')
                        .replace(/\n/gi, '&linebreak;')
                        .replace(/<br\s*\/?>/gi, '&linebreak;')
                        .replace(/<\/p>/gi, '&linebreak;&linebreak;')
                        .replace(/<p>/gi, '')
                        .replace(/(&linebreak;)+$/g, '')
                        .replace(new RegExp('\t', 'g'), ' ')
                    : '' 
            };
        });
    
        let dataString: string = JSON.stringify(
            JSON.stringify(data) !== '{}' && JSON.stringify(data) !== '' ? data : DEFAULT_INVOICE_ITEM_DATA
        );
    
        dataString = dataString.replace(new RegExp('"', 'g'), '~');
    
        return dataString;
    }

    static parseTaskChecklist(checklistString: string): TaskChecklist {
        checklistString = checklistString.replace(new RegExp('~', 'g'), '"');

        if (checklistString[0] && checklistString[0] === "\"") {
            checklistString = checklistString.substring(1, checklistString.length);
        }
        if (checklistString[checklistString.length - 1] === "\"") {
            checklistString = checklistString.substring(0, checklistString.length - 1);
        }

        let taskChecklist: TaskChecklist = checklistString + '' === '' ? {} : JSON.parse(checklistString);
        if ((checklistString + '' as string).trim() === '{}' || (checklistString + '' as string).trim() === '') {
            taskChecklist = DEFAULT_TASKCHECKLIST; 
        }

        return taskChecklist;
    }

    static parseInvoiceItemData(dataString: string): InvoiceData {
        if (dataString === '') {
            return DEFAULT_INVOICE_ITEM_DATA;
        }

        dataString = dataString.replace(new RegExp('~', 'g'), '\"');
        dataString = dataString.replace(new RegExp('\r\n', 'g'), '');
        dataString = dataString.replace(new RegExp('\n', 'g'), '');
        dataString = dataString.replace(new RegExp('	', 'g'), ' ');

        if (dataString[0] && dataString[0] === "\"") {
            dataString = dataString.substring(1, dataString.length);
        }
        if (dataString[dataString.length - 1] === "\"") {
            dataString = dataString.substring(0, dataString.length - 1);
        }

        let invoiceItemData: InvoiceData = dataString + '' === '' ? {} : JSON.parse(dataString);
        if ((dataString + '' as string).trim() === '{}' || (dataString + '' as string).trim() === '') {
            invoiceItemData = DEFAULT_INVOICE_ITEM_DATA;
        }

        invoiceItemData.items = invoiceItemData.items.map((item: InvoiceDataItem) => {
            return {
                ...item,
                description: item.description.replace(new RegExp('&linebreak;', 'g'), '<br />').replace(new RegExp('&quot;', 'g'), '"'),
            };
        });

        return invoiceItemData;
    }

    

    // Queries --------------------------------------------------------------------------------------------------

    static search(text: string, take: number, types: EntityType[]): DocumentNode {
        const query: string = `
            query search {
                result: search(
                    text: "${ escape(text) }",
                    take: ${ take },
                    types: ${ types.length < 1 ? '[]' : '[' + types.join(', ') + ']' },
                ) {
                    ...SearchResultFragment
                }
            }
            ${ SEARCHRESULT_FRAGMENT_STRING }
            ${ DEEP_FILE_FRAGMENT_STRING }
        `;
        return gql`${ query }`;
    }

    static whoAmI(): DocumentNode {
        return gql`
            query whoAmI {
                result: whoAmI
                {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static logIn(email: string, password: string): DocumentNode {
        return gql`
            query logIn {
                result: logIn(
                    email: "${ escape(email) }",
                    password: "${ escape(password) }",
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static logOut(): DocumentNode {
        return gql`
            query logOut {
                result: logOut
                {
                    ...RequestResultFragment
                }
            }
            ${ REQUESTRESULT_FRAGMENT }
        `;
    }

    static requestPasswordRecovery(email: string): DocumentNode {
        return gql`
            query requestPasswordRecovery {
                result: requestPasswordRecovery(
                    email: "${ escape(email) }",
                ) 
            }
        `;
    }

    static signUp(user: UserInput, recaptchaToken: string): DocumentNode {
        const settingsString: string = JSON.stringify(user.settings).replace(new RegExp('\"', 'g'), '~');
        return gql`
            query signUp {
                result: signUp(
                    user: {
                        firstName: "${ escape(user.firstName) }",
                        lastName: "${ escape(user.lastName) }",
                        email: "${ escape(user.email) }",
                        password: "${ escape(user.password) }",
                        languageCode: ${ user.languageCode },
                        avatarPath: "${ escape(user.avatarPath) }",
                        phoneNumber: "${ escape(user.phoneNumber) }",
                        settings: "${ escape(settingsString) }",
                        isPartner: ${ user.isPartner },
                    },
                    recaptchaToken: "${escape(recaptchaToken)}"
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static signUpCustomer(userInput: CustomerUserInput, recaptchaToken: string): DocumentNode {
        return gql`
            query signUpCustomer {
                result: signUpCustomer(
                    user: {
                        title: "${escape(userInput.title)}",
                        firstName: "${escape(userInput.firstName)}",
                        lastName: "${escape(userInput.lastName)}",
                        street: "${escape(userInput.street)}",
                        houseNumber: "${escape(userInput.houseNumber)}",
                        additionalAddress: "${escape(userInput.additionalAddress || '')}",
                        zipCode: "${escape(userInput.zipCode)}",
                        city: "${escape(userInput.city)}",
                        phoneNumber: "${escape(userInput.phoneNumber)}",
                        email: "${escape(userInput.email)}",
                        password: "${escape(userInput.password)}",
                        company: "${escape(userInput.company || '')}",
                        companyId: "${escape(userInput.companyId || '')}",
                        taxId: "${escape(userInput.taxId || '')}",
                        languageCode: ${userInput.languageCode},
                    },
                    recaptchaToken: "${escape(recaptchaToken)}"
                ) {
                    ...DeepUserFragment
                }
            }
            ${DEEP_USER_FRAGMENT}
        `;
    }

    static signUpExistingCustomer(customerNumber: string, email: string, recaptchaToken: string): DocumentNode {
        return gql`
            query signUpExistingCustomer {
                result: signUpExistingCustomer(
                    customerNumber: "${ escape(customerNumber) }",
                    email: "${ escape(email) }",
                    recaptchaToken: "${escape(recaptchaToken)}"
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static activateUser(activationCode: string): DocumentNode {
        return gql`
            query activateUser {
                result: activateUser(
                    activationCode: "${ escape(activationCode) }",
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static deactivateUser(activationCode: string): DocumentNode {
        return gql`
            query deactivateUser {
                result: deactivateUser(
                    activationCode: "${ escape(activationCode) }",
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static changePassword(newPassword: string, passwordRecoveryCode: string): DocumentNode {
        return gql`
            query changePassword {
                result: changePassword(
                    newPassword: "${ escape(newPassword) }",
                    passwordRecoveryCode: "${ escape(passwordRecoveryCode) }",
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static getAllAddresses(skip: number, take: number, sortBy: 'name' | 'modification', relations: AddressRelation[] = []): DocumentNode {
        const query: string = `
            query getAllAddresses {
                result: getAllAddresses(
                    skip: ${ skip },
                    take: ${ take },
                    sortBy: "${ escape(sortBy) }"
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialAddressFragment
                }
            }
            ${ PARTIAL_ADDRESS_FRAGMENT(relations) }
            ${ getFragmentsForAddressRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getAddressById(id: number, relations: AddressRelation[] = []): DocumentNode {
        const query: string = `
            query getAddressById {
                result: getAddressById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialAddressFragment
                }
            }
            ${ PARTIAL_ADDRESS_FRAGMENT(relations) }
            ${ getFragmentsForAddressRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getAllCustomers(skip: number, take: number, sortBy: 'name' | 'modification', pinned: number[], relations: CustomerRelation[] = [], loadSyntheticProperties: boolean = false, filterCustomersByUserId: number | null, filterCustomersByIsUser: boolean | null): DocumentNode {
        const query: string = `
            query getAllCustomers {
                result: getAllCustomers(
                    skip: ${ skip },
                    take: ${ take },
                    sortBy: "${ escape(sortBy) }"
                    filterCustomersByUserId: ${ filterCustomersByUserId },
                    filterCustomersByIsUser: ${ filterCustomersByIsUser },
                    pinned: [${ pinned.toString() }],
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                    loadSyntheticProperties: ${ loadSyntheticProperties },
                ) {
                    ...PartialCustomerFragment
                }
            }
            ${ PARTIAL_CUSTOMER_FRAGMENT(relations) }
            ${ getFragmentsForCustomerRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getCustomerById(id: number, relations: CustomerRelation[] = []): DocumentNode {
        const query: string = `
            query getCustomerById {
                result: getCustomerById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialCustomerFragment
                }
            }
            ${ PARTIAL_CUSTOMER_FRAGMENT(relations) }
            ${ getFragmentsForCustomerRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getAllCustomizations(skip: number, take: number): DocumentNode {
        const query: string = `
            query getAllCustomizations {
                result: getAllCustomizations(
                    skip: ${ skip },
                    take: ${ take },
                ) {
                    ...DeepCustomizationFragment
                }
            }
            ${ DEEP_CUSTOMIZATION_FRAGMENT_STRING }
        `;
        return gql`${ query }`;
    }

    static getCustomizationById(id: number): DocumentNode {
        const query: string = `
            query getCustomizationById {
                result: getCustomizationById(
                    id: ${ id },
                ) {
                    ...DeepCustomizationFragment
                }
            }
            ${ DEEP_CUSTOMIZATION_FRAGMENT_STRING }
        `;

        return gql`${ query }`;
    }

    static getAllFiles(skip: number, take: number, relations: FileRelation[] = [], loadSyntheticProperties: boolean = false): DocumentNode {
        const query: string = `
            query getAllFiles {
                result: getAllFiles(
                    skip: ${ skip },
                    take: ${ take },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                    loadSyntheticProperties: ${ loadSyntheticProperties },
                ) {
                    ...PartialFileFragment
                }
            }
            ${ PARTIAL_FILE_FRAGMENT(relations) }
            ${ getFragmentsForFileRelations(relations) }
        `;
        return gql`${ query }`;
    }
    
    static getFileById(id: number, relations: FileRelation[] = []): DocumentNode {
        const query: string = `
            query getFileById {
                result: getFileById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialFileFragment
                }
            }
            ${ PARTIAL_FILE_FRAGMENT(relations) }
            ${ getFragmentsForFileRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getAllOffers(skip: number, take: number, sortBy: 'offerNumber' | 'modification' | 'offerDate' | 'offerTerm' | 'acceptedDate' | 'grossAmount',  pinned: number[],  relations: OfferRelation[] = [],  loadSyntheticProperties: boolean = false, filterOffersByAccepted: boolean | null, filterOffersByExpired: boolean | null, filterOffersByCustomerId: number | null, filterOffersByUserId: number | null): DocumentNode {
        const query: string = `
            query getAllOffers {
                result: getAllOffers(
                    skip: ${ skip },
                    take: ${ take },
                    sortBy: "${ escape(sortBy) }"
                    pinned: [${ pinned.toString() }],
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                    loadSyntheticProperties: ${ loadSyntheticProperties },
                    filterOffersByAccepted: ${ filterOffersByAccepted },
                    filterOffersByExpired: ${ filterOffersByExpired },
                    filterOffersByCustomerId: ${ filterOffersByCustomerId },
                    filterOffersByUserId: ${ filterOffersByUserId },
                ) {
                    ...PartialOfferFragment
                }
            }
            ${ PARTIAL_OFFER_FRAGMENT(relations) }
            ${ getFragmentsForOfferRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getOfferById(id: number, relations: OfferRelation[] = []): DocumentNode {
        const query: string = `
            query getOfferById {
                result: getOfferById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialOfferFragment
                }
            }
            ${ PARTIAL_OFFER_FRAGMENT(relations) }
            ${ getFragmentsForOfferRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getOfferAmounts(): DocumentNode { // TODO refactor all queries to this syntax...
        const query: string = `
            query getOfferAmounts(
                $filterOffersByAccepted: Boolean,
                $filterOffersByExpired: Boolean,
                $filterOffersByCustomerId: Int,
                $filterOffersByUserId: Int
            ) {
                getOfferAmounts(
                    filterOffersByAccepted: $filterOffersByAccepted,
                    filterOffersByExpired: $filterOffersByExpired,
                    filterOffersByCustomerId: $filterOffersByCustomerId,
                    filterOffersByUserId: $filterOffersByUserId,
                ) {
                    netAmount
                    grossAmount
                }
            }
        `;
        return gql`${query}`;
    }

    static getAllPartners(skip: number, take: number, sortBy: 'name' | 'modification', pinned: number[], relations: CustomerRelation[] = [], loadSyntheticProperties: boolean = false, filterCustomersByUserId: number | null, filterCustomersByIsUser: boolean | null): DocumentNode {
        const query: string = `
            query getAllPartners {
                result: getAllPartners(
                    skip: ${ skip },
                    take: ${ take },
                    sortBy: "${ escape(sortBy) }"
                    filterCustomersByUserId: ${ filterCustomersByUserId },
                    filterCustomersByIsUser: ${ filterCustomersByIsUser },
                    pinned: [${ pinned.toString() }],
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                    loadSyntheticProperties: ${ loadSyntheticProperties },
                ) {
                    ...PartialPartnerFragment
                }
            }
            ${ DEEP_PARTNER_FRAGMENT_STRING }
        `;
        return gql`${ query }`;
    }

    static getPartnerById(id: number): DocumentNode {        
        return gql`
            query getPartnerById {
                result: getPartnerById(
                    id: ${id}
                ) {
                    ...DeepPartnerFragment
                }
            }
            ${DEEP_PARTNER_FRAGMENT}
        `;
    }

    static getPartnerByUserId(userId: number): DocumentNode {
        return gql`
            query getPartnerByUserId {
                result: getPartnerByUserId(
                    userId: ${ userId },
                ) {
                    ...DeepPartnerFragment
                }
            }
            ${ DEEP_PARTNER_FRAGMENT_STRING }
        `;
    }

    static getAllProducts(skip: number, take: number, relations: ProductRelation[] = []): DocumentNode {
        const query: string = `
            query getAllProducts {
                result: getAllProducts(
                    skip: ${ skip },
                    take: ${ take },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialProductFragment
                }
            }
            ${ PARTIAL_PRODUCT_FRAGMENT(relations) }
            ${ getFragmentsForProductRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getProductById(id: number, relations: ProductRelation[] = []): DocumentNode {
        const query: string = `
            query getProductById {
                result: getProductById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialProductFragment
                }
            }
            ${ PARTIAL_PRODUCT_FRAGMENT(relations) }
            ${ getFragmentsForProductRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getProductItemById(id: number, relations: ProductItemRelation[] = []): DocumentNode {
        const query: string = `
            query getProductItemById {
                result: getProductItemById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialProductItemFragment
                }
            }
            ${ PARTIAL_PRODUCTITEM_FRAGMENT(relations) }
            ${ getFragmentsForProductItemRelations(relations) }
        `;
        return gql`${ query }`;
    }
    
    static getAllProductItems(skip: number, take: number, relations: ProductItemRelation[] = []): DocumentNode {
        const query: string = `
            query getAllProductItems {
                result: getAllProductItems(
                    skip: ${ skip },
                    take: ${ take },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialProductItemFragment
                }
            }
            ${ PARTIAL_PRODUCTITEM_FRAGMENT(relations) }
            ${ getFragmentsForProductItemRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getProductPackageById(id: number, relations: ProductPackageRelation[] = []): DocumentNode {
        const query: string = `
            query getProductPackageById {
                result: getProductPackageById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialProductPackageFragment
                }
            }
            ${ PARTIAL_PRODUCTPACKAGE_FRAGMENT(relations) }
            ${ getFragmentsForProductPackageRelations(relations) }
        `;
        return gql`${ query }`;
    }
    
    static getAllProductPackages(skip: number, take: number, relations: ProductPackageRelation[] = []): DocumentNode {
        const query: string = `
            query getAllProductPackages {
                result: getAllProductPackages(
                    skip: ${ skip },
                    take: ${ take },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialProductPackageFragment
                }
            }
            ${ PARTIAL_PRODUCTPACKAGE_FRAGMENT(relations) }
            ${ getFragmentsForProductPackageRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getAllProjects(skip: number, take: number, sortBy: 'name' | 'modification',  pinned: number[], relations: ProjectRelation[] = [], loadSyntheticProperties: boolean = false, filterProjectsByCustomerId: number | null, filterProjectsByPartnerId: number | null, filterProjectsByUserId: number | null): DocumentNode {
        const query: string = `
            query getAllProjects {
                result: getAllProjects(
                    skip: ${ skip },
                    take: ${ take },
                    sortBy: "${ escape(sortBy) }"
                    filterProjectsByCustomerId: ${ filterProjectsByCustomerId },
                    filterProjectsByUserId: ${ filterProjectsByUserId },
                    filterProjectsByPartnerId: ${ filterProjectsByPartnerId },
                    pinned: [${ pinned.toString() }],
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                    loadSyntheticProperties: ${ loadSyntheticProperties },
                ) {
                    ...PartialProjectFragment
                }
            }
            ${ PARTIAL_PROJECT_FRAGMENT(relations) }
            ${ getFragmentsForProjectRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getProjectById(id: number, relations: ProjectRelation[] = []): DocumentNode {
        const query: string = `
            query getProjectById {
                result: getProjectById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialProjectFragment
                }
            }
            ${ PARTIAL_PROJECT_FRAGMENT(relations) }
            ${ getFragmentsForProjectRelations(relations) }
        `;
        return gql`${ query }`;
    }
    
    static getAllTasks(skip: number, take: number, sortBy: 'name' | 'modification' | 'priority' | 'deadline',  pinned: number[], relations: TaskRelation[] = [], loadSyntheticProperties: boolean = false, filterTasksByDone: boolean | null, filterTasksByUserId: number | null): DocumentNode {
        const query: string = `
            query getAllTasks {
                result: getAllTasks(
                    skip: ${ skip },
                    take: ${ take },
                    sortBy: "${ escape(sortBy) }"
                    filterTasksByDone: ${ filterTasksByDone }
                    filterTasksByUserId: ${ filterTasksByUserId }
                    pinned: [${ pinned.toString() }],
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                    loadSyntheticProperties: ${ loadSyntheticProperties },
                ) {
                    ...PartialTaskFragment
                }
            }
            ${ PARTIAL_TASK_FRAGMENT(relations) }
            ${ getFragmentsForTaskRelations(relations) }
        `;
        return gql`${ query }`;
    }
    
    static getTaskById(id: number, relations: TaskRelation[] = []): DocumentNode {
        const query: string = `
            query getTaskById {
                result: getTaskById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialTaskFragment
                }
            }
            ${ PARTIAL_TASK_FRAGMENT(relations) }
            ${ getFragmentsForTaskRelations(relations) }
        `;
        return gql`${ query }`;
    }
    
    static getAllUsers(skip: number, take: number, relations: UserRelation[] = []): DocumentNode {
        const query: string = `
            query getAllUsers {
                result: getAllUsers(
                    skip: ${ skip },
                    take: ${ take },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialUserFragment
                }
            }
            ${ PARTIAL_USER_FRAGMENT(relations) }
            ${ getFragmentsForUserRelations(relations) }
        `;
        return gql`${ query }`;
    }

    static getUserById(id: number, relations: UserRelation[] = []): DocumentNode {
        const query: string = `
            query getUserById {
                result: getUserById(
                    id: ${ id },
                    relations: ${ relations.length < 1 ? '[]' : '["' + relations.join('", "') + '"]' },
                ) {
                    ...PartialUserFragment
                }
            }
            ${ PARTIAL_USER_FRAGMENT(relations) }
            ${ getFragmentsForUserRelations(relations) }
        `;
        return gql`${ query }`;
    }

    // Mutations --------------------------------------------------------------------------------------------------

    static createAddress(input: AddressInput): DocumentNode {
        return gql`
            mutation createAddress {
                result: createAddress(
                    data: {
                        name: "${ escape(input.name) }",
                        description: "${ escape(input.description) }",
                        title: "${ escape(input.title) }",
                        firstName: "${ escape(input.firstName) }",
                        lastName: "${ escape(input.lastName) }",
                        company: "${ escape(input.company) }",
                        showContactPerson: ${ input.showContactPerson },
                        street: "${ escape(input.street) }",
                        houseNumber: "${ escape(input.houseNumber) }",
                        additionalAddress: "${ escape(input.additionalAddress) }",
                        zipCode: "${ escape(input.zipCode) }",
                        city: "${ escape(input.city) }",
                        phoneNumber: "${ escape(input.phoneNumber) }",
                    }
                ) {
                    ...DeepAddressFragment
                }
            }
            ${ DEEP_ADDRESS_FRAGMENT }
        `;
    }

    static updateAddress(id: number, input: AddressInput): DocumentNode {
        return gql`
            mutation updateAddress {
                result: updateAddress(
                    id: ${ id },
                    data: {
                        name: "${ escape(input.name) }",
                        description: "${ escape(input.description) }",
                        title: "${ escape(input.title) }",
                        firstName: "${ escape(input.firstName) }",
                        lastName: "${ escape(input.lastName) }",
                        company: "${ escape(input.company) }",
                        showContactPerson: ${ input.showContactPerson },
                        street: "${ escape(input.street) }",
                        houseNumber: "${ escape(input.houseNumber) }",
                        additionalAddress: "${ escape(input.additionalAddress) }",
                        zipCode: "${ escape(input.zipCode) }",
                        city: "${ escape(input.city) }",
                        phoneNumber: "${ escape(input.phoneNumber) }",
                    }
                ) {
                    ...DeepAddressFragment
                }
            }
            ${ DEEP_ADDRESS_FRAGMENT }
        `;
    }

    static updateAddressAddOffers(id: number, offerIds: number[]): DocumentNode {
        return gql`
            mutation updateAddressAddOffers {
                result: updateAddressAddOffers(
                    id: ${ id },
                    offerIds: [${ offerIds.toString() }],
                ) {
                    ...DeepAddressFragment
                }
            }
            ${ DEEP_ADDRESS_FRAGMENT }
        `;
    }

    static updateAddressRemoveOffers(id: number, offerIds: number[]): DocumentNode {
        return gql`
            mutation updateAddressRemoveOffers {
                result: updateAddressRemoveOffers(
                    id: ${ id },
                    offerIds: [${ offerIds.toString() }],
                ) {
                    ...DeepAddressFragment
                }
            }
            ${ DEEP_ADDRESS_FRAGMENT }
        `;
    }

    static deleteAddress(id: number): DocumentNode {
        return gql`
            mutation deleteAddress {
                result: deleteAddress(
                    id: ${ id },
                ) {
                    ...DeepAddressFragment
                }
            }
            ${ DEEP_ADDRESS_FRAGMENT }
        `;
    }

    static createCustomer(input: CustomerInput): DocumentNode {
        return gql`
            mutation createCustomer {
                result: createCustomer(
                    data: {
                        customerNumber: "${ escape(input.customerNumber) }",
                        title: "${ escape(input.title) }",
                        firstName: "${ escape(input.firstName) }",
                        lastName: "${ escape(input.lastName) }",
                        company: "${ escape(input.company) }",
                        companyId: "${ escape(input.companyId) }",
                        taxId: "${ escape(input.taxId) }",
                        email: "${ escape(input.email) }",
                        phoneNumber: "${ escape(input.phoneNumber) }",
                    }
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomer(id: number, input: CustomerInput): DocumentNode {
        return gql`
            mutation updateCustomer {
                result: updateCustomer(
                    id: ${ id },
                    data: {
                        customerNumber: "${ escape(input.customerNumber) }",
                        title: "${ escape(input.title) }",
                        firstName: "${ escape(input.firstName) }",
                        lastName: "${ escape(input.lastName) }",
                        company: "${ escape(input.company) }",
                        companyId: "${ escape(input.companyId) }",
                        taxId: "${ escape(input.taxId) }",
                        email: "${ escape(input.email) }",
                        phoneNumber: "${ escape(input.phoneNumber) }",
                    }
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerAddAddresses(id: number, addressIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerAddAddresses {
                result: updateCustomerAddAddresses(
                    id: ${ id },
                    addressIds: [${ addressIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerAddFiles(id: number, fileIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerAddFiles {
                result: updateCustomerAddFiles(
                    id: ${ id },
                    fileIds: [${ fileIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerAddOffers(id: number, offerIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerAddOffers {
                result: updateCustomerAddOffers(
                    id: ${ id },
                    offerIds: [${ offerIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerRemoveOffers(id: number, offerIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerRemoveOffers {
                result: updateCustomerRemoveOffers(
                    id: ${ id },
                    offerIds: [${ offerIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerAddProjects(id: number, projectIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerAddProjects {
                result: updateCustomerAddProjects(
                    id: ${ id },
                    projectIds: [${ projectIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerRemoveProjects(id: number, projectIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerRemoveProjects {
                result: updateCustomerRemoveProjects(
                    id: ${ id },
                    projectIds: [${ projectIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerAddSharedFiles(id: number, fileIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerAddSharedFiles {
                result: updateCustomerAddSharedFiles(
                    id: ${ id },
                    fileIds: [${ fileIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerAddTasks(id: number, taskIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerAddTasks {
                result: updateCustomerAddTasks(
                    id: ${ id },
                    taskIds: [${ taskIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static updateCustomerRemoveSharedFiles(id: number, fileIds: number[]): DocumentNode {
        return gql`
            mutation updateCustomerRemoveSharedFiles {
                result: updateCustomerRemoveSharedFiles(
                    id: ${ id },
                    fileIds: [${ fileIds.toString() }],
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }

    static inviteCustomer(id: number): DocumentNode {
        return gql`
            mutation inviteCustomer {
                inviteCustomer(customerId: ${id})
            }
        `;
    } 
    
    static deleteCustomer(id: number): DocumentNode {
        return gql`
            mutation deleteCustomer {
                result: deleteCustomer(
                    id: ${ id },
                ) {
                    ...DeepCustomerFragment
                }
            }
            ${ DEEP_CUSTOMER_FRAGMENT }
        `;
    }
    
    static createFile(input: FileInput): DocumentNode {
        return gql`
            mutation createFile {
                result: createFile(
                    data: {
                        path: "${ escape(input.path) }",
                        originalName: "${ escape(input.originalName) }",
                        name: "${ escape(input.name) }",
                        description: "${ escape(input.description) }",
                        pdfPreviewPath: "${ escape(input.pdfPreviewPath) }",
                    }
                ) {
                    ...DeepFileFragment
                }
            }
            ${ DEEP_FILE_FRAGMENT }
        `;
    }

    static updateFile(id: number, input: FileInput): DocumentNode {
        return gql`
            mutation updateFile {
                result: updateFile(
                    id: ${ id },
                    data: {
                        path: "${ escape(input.path) }",
                        originalName: "${ escape(input.originalName) }",
                        name: "${ escape(input.name) }",
                        description: "${ escape(input.description) }",
                        pdfPreviewPath: "${ escape(input.pdfPreviewPath) }",
                    }
                ) {
                    ...DeepFileFragment
                }
            }
            ${ DEEP_FILE_FRAGMENT }
        `;
    }

    static deleteFile(id: number): DocumentNode {
        return gql`
            mutation deleteFile {
                result: deleteFile(
                    id: ${ id },
                ) {
                    ...DeepFileFragment
                }
            }
            ${ DEEP_FILE_FRAGMENT }
        `;
    }

    static createOffer(input: OfferInput): DocumentNode {
        return gql`
            mutation createOffer {
                result: createOffer(
                    data: {
                        offerNumber: "${ escape(input.offerNumber) }",
                        description: "${ escape(input.description) }",
                        offerDate: ${ input.offerDate ? '"' + Util.getOffsetDateWithoutTime(input.offerDate).toISOString() + '"' : null },
                        offerTerm: ${ input.offerTerm ? '"' + Util.getOffsetDateWithoutTime(input.offerTerm).toISOString() + '"' : null },
                        items: "${ Documents.stringifyInvoiceItemData(input.items) }",
                        netAmount: ${ input.netAmount },
                        grossAmount: ${ input.grossAmount },
                        acceptedDate: ${ input.acceptedDate ? '"' + Util.getOffsetDateWithoutTime(input.acceptedDate).toISOString() + '"' : null },
                        notes: "${ escape(input.notes) }",
                    }
                ) {
                    ...DeepOfferFragment
                }
            }
            ${ DEEP_OFFER_FRAGMENT }
        `;
    }

    static updateOffer(id: number, input: OfferInput): DocumentNode {
        return gql`
            mutation updateOffer {
                result: updateOffer(
                    id: ${ id },
                    data: {
                        offerNumber: "${ escape(input.offerNumber) }",
                        description: "${ escape(input.description) }",
                        offerDate: ${ input.offerDate ? '"' + Util.getOffsetDateWithoutTime(input.offerDate).toISOString() + '"' : null },
                        offerTerm: ${ input.offerTerm ? '"' + Util.getOffsetDateWithoutTime(input.offerTerm).toISOString() + '"' : null },
                        items: "${ Documents.stringifyInvoiceItemData(input.items) }",
                        netAmount: ${ input.netAmount },
                        grossAmount: ${ input.grossAmount },
                        acceptedDate: ${ input.acceptedDate ? '"' + Util.getOffsetDateWithoutTime(input.acceptedDate).toISOString() + '"' : null },
                        notes: "${ escape(input.notes) }",
                    }
                ) {
                    ...DeepOfferFragment
                }
            }
            ${ DEEP_OFFER_FRAGMENT }
        `;
    }

     static updateOfferAddTasks(id: number, taskIds: number[]): DocumentNode {
        return gql`
            mutation updateOfferAddTasks {
                result: updateOfferAddTasks(
                    id: ${ id },
                    taskIds: [${ taskIds.toString() }],
                ) {
                    ...DeepOfferFragment
                }
            }
            ${ DEEP_OFFER_FRAGMENT }
        `;
    }

    static updateOfferRemoveTasks(id: number, taskIds: number[]): DocumentNode {
        return gql`
            mutation updateOfferRemoveTasks {
                result: updateOfferRemoveTasks(
                    id: ${ id },
                    taskIds: [${ taskIds.toString() }],
                ) {
                    ...DeepOfferFragment
                }
            }
            ${ DEEP_OFFER_FRAGMENT }
        `;
    }

    static deleteOffer(id: number): DocumentNode {
        return gql`
            mutation deleteOffer {
                result: deleteOffer(
                    id: ${ id },
                ) {
                    ...DeepOfferFragment
                }
            }
            ${ DEEP_OFFER_FRAGMENT }
        `;
    }

    static createPartnerInvite(email: string): DocumentNode {
        return gql`
            mutation createPartnerInvite {
                result: createPartnerInvite(
                    email: "${escape(email)}"
                )
            }
        `;
    }

    static signUpPartnerWithInvite(input: PartnerUserInput, inviteCode: string, recaptchaToken: string): DocumentNode {
        return gql`
            mutation registerPartnerWithInvite {
                result: registerPartnerWithInvite(
                    input: {
                        firstName: "${escape(input.firstName)}",
                        lastName: "${escape(input.lastName)}",
                        password: "${escape(input.password)}",
                        companyName: "${escape(input.companyName)}",
                        phoneNumber: "${escape(input.phoneNumber)}",
                        languageCode: ${input.languageCode}
                    },
                    inviteCode: "${escape(inviteCode)}",
                    recaptchaToken: "${escape(recaptchaToken)}"
                ) {
                    ...DeepUserFragment
                }
            }
            ${DEEP_USER_FRAGMENT}
        `;
    }

    static updatePartner(id: number, input: PartnerInput): DocumentNode {
        return gql`
            mutation updatePartner {
                result: updatePartner(
                    id: ${ id },
                    data: {
                        companyName: "${ escape(input.companyName) }",
                        contactPerson: "${ escape(input.contactPerson) }",
                        phoneNumber: "${ escape(input.phoneNumber) }",
                        email: "${ escape(input.email) }",
                    }
                ) {
                    ...DeepPartnerFragment
                }
            }
            ${ DEEP_PARTNER_FRAGMENT_STRING }
        `;
    }

    static updatePartnerAddProjects(id: number, projectIds: number[]): DocumentNode {
        return gql`
            mutation updatePartnerAddProjects {
                result: updatePartnerAddProjects(
                    id: ${ id },
                    projectIds: [${ projectIds.toString() }],
                ) {
                    ...DeepPartnerFragment
                }
            }
            ${ DEEP_PARTNER_FRAGMENT }
        `
    }

    static updatePartnerRemoveProjects(id: number, projectIds: number[]): DocumentNode {
        return gql`
            mutation updatePartnerRemoveProjects {
                result: updatePartnerRemoveProjects(
                    id: ${ id },
                    projectIds: [${ projectIds.toString() }],
                ) {
                    ...DeepPartnerFragment
                }
            }
            ${ DEEP_PARTNER_FRAGMENT }
        `
    }

    static createProduct(input: ProductInput): DocumentNode {
        return gql`
            mutation createProduct {
                result: createProduct(
                    data: {
                        name: "${ escape(input.name) }",
                        quantity: ${ input.quantity },
                        pricePerUnit: ${ input.pricePerUnit },
                        showPricePerUnit: ${ input.showPricePerUnit },
                        vat: ${ input.vat },
                        description: "${ escape(input.description) }",
                    }
                ) {
                    ...DeepProductFragment
                }
            }
            ${ DEEP_PRODUCT_FRAGMENT }
        `;
    }
    
    static updateProduct(id: number, input: ProductInput): DocumentNode {
        return gql`
            mutation updateProduct {
                result: updateProduct(
                    id: ${ id },
                    data: {
                        name: "${ escape(input.name) }",
                        quantity: ${ input.quantity },
                        pricePerUnit: ${ input.pricePerUnit },
                        showPricePerUnit: ${ input.showPricePerUnit },
                        vat: ${ input.vat },
                        description: "${ escape(input.description) }",
                    }
                ) {
                    ...DeepProductFragment
                }
            }
            ${ DEEP_PRODUCT_FRAGMENT }
        `;
    }

    static updateProductAddProductItems(id: number, productItemIds: number[]): DocumentNode {
        return gql`
            mutation updateProductAddProductItems {
                result: updateProductAddProductItems(
                    id: ${ id },
                    productItemIds: [${ productItemIds.toString() }],
                ) {
                    ...DeepProductFragment
                }
            }
            ${ DEEP_PRODUCT_FRAGMENT }
        `;
    }

    static createProductItem(input: ProductItemInput): DocumentNode {
        return gql`
            mutation createProductItem {
                result: createProductItem(
                    data: {
                        sortIndex: ${ input.sortIndex },
                        quantity: ${ input.quantity },
                        pricePerUnit: ${ input.pricePerUnit },
                    }
                ) {
                    ...DeepProductItemFragment
                }
            }
            ${ DEEP_PRODUCTITEM_FRAGMENT }
        `;
    }

    static deleteProductItem(id: number): DocumentNode {
        return gql`
            mutation deleteProductItem {
                result: deleteProductItem(
                    id: ${ id },
                ) {
                    ...DeepProductItemFragment
                }
            }
            ${ DEEP_PRODUCTITEM_FRAGMENT }
        `;
    }

    static createProductPackage(input: ProductPackageInput): DocumentNode {
        return gql`
            mutation createProductPackage {
                result: createProductPackage(
                    data: {
                        name: "${ escape(input.name) }",
                    }
                ) {
                    ...DeepProductPackageFragment
                }
            }
            ${ DEEP_PRODUCTPACKAGE_FRAGMENT }
        `;
    }

    static updateProductPackage(id: number, input: ProductPackageInput): DocumentNode {
        return gql`
            mutation updateProductPackage {
                result: updateProductPackage(
                    id: ${ id },
                    data: {
                        name: "${ escape(input.name) }",
                    }
                ) {
                    ...DeepProductPackageFragment
                }
            }
            ${ DEEP_PRODUCTPACKAGE_FRAGMENT }
        `;
    }

    static updateProductPackageAddProductItems(id: number, productItemIds: number[]): DocumentNode {
        return gql`
            mutation updateProductPackageAddProductItems {
                result: updateProductPackageAddProductItems(
                    id: ${ id },
                    productItemIds: [${ productItemIds.toString() }],
                ) {
                    ...DeepProductPackageFragment
                }
            }
            ${ DEEP_PRODUCTPACKAGE_FRAGMENT }
        `;
    }

    static createProject(input: ProjectInput): DocumentNode {
        return gql`
            mutation createProject {
                result: createProject(
                    data: {
                        projectNumber: "${ escape(input.projectNumber) }",
                        name: "${ escape(input.name) }",
                        description: "${ escape(input.description) }",
                    }
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static updateProject(id: number, input: ProjectInput): DocumentNode {
        return gql`
            mutation updateProject {
                result: updateProject(
                    id: ${ id },
                    data: {
                        projectNumber: "${ escape(input.projectNumber) }",
                        name: "${ escape(input.name) }",
                        description: "${ escape(input.description) }",
                    }
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static updateProjectAddOffers(id: number, offerIds: number[]): DocumentNode {
        return gql`
            mutation updateProjectAddOffers {
                result: updateProjectAddOffers(
                    id: ${ id },
                    offerIds: [${ offerIds.toString() }],
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }
    
    static updateProjectSetAddress(id: number, addressId: number): DocumentNode {
        return gql`
            mutation updateProjectSetAddress {
                result: updateProjectSetAddress(
                    id: ${ id },
                    addressId: ${ addressId },
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static updateProjectResetAddress(id: number): DocumentNode {
        return gql`
            mutation updateProjectResetAddress {
                result: updateProjectResetAddress(
                    id: ${ id },
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static updateProjectAddFiles(id: number, fileIds: number[]): DocumentNode {
        return gql`
            mutation updateProjectAddFiles {
                result: updateProjectAddFiles(
                    id: ${ id },
                    fileIds: [${ fileIds.toString() }],
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static updateProjectRemoveFiles(id: number, fileIds: number[]): DocumentNode {
        return gql`
            mutation updateProjectRemoveFiles {
                result: updateProjectRemoveFiles(
                    id: ${ id },
                    fileIds: [${ fileIds.toString() }],
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static updateProjectAddTasks(id: number, taskIds: number[]): DocumentNode {
        return gql`
            mutation updateProjectAddTasks {
                result: updateProjectAddTasks(
                    id: ${ id },
                    taskIds: [${ taskIds.toString() }],
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static updateProjectRemoveTasks(id: number, taskIds: number[]): DocumentNode {
        return gql`
            mutation updateProjectRemoveTasks {
                result: updateProjectRemoveTasks(
                    id: ${ id },
                    taskIds: [${ taskIds.toString() }],
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static deleteProject(id: number): DocumentNode {
        return gql`
            mutation deleteProject {
                result: deleteProject(
                    id: ${ id },
                ) {
                    ...DeepProjectFragment
                }
            }
            ${ DEEP_PROJECT_FRAGMENT }
        `;
    }

    static createTask(input: TaskInput): DocumentNode {
        return gql`
            mutation createTask {
                result: createTask(
                    data: {
                        name: "${ escape(input.name) }",
                        description: "${ escape(input.description) }",
                        checklist: "${ Documents.stringifyTaskChecklist(input.checklist) }",
                        deadline: ${ input.deadline ? '"' + Util.offsetDate(input.deadline).toISOString() + '"' : null },
                        priority: ${ input.priority },
                        done: ${ input.done },
                        isPublic: ${ input.isPublic },
                    }
                ) {
                    ...DeepTaskFragment
                }
            }
            ${ DEEP_TASK_FRAGMENT }
        `;
    }

    static updateTask(id: number, input: TaskInput): DocumentNode {
        return gql`
            mutation updateTask {
                result: updateTask(
                    id: ${ id },
                    data: {
                        name: "${ escape(input.name) }",
                        description: "${ escape(input.description) }",
                        checklist: "${ Documents.stringifyTaskChecklist(input.checklist) }",
                        deadline: ${ input.deadline ? '"' + Util.offsetDate(input.deadline).toISOString() + '"' : null },
                        priority: ${ input.priority },
                        done: ${ input.done },
                        isPublic: ${ input.isPublic },
                    }
                ) {
                    ...DeepTaskFragment
                }
            }
            ${ DEEP_TASK_FRAGMENT }
        `;
    }

    static deleteTask(id: number): DocumentNode {
        return gql`
            mutation deleteTask {
                result: deleteTask(
                    id: ${ id },
                ) {
                    ...DeepTaskFragment
                }
            }
            ${ DEEP_TASK_FRAGMENT }
        `;
    }

    static updateUser(id: number, input: UserInput): DocumentNode {
        const settingsString: string = JSON.stringify(input.settings).replace(new RegExp('\"', 'g'), '~');
        return gql`
            mutation updateUser {
                result: updateUser(
                    id: ${ id },
                    data: {
                        firstName: "${ escape(input.firstName) }",
                        lastName: "${ escape(input.lastName) }",
                        email: "${ escape(input.email) }",
                        password: "${ escape(input.password) }",
                        languageCode: ${ input.languageCode },
                        avatarPath: "${ escape(input.avatarPath) }",
                        phoneNumber: "${ escape(input.phoneNumber) }",
                        settings: "${ escape(settingsString) }",
                        isPartner: ${ input.isPartner },
                    }
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static updateUserAddTasks(id: number, taskIds: number[]): DocumentNode {
        return gql`
            mutation updateUserAddTasks {
                result: updateUserAddTasks(
                    id: ${ id },
                    taskIds: [${ taskIds.toString() }],
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static updateUserSetPermissions(id: number, permissions: UserPermissions): DocumentNode {
        const permissionString: string = JSON.stringify(permissions).replace(new RegExp('\"', 'g'), '~');
        return gql`
            mutation updateUserSetPermissions {
                result: updateUserSetPermissions(
                    id: ${ id },
                    permissions: "${ permissionString }",
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }

    static deleteUser(id: number): DocumentNode {
        return gql`
            mutation deleteUser {
                result: deleteUser(
                    id: ${ id },
                ) {
                    ...DeepUserFragment
                }
            }
            ${ DEEP_USER_FRAGMENT }
        `;
    }
}