import { gql } from 'apollo-angular';
import { DEEP_ADDRESS_FRAGMENT_STRING } from '../entities/address';
import { DEEP_CUSTOMER_FRAGMENT_STRING } from '../entities/customer';
import { DEEP_FILE_FRAGMENT_STRING } from '../entities/file';
import { DEEP_PROJECT_FRAGMENT_STRING } from '../entities/project';
import { DEEP_TASK_FRAGMENT_STRING } from '../entities/task';
import { DEEP_USER_FRAGMENT_STRING } from '../entities/user';
import { DEEP_PARTNER_FRAGMENT_STRING } from '../entities/partner';
import { DEEP_OFFER_FRAGMENT_STRING } from '../entities/offer';
import { DEEP_PRODUCT_FRAGMENT_STRING } from '../entities/product';
import { DEEP_PRODUCTITEM_FRAGMENT_STRING } from '../entities/productitem';
import { DEEP_PRODUCTPACKAGE_FRAGMENT_STRING } from '../entities/productpackage';



export const REQUESTRESULT_FRAGMENT_STRING = `
    fragment RequestResultFragment on RequestResult {
        success
        message
    }
`;

export const SEARCHRESULT_FRAGMENT_STRING = `
    fragment SearchResultFragment on SearchResult {
        customers {
            ...DeepCustomerFragment
        }
        files {
            ...DeepFileFragment
        }
        invoices {
            ...DeepInvoiceFragment
        }
        orders {
            ...DeepOrderFragment
        }
        projects {
            ...DeepProjectFragment
        }
    }
`;

export const DEEP_ADDRESS_FRAGMENT = gql`${ DEEP_ADDRESS_FRAGMENT_STRING }`;
export const DEEP_CUSTOMER_FRAGMENT = gql`${ DEEP_CUSTOMER_FRAGMENT_STRING }`;
export const DEEP_FILE_FRAGMENT = gql`${ DEEP_FILE_FRAGMENT_STRING }`;
export const DEEP_OFFER_FRAGMENT = gql`${ DEEP_OFFER_FRAGMENT_STRING }`;
export const DEEP_PARTNER_FRAGMENT = gql`${ DEEP_PARTNER_FRAGMENT_STRING }`;
export const DEEP_PRODUCT_FRAGMENT = gql`${ DEEP_PRODUCT_FRAGMENT_STRING }`;
export const DEEP_PRODUCTITEM_FRAGMENT = gql`${ DEEP_PRODUCTITEM_FRAGMENT_STRING }`;
export const DEEP_PRODUCTPACKAGE_FRAGMENT = gql`${ DEEP_PRODUCTPACKAGE_FRAGMENT_STRING }`;
export const DEEP_PROJECT_FRAGMENT = gql`${ DEEP_PROJECT_FRAGMENT_STRING }`;
export const DEEP_TASK_FRAGMENT = gql`${ DEEP_TASK_FRAGMENT_STRING }`;
export const DEEP_USER_FRAGMENT = gql`${ DEEP_USER_FRAGMENT_STRING }`;
export const REQUESTRESULT_FRAGMENT = gql`${ REQUESTRESULT_FRAGMENT_STRING }`;
export const SEARCHRESULT_FRAGMENT = gql`${ SEARCHRESULT_FRAGMENT_STRING }`;



export const FRAGMENTS: { [key: string] : string } = {
    'REQUESTRESULT_FRAGMENT': REQUESTRESULT_FRAGMENT_STRING,
    'SEARCHRESULT_FRAGMENT': SEARCHRESULT_FRAGMENT_STRING,
    'DEEP_ADDRESS_FRAGMENT': DEEP_ADDRESS_FRAGMENT_STRING,
    'DEEP_CUSTOMER_FRAGMENT': DEEP_CUSTOMER_FRAGMENT_STRING,
    'DEEP_FILE_FRAGMENT': DEEP_FILE_FRAGMENT_STRING,
    'DEEP_OFFER_FRAGMENT': DEEP_OFFER_FRAGMENT_STRING,
    'DEEP_PARTNER_FRAGMENT': DEEP_PARTNER_FRAGMENT_STRING,
    'DEEP_PRODUCT_FRAGMENT': DEEP_PRODUCT_FRAGMENT_STRING,
    'DEEP_PRODUCTITEM_FRAGMENT': DEEP_PRODUCTITEM_FRAGMENT_STRING,
    'DEEP_PRODUCTPACKAGE_FRAGMENT': DEEP_PRODUCTPACKAGE_FRAGMENT_STRING,
    'DEEP_PROJECT_FRAGMENT': DEEP_PROJECT_FRAGMENT_STRING,
    'DEEP_TASK_FRAGMENT': DEEP_TASK_FRAGMENT_STRING,
    'DEEP_USER_FRAGMENT': DEEP_USER_FRAGMENT_STRING,
};