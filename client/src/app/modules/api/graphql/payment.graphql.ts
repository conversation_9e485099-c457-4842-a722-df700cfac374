import { FRAGMENTS } from './fragments.graphql';
import { PaymentRelation, PARTIAL_PAYMENT_FRAGMENT } from '../entities/payment';

/**
 * GraphQL queries and mutations for payments
 */
export const PAYMENT_QUERIES = {
    getAllPayments: () => {
        return `
            query GetAllPayments {
                getAllPayments {
                    ...PartialPaymentFragment
                }
            }
            ${PARTIAL_PAYMENT_FRAGMENT([])}
        `;
    },

    getPaymentById: (id: number) => {
        return `
            query GetPaymentById {
                getPaymentById(id: ${id}) {
                    ...PartialPaymentFragment
                }
            }
            ${PARTIAL_PAYMENT_FRAGMENT([])}
        `;
    },

    getPaymentsByUserId: (userId: string) => {
        return `
            query GetPaymentsByUserId {
                getPaymentsByUserId(userId: "${userId}") {
                    ...PartialPaymentFragment
                }
            }
            ${PARTIAL_PAYMENT_FRAGMENT([])}
        `;
    },

    getPaymentByRequestId: (requestId: string) => {
        return `
            query GetPaymentByRequestId {
                getPaymentByRequestId(requestId: "${requestId}") {
                    ...PartialPaymentFragment
                }
            }
            ${PARTIAL_PAYMENT_FRAGMENT([])}
        `;
    }
};

export const PAYMENT_MUTATIONS = {
    initiatePayment: (
        userId: string,
        amount: number,
        currency: string,
        description: string,
        metadata?: Record<string, any>,
        isSubscription?: boolean,
        subscriptionId?: string
    ) => {
        const metadataJson = metadata ? JSON.stringify(JSON.stringify(metadata)) : null;
        const isSubscriptionParam = isSubscription !== undefined ? `, isSubscription: ${isSubscription}` : '';
        const subscriptionIdParam = subscriptionId ? `, subscriptionId: "${subscriptionId}"` : '';

        return `
            mutation InitiatePayment {
                initiatePayment(
                    userId: "${userId}",
                    amount: ${amount},
                    currency: "${currency}",
                    description: "${description}"
                    ${metadataJson ? `, metadata: ${metadataJson}` : ''}
                    ${isSubscriptionParam}
                    ${subscriptionIdParam}
                ) {
                    ...PartialPaymentFragment
                }
            }
            ${PARTIAL_PAYMENT_FRAGMENT([])}
        `;
    },

    createPayment: (input: any) => {
        const inputStr = JSON.stringify(input).replace(/"([^"]+)":/g, '$1:');
        
        return `
            mutation CreatePayment {
                createPayment(input: ${inputStr}) {
                    ...PartialPaymentFragment
                }
            }
            ${PARTIAL_PAYMENT_FRAGMENT([])}
        `;
    },

    updatePayment: (id: number, input: any) => {
        const inputStr = JSON.stringify(input).replace(/"([^"]+)":/g, '$1:');
        
        return `
            mutation UpdatePayment {
                updatePayment(id: ${id}, input: ${inputStr}) {
                    ...PartialPaymentFragment
                }
            }
            ${PARTIAL_PAYMENT_FRAGMENT([])}
        `;
    },

    deletePayment: (id: number) => {
        return `
            mutation DeletePayment {
                deletePayment(id: ${id}) {
                    ...PartialPaymentFragment
                }
            }
            ${PARTIAL_PAYMENT_FRAGMENT([])}
        `;
    }
};
