import { InvoiceData } from "./types/invoicedata";
import { TaskChecklist } from "./types/taskchecklist";

export const MAX_GRAPHQL_INT: number = 2147483647;
export const INITIAL_TAKE: number = 20;
export const DEFAULT_COMMENTS_TO_SHOW: number = 10;
export const DEFAULT_FILES_TO_SHOW: number = 20;
export const MAX_SEARCH_RESULTS_PER_ENTITY: number = 50;
export const DEFAULT_TASKS_TO_SHOW: number = 20;
export const AUTOSAVE_DELAY: number = 60 * 1000;
export const DEFAULT_INVOICE_ITEM_DATA: InvoiceData = {
    items: [],
};
export const DEFAULT_TASKCHECKLIST: TaskChecklist = [];