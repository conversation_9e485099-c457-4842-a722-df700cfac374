import { HttpLink } from 'apollo-angular/http';
import { ApolloClientOptions, split, InMemoryCache } from '@apollo/client/core';
import { getMainDefinition } from '@apollo/client/utilities';
import { WebSocketLink } from '@apollo/client/link/ws';
import { ENVIRONMENT } from '../../../../environments/environment';
import { inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';

export const apolloFactory: () => ApolloClientOptions<any> = () => {
    const httpClient = inject(HttpClient);

    const httpLink = new HttpLink(httpClient).create({
        uri: ENVIRONMENT.serverUrl,
        withCredentials: true,
    });

    const webSocketLink = new WebSocketLink({
        uri: ENVIRONMENT.serverWsUrl,
        options: {
            reconnect: true,
        },
    });

    const link = split(
        ({ query }) => {
            const mainDefinition = getMainDefinition(query);
            return mainDefinition.kind === 'OperationDefinition' && mainDefinition.operation === 'subscription';
        },
        webSocketLink,
        httpLink,
    );

    return {
        link,
        credentials: 'same-origin',
        cache: new InMemoryCache(),
        defaultOptions: {
            watchQuery: {
                fetchPolicy: 'cache-first',
                errorPolicy: 'all',
            },
            query: {
                fetchPolicy: 'no-cache',
                errorPolicy: 'all',
            }
        }
    };
};