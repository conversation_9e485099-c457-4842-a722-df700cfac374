import { NgModule } from '@angular/core';
import { APOLLO_OPTIONS, provideApollo } from 'apollo-angular';
import { HttpLink } from 'apollo-angular/http';
import { ApiService } from './providers/api.service';
import { apolloFactory } from './config/apollo.factory';
import { HttpClientModule, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

@NgModule({
    declarations: [],
    imports: [],
    providers: [
        provideHttpClient(withInterceptorsFromDi()),
        provideApollo(() => {
            return apolloFactory();
        }),
        ApiService,
    ],
    exports: [],
})
export class ApiModule {}