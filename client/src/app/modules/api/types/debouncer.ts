export class Debouncer {
    delay: number;

    private timer: NodeJS.Timeout | null = null; 

    constructor(delay: number = 400) {
        this.delay = delay;
    }

    debounce(action: () => void): void {
        if (this.timer) {
            clearTimeout(this.timer);
            this.timer = null;
        }

        this.timer = setTimeout(() => {
            if (!this.timer) {
                return;
            }

            action();
        }, this.delay);
    }
}