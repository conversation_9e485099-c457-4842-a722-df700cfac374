export class MoneyAmount {
    netAmount: number;
    grossAmount: number;

    constructor(data?: Partial<MoneyAmount>) {
        this.netAmount = data?.netAmount ?? 0;
        this.grossAmount = data?.grossAmount ?? 0;
    }
}

// Define the fragment for GraphQL queries
export const MONEY_AMOUNT_FRAGMENT = `
    fragment MoneyAmountFragment on MoneyAmount {
        netAmount
        grossAmount
    }
`;
