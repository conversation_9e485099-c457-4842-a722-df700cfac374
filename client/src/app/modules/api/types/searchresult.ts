import { Customer } from "../entities/customer";
import { File } from "../entities/file";
import { Offer } from "../entities/offer";
import { Partner } from "../entities/partner";
import { Project } from "../entities/project";
import { Task } from "../entities/task";

export interface SearchResult {
    files: Partial<File>[];
    tasks: Partial<Task>[];
    customers: Partial<Customer>[];
    partners: Partial<Partner>[];
    projects: Partial<Project>[];
    offers: Partial<Offer>[];
    // TODO: activate when entities are implemented

    // invoices: Partial<Invoice>[];
    // credits: Partial<Credit>[];
}