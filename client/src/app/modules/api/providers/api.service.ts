import { HttpClient, HttpEventType } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { catchError, filter, firstValueFrom, map, Observable, Subscriber } from 'rxjs';
import { DocumentNode } from 'graphql';
import { FetchResult } from '@apollo/client/core';
import { Apollo } from 'apollo-angular';
import { TranslationService } from '../../translation/providers/translation.service';
import { UserPermissions } from '../types/user-permissions';
import { ENVIRONMENT } from '../../../../environments/environment';
import { UserStatus } from '../../auth/types/user-status';
import { ServerUploadResponse } from '../types/uploadresponse';
import { RequestResult } from '../types/requestresult';
import { UnitSetting } from '../types/unitsetting';
import { SearchResult } from '../types/searchresult';
import { User, UserInput, UserRelation } from '../entities/user';
import { EntityType } from '../enums/entitytype';
import { Documents } from '../graphql/documents.graphql';
import { UserSettings } from '../types/usersettings';
import { CustomerUserInput } from '../types/customer-user-input';
import { Customer, CustomerInput, CustomerRelation } from '../entities/customer';
import { File, FileInput, FileRelation } from '../entities/file';
import { Address, AddressInput, AddressRelation } from '../entities/address';
import { Partner, PartnerInput } from '../entities/partner';
import { PartnerUserInput } from '../types/partner-user.input';
import { LanguageCode } from '../enums/language-code';
import { Task, TaskInput, TaskRelation } from '../entities/task';
import { TaskChecklist } from '../types/taskchecklist';
import { FileUploadProgress } from '../types/fileuploadprogress';
import { Project, ProjectInput, ProjectRelation } from '../entities/project';
import { Offer, OfferInput, OfferRelation } from '../entities/offer';
import { Util } from 'src/app/util/util';
import { InvoiceData } from '../types/invoicedata';
import { MoneyAmount } from '../types/moneyamount';
import { Customization } from '../entities/customization';
import { Product, ProductInput, ProductRelation } from '../entities/product';
import { ProductItem, ProductItemInput, ProductItemRelation } from '../entities/productitem';
import { ProductPackage, ProductPackageInput, ProductPackageRelation } from '../entities/productpackage';

@Injectable({
    providedIn: 'root'
})
export class ApiService {
    constructor(
        protected apollo: Apollo,
        protected httpClient: HttpClient,
        protected translationService: TranslationService,
    ) {
        this.handleUserLanguage();
    }

    static userSortPredicate: (a: Partial<User>, b: Partial<User>) => number = (a: Partial<User>, b: Partial<User>) => {
        if ((a.firstName ?? '') > (b.firstName ?? '')) {
            return 1;
        }
        else if (a.firstName === b.firstName) {
            return (a.lastName ?? '') >= (b.lastName ?? '') ? 1 : -1;
        }

        return -1;
    };

    static userSortPredicateDesc: (a: Partial<User>, b: Partial<User>) => number = (a: Partial<User>, b: Partial<User>) => {
        if ((a.firstName ?? '') > (b.firstName ?? '')) {
            return 1;
        }
        else if (a.firstName === b.firstName) {
            return (a.lastName ?? '') >= (b.lastName ?? '') ? -1 : 1;
        }

        return -1;
    };

    // Apollo ---------------------------------------------------------------------------------------------------------------------------------------------------

    query<T, U>(document: DocumentNode, handler?: (result: T) => U, useCache: boolean = false): Promise<U> {
        return new Promise((resolve, reject) => {
            this.apollo.query({ query: document, fetchPolicy: useCache ? 'cache-first' : 'no-cache' })
                .pipe(
                    catchError((error: any, caught: Observable<FetchResult<unknown, Record<string, any>, Record<string, any>>>) => {
                        reject(error);
                        return caught;
                    }),
                )
                .subscribe((result: FetchResult<unknown, Record<string, any>, Record<string, any>>) => {
                    return resolve(handler ? handler((result.data as any).result as T) : (result.data as any).result as U);
                });
        });
    }

    mutate<T, U>(document: DocumentNode, handler?: (result: T) => U): Promise<U> {
        return new Promise((resolve, reject) => {
            this.apollo.mutate({ mutation: document })
                .pipe(
                    catchError((error: any, caught: Observable<FetchResult<unknown, Record<string, any>, Record<string, any>>>) => {
                        reject(error);
                        return caught;
                    }),
                )
                .subscribe((result: FetchResult<unknown, Record<string, any>, Record<string, any>>) => {
                    return resolve(handler ? handler((result.data as any).result as T) : (result.data as any).result as U);
                });
        });
    }

    subscribe<T, U>(document: DocumentNode, handler?: (result: T) => U): Observable<U> {
        return this.apollo.subscribe({ query: document }).pipe(
            map((result: FetchResult<unknown, Record<string, any>, Record<string, any>>) => {
                return handler ? handler((result.data as any).result as T) : (result.data as any).result as U;
            }),
        );
    }

    // Queries ---------------------------------------------------------------------------------------------------------------------------------------------------

    private handleUserLanguage(): void {
        this.translationService.languageChanged.subscribe((language: string) => {
            this.whoAmI().then((user: Partial<User> | null) => {
                if (!user) {
                    return;
                }

                if (user.languageCode === language) {
                    return;
                }

                if (user.id) {
                    this.updateUser(user.id, {
                        ...user,
                        languageCode: language as LanguageCode,
                    } as UserInput); 
                }
            });
        });
    }

    private getUserPermissions(user: Partial<User>): UserPermissions {
        const transformed: string = (user.permissions + '' as string).replace(new RegExp('~', 'g'), '\'').replace(new RegExp('\'', 'g'), '"');
        let permissions: UserPermissions = user.permissions + '' === '' ? {} : JSON.parse(transformed);

        if ((user.permissions + '' as string).trim() === '{}' || (user.permissions + '' as string).trim() === '') {
            permissions = {
                // TODO adjust as needed
                canViewAndEditOffers: false,
                canViewAndEditInvoicesAndCredits: false,
            };
        }

        // TODO adjust as needed
        permissions.canViewAndEditOffers = permissions.canViewAndEditOffers ?? false;
        permissions.canViewAndEditInvoicesAndCredits = permissions.canViewAndEditInvoicesAndCredits ?? false;

        if (permissions.canViewAndEditInvoicesAndCredits) {
            permissions.canViewAndEditOffers = true;
        }

        if (user.isAdmin) {
            permissions = {
                canViewAndEditOffers: true,
                canViewAndEditInvoicesAndCredits: true,
            };
        }

        return permissions;
    }

    getFullName(customerOrAddress: Partial<Customer> | Partial<Address>): string {
        return ((customerOrAddress.title ?? '') !== '' ? (customerOrAddress.title ?? '')
            .replace('Mr. ', '')
            .replace('Mr.', '')
            .replace('Ms. ', '')
            .replace('Ms.', '')
            .replace('Diverse ', '')
            .replace('Diverse', '')
            + ((customerOrAddress.title ?? '').indexOf('Dr.') !== -1 ? ' ' : '') : '')
            + customerOrAddress.firstName + ' ' + customerOrAddress.lastName;
    }

    convertFullName(fullName: string, languageCode: LanguageCode): Observable<string> {
        return new Observable<string>((subscriber: Subscriber<string>) => {
            if (fullName.indexOf('Family') === -1) {
                subscriber.next(fullName);
                subscriber.complete();
                return;
            }

            let split: string[] = fullName.split(' ');
            split = split.filter((part: string) => part.indexOf('Family') === -1);

            if (languageCode === LanguageCode.de) {
                subscriber.next('Familie ' + split.join(' '));
            }
            else {
                subscriber.next('The ' + split.join(' ') + ' Family');
            }

            subscriber.complete();
        });
    }

    getFileType(file: Partial<File>): string {
        if (!file.originalName) {
            return '';
        }

        let split: string[] = file.originalName.split('.');
        if (split.length < 2) {
            return '';
        }

        return split[split.length - 1].toLowerCase();
    }

    async search(text: string, take: number, types: EntityType[]): Promise<SearchResult> {
        return this.query(Documents.search(text, take, types), (searchResult: SearchResult) => {
            return searchResult;
        });
    }

    async getAllAddresses(skip: number, take: number, sortBy: 'modification' | 'name', relations: AddressRelation[]): Promise<Partial<Address>[]> {
        return this.query(Documents.getAllAddresses(skip, take, sortBy, relations), (addresses: Address[]) => addresses.map((address: Address) => this.resolveAddress(address))
            .filter((address): address is Partial<Address> => address !== null)
        );
    }

    async getAddressById(id: number, relations: AddressRelation[]): Promise<Partial<Address> | null> {
        return this.query(Documents.getAddressById(id, relations), (address: Address) => this.resolveAddress(address));
    }

    async getAllCustomers(skip: number, take: number, sortBy: 'name' | 'modification', pinned: number[], relations: CustomerRelation[], loadSyntheticProperties: boolean = false, filterCustomersByUserId: number | null = null, filterCustomersByIsUser: boolean = false): Promise<Partial<Customer>[]> {
        return this.query(Documents.getAllCustomers(skip, take, sortBy, pinned, relations, loadSyntheticProperties, filterCustomersByUserId, filterCustomersByIsUser), (customers: Customer[]) => customers.map((customer: Customer) => this.resolveCustomer(customer))
            .filter((customer): customer is Partial<Customer> => customer !== null)
        );
    }

    async getCustomerById(id: number, relations: CustomerRelation[]): Promise<Partial<Customer> | null> {
        return this.query(Documents.getCustomerById(id, relations), (customer: Customer) => this.resolveCustomer(customer));
    }

    async signUpCustomer(input: CustomerUserInput, recaptchaToken: string): Promise<Partial<User> | null> {
        const languageCode: string = navigator.language;
        if (languageCode.indexOf('de') !== -1) {
            input.languageCode = LanguageCode.de;
        }
        else {
            input.languageCode = LanguageCode.en;
        }

        return this.query(Documents.signUpCustomer(input, recaptchaToken), (user: User) => this.resolveUser(user));
    }

    // async getAllFiles(skip: number, take: number, relations: FileRelation[], loadSyntheticProperties: boolean = false): Promise<Partial<File>[]> {
    //     return this.query(Documents.getAllFiles(skip, take, relations, loadSyntheticProperties), (files: File[]) => files.map((file: File) => this.resolveFile(file)));
    // }

    async getFileById(id: number, relations: FileRelation[]): Promise<Partial<File> | null> {
        return this.query(Documents.getFileById(id, relations), (file: File) => this.resolveFile(file));
    }

    async uploadFiles(files: globalThis.File[], progressEmitter?: EventEmitter<FileUploadProgress>): Promise<ServerUploadResponse> {
        let size: number = 0;
        const formData: FormData = new FormData();
        files.map((file: globalThis.File) => {
            formData.append('files', file, file.name);

            size += file.size;
        });

        return new Promise<ServerUploadResponse>((resolve, reject) => {
            this.httpClient.post(ENVIRONMENT.uploadUrl, formData, { ...ENVIRONMENT.httpOptions, reportProgress: true, observe: 'events' }).pipe(
                map((event: any) => {
                    if (event.type == HttpEventType.UploadProgress) {
                        if (progressEmitter) {
                            progressEmitter.emit({
                                uploadedSize: event.loaded,
                                progress: event.loaded / event.total,
                            });
                        }

                        return;
                    }

                    return event;
                }),
                filter(e => !!e && e.type && e.type === HttpEventType.Response)
            ).subscribe((response: any) => {
                return resolve(response.body);
            }, (err: Error) => {
                return reject(err);
            });
        });
    }

    async getAllOffers(skip: number, take: number, sortBy: 'offerNumber' | 'modification' | 'offerDate' | 'offerTerm' | 'acceptedDate' | 'grossAmount', pinned: number[],  relations: OfferRelation[], loadSyntheticProperties: boolean = false, filterOffersByAccepted: boolean | null, filterOffersByExpired: boolean | null, filterOffersByCustomerId: number | null, filterOffersByUserId: number | null): Promise<Partial<Offer>[]> {
        if (take === 0) {
            return Promise.resolve([]);
        }
        return this.query(Documents.getAllOffers(skip, take, sortBy, pinned, relations, loadSyntheticProperties, filterOffersByAccepted, filterOffersByExpired, filterOffersByCustomerId, filterOffersByUserId), (offers: Offer[]) => offers
            .map((offer: Offer) => this.resolveOffer(offer)).filter((offer): offer is Partial<Offer> => offer !== null)
        );
    }

    async getOfferById(id: number, relations: OfferRelation[]): Promise<Partial<Offer> | null> {
        return this.query(Documents.getOfferById(id, relations), (offer: Offer) => this.resolveOffer(offer));
    }

    async getOfferAmounts(filterOffersByAccepted: boolean | null, filterOffersByExpired: boolean | null, filterOffersByCustomerId: number | null, filterOffersByUserId: number | null): Promise<MoneyAmount> {
        const query = Documents.getOfferAmounts();
    
        try {
            const response = await firstValueFrom(
                this.apollo.query<{ getOfferAmounts: MoneyAmount }>({
                    query,
                    variables: {
                        filterOffersByAccepted,
                        filterOffersByExpired,
                        filterOffersByCustomerId,
                        filterOffersByUserId
                    },
                    fetchPolicy: 'no-cache'
                })
            );
    
            if (response.errors && response.errors.length > 0) {
                console.error('GraphQL errors:', response.errors);
                throw new Error('Failed to fetch offer amounts');
            }
    
            return response.data.getOfferAmounts;
        } catch (error) {
            console.error('Error fetching offer amounts:', error);
            throw error;
        }
    }

    async getAllPartners(skip: number, take: number, sortBy: 'name' | 'modification', pinned: number[], relations: CustomerRelation[], loadSyntheticProperties: boolean = false, filterCustomersByUserId: number | null = null, filterCustomersByIsUser: boolean = false): Promise<Partial<Partner>[]> {
        if (take === 0) {
            return Promise.resolve([]);
        }
        return this.query(Documents.getAllCustomers(skip, take, sortBy, pinned, relations, loadSyntheticProperties, filterCustomersByUserId, filterCustomersByIsUser), (customers: Customer[]) => customers
            .map((customer: Customer) => this.resolveCustomer(customer)).filter((partner): partner is Partial<Partner> => partner !== null)
        );
    }

    async getPartnerById(id: number): Promise<Partial<Partner> | null> {
        return this.query(Documents.getPartnerById(id), (partner: Partner) => this.resolvePartner(partner));
    }

    async getPartnerByUserId(userId: number): Promise<Partial<Partner> | null> {
        return this.query(Documents.getPartnerByUserId(userId), (partner: Partner) => this.resolvePartner(partner));
    }

    async getAllProducts(skip: number, take: number, relations: ProductRelation[]): Promise<Partial<Product>[]> {
        if (take === 0) {
            return Promise.resolve([]);
        }
        return this.query(Documents.getAllProducts(skip, take, relations), (products: Product[]) => products
            .map((product: Product) => this.resolveProduct(product)).filter((product): product is Partial<Product> => product !== null));
    }

    async getProductById(id: number, relations: ProductRelation[]): Promise<Partial<Product> | null> {
        return this.query(Documents.getProductById(id, relations), (product: Product) => this.resolveProduct(product));
    }

    async getAllProductItems(skip: number, take: number, relations: ProductItemRelation[]): Promise<Partial<ProductItem>[]> {
        if (take === 0) {
            return Promise.resolve([]);
        }
        return this.query(Documents.getAllProductItems(skip, take, relations), (productItems: ProductItem[]) => productItems
            .map((productItem: ProductItem) => this.resolveProductItem(productItem)).filter((productItem): productItem is Partial<ProductItem> => productItem !== null));
    }

    async getProductItemById(id: number, relations: ProductItemRelation[]): Promise<Partial<ProductItem> | null> {
        return this.query(Documents.getProductItemById(id, relations), (productItem: ProductItem) => this.resolveProductItem(productItem));
    }
   
    async getAllProductPackages(skip: number, take: number, relations: ProductPackageRelation[]): Promise<Partial<ProductPackage>[]> {
        if (take === 0) {
            return Promise.resolve([]);
        }
        return this.query(Documents.getAllProductPackages(skip, take, relations), (productPackages: ProductPackage[]) => productPackages
            .map((productPackage: ProductPackage) => this.resolveProductPackage(productPackage)).filter((productPackage): productPackage is Partial<ProductPackage> => productPackage !== null));
    }

    async getProductPackageById(id: number, relations: ProductPackageRelation[]): Promise<Partial<ProductPackage> | null> {
        return this.query(Documents.getProductPackageById(id, relations), (product: Product) => this.resolveProductPackage(product));
    }

    async getAllProjects(skip: number, take: number, sortBy: 'name' | 'modification', pinned: number[], relations: ProjectRelation[], loadSyntheticProperties: boolean = false, filterProjectsByCustomerId: number | null, filterProjectsByPartnerId: number | null, filterProjectsByUserId: number | null): Promise<Partial<Project>[]> {
        return this.query(Documents.getAllProjects(skip, take, sortBy, pinned, relations, loadSyntheticProperties, filterProjectsByCustomerId, filterProjectsByPartnerId, filterProjectsByUserId), (projects: Project[]) => projects
            .map((project: Project) => this.resolveProject(project)).filter((project): project is Partial<Project> => project !== null));
    }

    async getProjectById(id: number, relations: ProjectRelation[]): Promise<Partial<Project> | null> {
        return this.query(Documents.getProjectById(id, relations), (project: Project) => this.resolveProject(project));
    }

    async getAllTasks(skip: number, take: number, sortBy: 'name' | 'modification' | 'priority' | 'deadline', pinned: number[], relations: TaskRelation[], loadSyntheticProperties: boolean = false, filterTasksByDone: boolean | null, filterTasksByUserId: number | null = null): Promise<Partial<Task>[]> {
        if (take === 0) {
            return Promise.resolve([]);
        }

        return this.query(Documents.getAllTasks(skip, take, sortBy, pinned, relations, loadSyntheticProperties, filterTasksByDone, filterTasksByUserId,), (tasks: Task[]) => tasks
            .map((task: Task) => this.resolveTask(task)).filter((task): task is Partial<Task> => task !== null));
    }

    async getTaskById(id: number, relations: TaskRelation[]): Promise<Partial<Task> | null> {
        return this.query(Documents.getTaskById(id, relations), (task: Task) => this.resolveTask(task));
    }

    async getAllCustomizations(skip: number, take: number): Promise<Partial<Customization>[]> {
        if (take === 0) {
            return Promise.resolve([]);
        }

        return this.query(Documents.getAllCustomizations(skip, take), (customizations: Customization[]) => customizations
            .map((customization: Customization) => this.resolveCustomization(customization)).filter((customization): customization is Partial<Customization> => customization !== null));
    }

    async getCustomizationById(id: number): Promise<Partial<Customization> | null> {
        return this.query(Documents.getCustomizationById(id), (customization: Customization) => this.resolveCustomization(customization));
    }

    async getAllUsers(skip: number, take: number, relations: UserRelation[]): Promise<Partial<User>[]> {
        if (take === 0) {
            return Promise.resolve([]);
        }

        return this.query(Documents.getAllUsers(skip, take, relations), (users: User[]) => users.map((user: User) => this.resolveUser(user)).filter((user): user is Partial<User> => user !== null));
    }

    async getUserById(id: number, relations: UserRelation[]): Promise<Partial<User> | null> {
        return this.query(Documents.getUserById(id, relations), (user: User) => this.resolveUser(user));
    }

    async whoAmI(): Promise<Partial<User> | null> {
        return this.query(Documents.whoAmI(), (user: User) => this.resolveUser(user));
    }

    async signUp(input: UserInput, recaptchaToken: string): Promise<Partial<User> | null> {
        const languageCode: string = navigator.language;
        if (languageCode.indexOf('de') !== -1) {
            input.languageCode = LanguageCode.de;
        }
        else {
            input.languageCode = LanguageCode.en;
        }

        return this.query(Documents.signUp(input, recaptchaToken), (user: User) => this.resolveUser(user));
    }

    async activateUser(activationCode: string): Promise<Partial<User> | null> {
        return this.query(Documents.activateUser(activationCode), (user: User) => this.resolveUser(user));
    }

    async deactivateUser(activationCode: string): Promise<Partial<User> | null> {
        return this.query(Documents.deactivateUser(activationCode), (user: User) => this.resolveUser(user));
    }

    async requestPasswordRecovery(email: string): Promise<boolean> {
        return this.query(Documents.requestPasswordRecovery(email));
    }

    async changePassword(newPassword: string, passwordRecoveryCode: string): Promise<Partial<User> | null> {
        return this.query(Documents.changePassword(newPassword, passwordRecoveryCode), (user: User) => this.resolveUser(user));
    }

    async logIn(email: string, password: string): Promise<Partial<User> | null> {
        return this.query(Documents.logIn(email, password), (user: User) => this.resolveUser(user));
    }

    getOnlineStatusForUser(user: Partial<User>): UserStatus {
        const lastUpdatedTime = user.updatedAt ? new Date(user.updatedAt).getTime() : 0;
        const minutesSinceLastUpdate = (new Date().getTime() - lastUpdatedTime) / (60 * 1000);
    
        if (minutesSinceLastUpdate < ENVIRONMENT.userAwayAfterMin) {
            return 'online';
        } else if (minutesSinceLastUpdate < ENVIRONMENT.userOfflineAfterMin) {
            return 'away';
        }
    
        return 'offline';
    }

    async logOut(): Promise<RequestResult> {
        return this.query(Documents.logOut());
    }
   
    resolveAddress(address: Partial<Address> | null): Partial<Address> | null {
        if (!address) {
            return null;
        }
    
        return {
            ...address,
            fullName: this.getFullName(address),
            description: address.description!.replace(new RegExp('&linebreak;', 'g'), '\n'),
            customer: address.customer ? this.resolveCustomer(address.customer) ?? undefined : undefined,
            projects: address.projects ? address.projects.map((project: Partial<Project>) => this.resolveProject(project)).filter((project): project is Partial<Project> => project !== null) : [],
            offers: address.offers ? address.offers.map((offer: Partial<Offer>) => this.resolveOffer(offer)).filter(offer => offer !== null) : [],

            // TODO: activate when entities are implemented
            // invoices: address.invoices ? address.invoices.map((invoice: Invoice) => this.resolveInvoice(invoice)) : [],
            // credits: address.credits ? address.credits.map((credit: Credit) => this.resolveCredit(credit)) : [],
        };
    } 

    resolveCustomer(customer: Partial<Customer> | null): Partial<Customer> | null {
        if (!customer) {
            return null;
        }

        return {
            ...customer,
            fullName: this.getFullName(customer),
            addresses: customer.addresses ? customer.addresses.map((address: Partial<Address>) => this.resolveAddress(address)).filter((address): address is Partial<Address> => address !== null) : [],
            projects: customer.projects ? customer.projects.map((project: Partial<Project>) => this.resolveProject(project)).filter((project): project is Partial<Project> => project !== null) : [],
            files: customer.files ? customer.files.map((file: Partial<File>) => this.resolveFile(file)).filter((file): file is Partial<File> => file !== null) : [],
            sharedFiles: customer.sharedFiles ? customer.sharedFiles.map((file: Partial<File>) => this.resolveFile(file)).filter((file): file is Partial<File> => file !== null) : [],
            tasks: customer.tasks ? customer.tasks.map((task: Partial<Task>) => this.resolveTask(task)).filter((task): task is Partial<Task> => task !== null) : [],
            offers: customer.offers ? customer.offers.map((offer: Partial<Offer>) => this.resolveOffer(offer)).filter(offer => offer !== null) : [],

            // TODO: activate when entities are implemented
            // invoices: customer.invoices ? customer.invoices.map((invoice: Invoice) => this.resolveInvoice(invoice)) : [],
            // credits: customer.credits ? customer.credits.map((credit: Credit) => this.resolveCredit(credit)) : [],
            // comments: customer.comments ? customer.comments.map((comment: Comment) => this.resolveComment(comment)).sort((a: Partial<Comment>, b: Partial<Comment>) => new Date(a.createdAt).getTime() > new Date(b.createdAt).getTime() ? -1 : 1) : [],
        };
    }

    resolveFile(file: Partial<File> | null): Partial<File> | null {
        if (!file) {
            return null;
        }

        return {
            ...file,
            type: this.getFileType(file),
            path: file.path ? file.path.replace('./uploads', ENVIRONMENT.serverProtocol + ENVIRONMENT.serverIp + ENVIRONMENT.serverPort + '/uploads') : '',
            pdfPreviewPath: file.pdfPreviewPath ? file.pdfPreviewPath.replace('./uploads', ENVIRONMENT.serverProtocol + ENVIRONMENT.serverIp + ENVIRONMENT.serverPort + '/uploads') : '',
            customer: file.customer ? this.resolveCustomer(file.customer) ?? undefined : undefined,
            project: file.project ? this.resolveProject(file.project) ?? undefined : undefined,
            sharedCustomer: file.sharedCustomer ? this.resolveCustomer(file.sharedCustomer) ?? undefined : undefined,
        };
    }

    resolveOffer(offer: Partial<Offer> | null): Partial<Offer> | null {
        if (!offer) {
            return null;
        }

        let items: InvoiceData = Documents.parseInvoiceItemData(offer.items + '' as string);

        return {
            ...offer,
            offerDate: offer.offerDate ? Util.offsetDate(new Date(offer.offerDate)) : undefined,
            offerTerm: offer.offerTerm ? Util.offsetDate(new Date(offer.offerTerm)) : undefined,
            acceptedDate: offer.acceptedDate ? Util.offsetDate(new Date(offer.acceptedDate)) : undefined,
            description: (offer.description ?? '').replace(new RegExp('&linebreak;', 'g'), '\n'),
            notes: (offer.notes ?? '').replace(new RegExp('&linebreak;', 'g'), '\n'),
            items,
            project: offer.project ? this.resolveProject(offer.project) ?? undefined : undefined,
            customer: offer.customer ? this.resolveCustomer(offer.customer) ?? undefined : undefined,
            address: offer.address ? this.resolveAddress(offer.address) ??  undefined : undefined,
            // TODO: activate when entities are implemented
            // invoices: offer.invoices ? offer.invoices.map((invoice: Invoice) => this.resolveInvoice(invoice)) : [],
            tasks: offer.tasks ? offer.tasks.map((task: Partial<Task>) => this.resolveTask(task)).filter((task): task is Partial<Task> => task !== null) : [],
        };
    }

    resolvePartner(partner: Partial<Partner>): Partial<Partner> | null {
        if (!partner) {
            return null;
        }

        return {
            ...partner,
            projects: partner.projects ? partner.projects.map((project: Partial<Project>) => this.resolveProject(project)).filter((project): project is Partial<Project> => project !== null) : [],
            files: partner.files ? partner.files.map((file: Partial<File>) => this.resolveFile(file)).filter((file): file is Partial<File> => file !== null) : [],
        };
    }

    resolveProduct(product: Partial<Product> | null): Partial<Product> | null {
        if (!product) {
            return null;
        }

        return {
            ...product,
            description: product.description!.replace(new RegExp('&linebreak;', 'g'), '\n').replace(new RegExp('&quot;', 'g'), '"') ?? '',
            productItems: product.productItems ? product.productItems.map((productItem: Partial<ProductItem>) => this.resolveProductItem(productItem)).filter((productItem): productItem is Partial<ProductItem> => productItem !== null)
                .sort((a: Partial<ProductItem>, b: Partial<ProductItem>) => 
                    (a.sortIndex ?? 0) > (b.sortIndex ?? 0) ? 1 : -1) : [],
        };
    }

    resolveProductItem(productItem: Partial<ProductItem> | null): Partial<ProductItem> | null {
        if (!productItem) {
            return null;
        }

        return {
            ...productItem,
            productPackage: productItem.productPackage ? this.resolveProductPackage(productItem.productPackage) ?? undefined : undefined,
            product: productItem.product ? this.resolveProduct(productItem.product) ?? undefined : undefined,
        };
    }

    resolveProductPackage(productPackage: Partial<ProductPackage> | null): Partial<ProductPackage> | null {
        if (!productPackage) {
            return null;
        }

        return {
            ...productPackage,
            productItems: productPackage.productItems ? productPackage.productItems.map((productItem: Partial<ProductItem>) => this.resolveProductItem(productItem)).filter((productItem): productItem is Partial<ProductItem> => productItem !== null)
                .sort((a: Partial<ProductItem>, b: Partial<ProductItem>) => (a.sortIndex ?? 0) > (b.sortIndex ?? 0) ? 1 : -1) : [],
        };
    }


    resolveProject(project: Partial<Project> | null): Partial<Project> | null {
        if (!project) {
            return null;
        }

        return {
            ...project,
            customer: this.resolveCustomer(project.customer!) ?? undefined,
            address: this.resolveCustomer(project.address!) ?? undefined,
            description: project.description!.replace(new RegExp('&linebreak;', 'g'), '\n') ?? '',
            // TODO: activate when entities are implemented
            // comments: project.comments ? project.comments.map((comment: Comment) => this.resolveComment(comment)).sort((a: Partial<Comment>, b: Partial<Comment>) => new Date(a.createdAt).getTime() > new Date(b.createdAt).getTime() ? -1 : 1) : [],
            files: project.files ? project.files.map((file: Partial<File>) => this.resolveFile(file)).filter((file): file is Partial<File> => file !== null) : [],
            tasks: project.tasks ? project.tasks.map((task: Partial<Task>) => this.resolveTask(task)).filter((task): task is Partial<Task> => task !== null) : [],
        };
    }

    resolveTask(task: Partial<Task> | null): Partial<Task> | null {
        if (!task) {
            return null;
        }

        let taskChecklist: TaskChecklist = Documents.parseTaskChecklist(task.checklist + '' as string);

        return {
            ...task,
            description: (task.description ?? '').replace(new RegExp('&linebreak;', 'g'), '\n').replace(new RegExp('&quot;', 'g'), '"'),
            checklist: taskChecklist,
            user: this.resolveUser(task.user!) ?? undefined,
            customer: this.resolveCustomer(task.customer!) ?? undefined,
            project: this.resolveProject(task.project!) ?? undefined,
            offer: this.resolveOffer(task.offer!) ?? undefined,

            // TODO: activate when entities are implemented
            // invoice: this.resolveInvoice(task.invoice),
            // credit: this.resolveCredit(task.credit),
        };
    }

     resolveCustomization(customization: Partial<Customization> | null): Partial<Customization> | null {
        if (!customization) {
            return null;
        }

        return {
            ...customization,
            defaultOfferDescription: (customization.defaultOfferDescription ?? '').replace(new RegExp('&linebreak;', 'g'), '\n').replace(new RegExp('&quot;', 'g'), '"'),
            defaultInvoiceDescription: (customization.defaultInvoiceDescription ?? '').replace(new RegExp('&linebreak;', 'g'), '\n').replace(new RegExp('&quot;', 'g'), '"'),
            defaultCreditDescription: (customization.defaultCreditDescription ?? '').replace(new RegExp('&linebreak;', 'g'), '\n').replace(new RegExp('&quot;', 'g'), '"'),
            defaultOfferNotes: (customization.defaultOfferNotes ?? '').replace(new RegExp('&linebreak;', 'g'), '\n').replace(new RegExp('&quot;', 'g'), '"'),
            defaultInvoiceNotes: (customization.defaultInvoiceNotes ?? '').replace(new RegExp('&linebreak;', 'g'), '\n').replace(new RegExp('&quot;', 'g'), '"'),
            defaultCreditNotes: (customization.defaultCreditNotes ?? '').replace(new RegExp('&linebreak;', 'g'), '\n').replace(new RegExp('&quot;', 'g'), '"'),
            iconPath: customization.iconPath ? customization.iconPath.replace('./uploads', ENVIRONMENT.serverProtocol + ENVIRONMENT.serverIp + ENVIRONMENT.serverPort + '/uploads') : '',
            wordmarkPath: customization.wordmarkPath ? customization.wordmarkPath.replace('./uploads', ENVIRONMENT.serverProtocol + ENVIRONMENT.serverIp + ENVIRONMENT.serverPort + '/uploads') : '',
            combinationmarkPath: customization.combinationmarkPath ? customization.combinationmarkPath.replace('./uploads', ENVIRONMENT.serverProtocol + ENVIRONMENT.serverIp + ENVIRONMENT.serverPort + '/uploads') : '',
            qrCodePath: customization.qrCodePath ? customization.qrCodePath.replace('./uploads', ENVIRONMENT.serverProtocol + ENVIRONMENT.serverIp + ENVIRONMENT.serverPort + '/uploads') : '',
        };
    }

    resolveUser(user: Partial<User> | null): Partial<User> | null {
        if (!user) {
            return null;
        }

        let permissions: UserPermissions = this.getUserPermissions(user);

        let settings: UserSettings = user.settings + '' === '' ? {} : JSON.parse((user.settings + '' as string).replace(new RegExp('~', 'g'), '\"'));
        if ((user.settings + '' as string).trim() === '{}' || (user.settings + '' as string).trim() === '') {
            settings = {
                unitSetting: UnitSetting.Meters,
            };
        }

        return {
            ...user,
            // TODO: activate when online status is implemented
            // status: this.getOnlineStatusForUser(user), 
            avatarPath: user.avatarPath ? user.avatarPath.replace('./uploads', ENVIRONMENT.serverProtocol + ENVIRONMENT.serverIp + ENVIRONMENT.serverPort + '/uploads') : '',
            permissions,
            settings,
            tasks: user.tasks ? user.tasks.map((task: Partial<Task>) => this.resolveTask(task)).filter((task): task is Partial<Task> => task !== null) : [],
        };
    }

    // Mutations ---------------------------------------------------------------------------------------------------------------------------------------------------

    async createAddress(input: AddressInput): Promise<Partial<Address> | null> {
        return this.mutate(Documents.createAddress(input), (address: Address) => this.resolveAddress(address));
    }

    async updateAddress(id: number, input: AddressInput): Promise<Partial<Address> | null> {
        return this.mutate(Documents.updateAddress(id, input), (address: Address) => this.resolveAddress(address));
    }

    async updateAddressAddOffers(id: number, offerIds: number[]): Promise<Partial<Address> | null> {
        return this.mutate(Documents.updateAddressAddOffers(id, offerIds), (address: Address) => this.resolveAddress(address));
    }

    async updateAddressRemoveOffers(id: number, offerIds: number[]): Promise<Partial<Address> | null> {
        return this.mutate(Documents.updateAddressRemoveOffers(id, offerIds), (address: Address) => this.resolveAddress(address));
    }

    async deleteAddress(id: number): Promise<Partial<Address> | null> {
        return this.mutate(Documents.deleteAddress(id), (address: Address) => this.resolveAddress(address));
    }
    
    async createCustomer(input: CustomerInput): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.createCustomer(input), (customer: Customer) => this.resolveCustomer(customer));
    }
    
    async signUpExistingCustomer(customerNumber: string, email: string, recaptchaToken: string): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.signUpExistingCustomer(customerNumber, email, recaptchaToken), (customer: Customer) => this.resolveCustomer(customer));
    }

    async updateCustomer(id: number, input: CustomerInput): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomer(id, input), (customer: Customer) => this.resolveCustomer(customer));
    }

    async updateCustomerAddAddresses(id: number, addressIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerAddAddresses(id, addressIds), (customer: Customer) => this.resolveCustomer(customer));
    }

    async updateCustomerAddFiles(id: number, fileIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerAddFiles(id, fileIds), (customer: Customer) => this.resolveCustomer(customer));
    }

    async updateCustomerAddOffers(id: number, offerIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerAddOffers(id, offerIds), (customer: Partial<Customer>) => this.resolveCustomer(customer));
    }

    async updateCustomerRemoveOffers(id: number, offerIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerRemoveOffers(id, offerIds), (customer: Partial<Customer>) => this.resolveCustomer(customer));
    }

    async updateCustomerAddProjects(id: number, projectIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerAddProjects(id, projectIds), (customer: Customer) => this.resolveCustomer(customer));
    }

    async updateCustomerRemoveProjects(id: number, projectIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerRemoveProjects(id, projectIds), (customer: Customer) => this.resolveCustomer(customer));
    }

    async updateCustomerAddSharedFiles(id: number, fileIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerAddSharedFiles(id, fileIds), (customer: Customer) => this.resolveCustomer(customer));
    }

    async updateCustomerAddTasks(id: number, taskIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerAddTasks(id, taskIds), (customer: Partial<Customer>) => this.resolveCustomer(customer));
    }
    
    async updateCustomerRemoveSharedFiles(id: number, fileIds: number[]): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.updateCustomerRemoveSharedFiles(id, fileIds), (customer: Customer) => this.resolveCustomer(customer));
    }

    async inviteCustomer(id: number): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.inviteCustomer(id), (customer: Customer) => this.resolveCustomer(customer));
    }

    async deleteCustomer(id: number): Promise<Partial<Customer> | null> {
        return this.mutate(Documents.deleteCustomer(id), (customer: Customer) => this.resolveCustomer(customer));
    }

    async createFile(input: FileInput): Promise<Partial<File> | null> {
        return this.mutate(Documents.createFile(input), (file: File) => this.resolveFile(file));
    }

    async updateFile(id: number, input: FileInput): Promise<Partial<File> | null> {
        input = {
            ...input,
            path: input.path ? './' + input.path.substring(input.path.indexOf('uploads'), input.path.length) : '',
        };

        return this.mutate(Documents.updateFile(id, input), (file: File) => this.resolveFile(file));
    }

    async deleteFile(id: number): Promise<Partial<File> | null> {
        return this.mutate(Documents.deleteFile(id), (file: File) => this.resolveFile(file));
    }

    async createOffer(input: OfferInput): Promise<Partial<Offer> | null> {
        return this.mutate(Documents.createOffer(input), (offer: Partial<Offer>) => this.resolveOffer(offer));
    }

    async updateOffer(id: number, input: OfferInput): Promise<Partial<Offer> | null> {
        return this.mutate(Documents.updateOffer(id, input), (offer: Partial<Offer>) => this.resolveOffer(offer));
    }

    async updateOfferAddTasks(id: number, taskIds: number[]): Promise<Partial<Offer> | null> {
        return this.mutate(Documents.updateOfferAddTasks(id, taskIds), (offer: Partial<Offer>) => this.resolveOffer(offer));
    }

    async updateOfferRemoveTasks(id: number, taskIds: number[]): Promise<Partial<Offer> | null> {
        return this.mutate(Documents.updateOfferRemoveTasks(id, taskIds), (offer: Partial<Offer>) => this.resolveOffer(offer));
    }

    async deleteOffer(id: number): Promise<Partial<Offer> | null> {
        return this.mutate(Documents.deleteOffer(id), (offer: Partial<Offer>) => this.resolveOffer(offer));
    }

    async createPartnerInvite(email: string): Promise<boolean> {
        return this.mutate(Documents.createPartnerInvite(email), (result: boolean) => result);
    }

    async signUpPartnerWithInvite(input: PartnerUserInput, inviteCode: string, recaptchaToken: string): Promise<Partial<User> | null> {
        return this.mutate(
            Documents.signUpPartnerWithInvite(input, inviteCode, recaptchaToken),
            (user: User) => this.resolveUser(user)
        );
    }

    async updatePartner(id: number, input: PartnerInput): Promise<Partial<Partner> | null> {
        return this.mutate(Documents.updatePartner(id, input), (partner: Partner) => this.resolvePartner(partner));
    }

    async updatePartnerAddProjects(id: number, projectIds: number[]): Promise<Partial<Partner> | null> {
        return this.mutate(Documents.updatePartnerAddProjects(id, projectIds), (partner: Partner) => this.resolvePartner(partner));
    }

    async updatePartnerRemoveProjects(id: number, projectIds: number[]): Promise<Partial<Partner> | null> {
        return this.mutate(Documents.updatePartnerRemoveProjects(id, projectIds), (partner: Partner) => this.resolvePartner(partner));
    }

    async createProduct(input: ProductInput): Promise<Partial<Product> | null> {
        return this.mutate(Documents.createProduct(input), (product: Product) => this.resolveProduct(product));
    }

    async updateProduct(id: number, input: ProductInput): Promise<Partial<Product> | null> {
        return this.mutate(Documents.updateProduct(id, input), (product: Product) => this.resolveProduct(product));
    }

    async updateProductAddProductItems(id: number, productItemIds: number[]): Promise<Partial<Product> | null> {
        return this.mutate(Documents.updateProductAddProductItems(id, productItemIds), (product: Product) => this.resolveProduct(product));
    }

    async createProductItem(input: ProductItemInput): Promise<Partial<ProductItem> | null> {
        return this.mutate(Documents.createProductItem(input), (productItem: ProductItem) => this.resolveProductItem(productItem));
    }

    async deleteProductItem(id: number): Promise<Partial<ProductItem> | null> {
        return this.mutate(Documents.deleteProductItem(id), (productItem: ProductItem) => this.resolveProductItem(productItem));
    }

    async createProductPackage(input: ProductPackageInput): Promise<Partial<ProductPackage> | null> {
        return this.mutate(Documents.createProductPackage(input), (productPackage: ProductPackage) => this.resolveProductPackage(productPackage));
    }

    async updateProductPackage(id: number, input: ProductPackageInput): Promise<Partial<ProductPackage> | null> {
        return this.mutate(Documents.updateProductPackage(id, input), (productPackage: ProductPackage) => this.resolveProductPackage(productPackage));
    }

    async updateProductPackageAddProductItems(id: number, productItemIds: number[]): Promise<Partial<ProductPackage> | null> {
        return this.mutate(Documents.updateProductPackageAddProductItems(id, productItemIds), (productPackage: ProductPackage) => this.resolveProductPackage(productPackage));
    }

    async createProject(input: ProjectInput): Promise<Partial<Project> | null> {
        return this.mutate(Documents.createProject(input), (project: Partial<Project>) => this.resolveProject(project));
    }

    async updateProject(id: number, input: ProjectInput): Promise<Partial<Project> | null> {
        return this.mutate(Documents.updateProject(id, input), (project: Partial<Project>) => this.resolveProject(project));
    }

    async updateProjectAddOffers(id: number, offerIds: number[]): Promise<Partial<Project> | null> {
        return this.mutate(Documents.updateProjectAddOffers(id, offerIds), (project: Partial<Project>) => this.resolveProject(project));
    }

    async updateProjectSetAddress(id: number, addressId: number): Promise<Partial<Project> | null> {
        return this.mutate(Documents.updateProjectSetAddress(id, addressId), (project: Partial<Project>) => this.resolveProject(project));
    }

    async updateProjectResetAddress(id: number): Promise<Partial<Project> | null> {
        return this.mutate(Documents.updateProjectResetAddress(id), (project: Partial<Project>) => this.resolveProject(project));
    }

    async updateProjectAddFiles(id: number, fileIds: number[]): Promise<Partial<Project> | null> {
        return this.mutate(Documents.updateProjectAddFiles(id, fileIds), (project: Partial<Project>) => this.resolveProject(project));
    }

    async updateProjectRemoveFiles(id: number, fileIds: number[]): Promise<Partial<Project> | null> {
        return this.mutate(Documents.updateProjectRemoveFiles(id, fileIds), (project: Partial<Project>) => this.resolveProject(project));
    }

    async updateProjectAddTasks(id: number, taskIds: number[]): Promise<Partial<Project> | null> {
        return this.mutate(Documents.updateProjectAddTasks(id, taskIds), (project: Partial<Project>) => this.resolveProject(project));
    }

    async updateProjectRemoveTasks(id: number, taskIds: number[]): Promise<Partial<Project> | null> {
        return this.mutate(Documents.updateProjectRemoveTasks(id, taskIds), (project: Partial<Project>) => this.resolveProject(project));
    }

    async deleteProject(id: number): Promise<Partial<Project> | null> {
        return this.mutate(Documents.deleteProject(id), (project: Partial<Project>) => this.resolveProject(project));
    }

    async createTask(input: TaskInput): Promise<Partial<Task> | null> {
        return this.mutate(Documents.createTask(input), (task: Partial<Task>) => this.resolveTask(task));
    }

    async updateTask(id: number, input: TaskInput): Promise<Partial<Task> | null> {
        return this.mutate(Documents.updateTask(id, input), (task: Partial<Task>) => this.resolveTask(task));
    }

    async deleteTask(id: number): Promise<Partial<Task> | null> {
        return this.mutate(Documents.deleteTask(id), (task: Partial<Task>) => this.resolveTask(task));
    }

    async updateUser(id: number, input: UserInput): Promise<Partial<User> | null> {
        input = {
            ...input,
            avatarPath: input.avatarPath ? './' + input.avatarPath.substring(input.avatarPath.indexOf('uploads'), input.avatarPath.length) : '',
        };

        return this.mutate(Documents.updateUser(id, input), (user: User) => this.resolveUser(user));
    }

    async uploadAvatar(avatar: globalThis.File): Promise<ServerUploadResponse> {
        const formData: FormData = new FormData();
        formData.append('files', avatar, avatar.name);
    
        // Make sure we're using the correct upload URL from environment
        return new Promise<ServerUploadResponse>((resolve, reject) => {
            this.httpClient.post(ENVIRONMENT.uploadUrl, formData, ENVIRONMENT.httpOptions)
                .subscribe(
                    (response: any) => resolve(response),
                    (error: Error) => reject(error)
                );
        });
    }

    async updateUserAddTasks(id: number, taskIds: number[]): Promise<Partial<User> | null> {
        return this.mutate(Documents.updateUserAddTasks(id, taskIds), (user: Partial<User>) => this.resolveUser(user));
    }

    async deleteUser(id: number): Promise<Partial<User> | null> {
        return this.mutate(Documents.deleteUser(id), (user: User) => this.resolveUser(user));
    }
}