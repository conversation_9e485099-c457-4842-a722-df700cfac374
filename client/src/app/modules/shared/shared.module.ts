import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Route, RouterModule } from '@angular/router';
import { MaterialModule } from '../material/material.module';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslationModule } from '../translation/translation.module';
// Providers
import { SharedService } from './providers/shared/shared.service';
import { KeepAliveDirective } from './directives/keepalive.directive';


const COMPONENTS: any[] = [];

const PROVIDERS: any[] = [
    SharedService
];

const DIRECTIVES: any[] = [
    KeepAliveDirective,
];

const PIPES: any[] = [];


@NgModule({
    declarations: [
        ...COMPONENTS,
        ...DIRECTIVES,
        ...PIPES,
    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        RouterModule,
        MaterialModule,
        TranslationModule,
    ],
    providers: [
        ...PROVIDERS,
       
    ],
    exports: [
        ...COMPONENTS,
        ...DIRECTIVES,
        ...PIPES,
    ],
})
export class SharedModule {}