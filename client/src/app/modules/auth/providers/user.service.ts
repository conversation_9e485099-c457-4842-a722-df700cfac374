import { Injectable } from '@angular/core';
import { Observable, ReplaySubject, Subscriber } from 'rxjs';
import { User, UserInput } from '../../api/entities/user';
import { ApiService } from '../../api/providers/api.service';

@Injectable({
    providedIn: 'root'
})
export class UserService {
    private _user: ReplaySubject<Partial<User> | null> = new ReplaySubject<Partial<User> | null>(1);
    set user(value: User) {
        this._user.next(value);
    }
    get user$(): Observable<Partial<User> | null> {
        return this._user.asObservable();
    }

    constructor(
        private apiService: ApiService,
    ) {
    }

    get(): Observable<Partial<User> | null> {
        return new Observable<Partial<User> | null>((subscriber: Subscriber<Partial<User> | null>) => {
            this.apiService.whoAmI().then(async (user: Partial<User> | null) => {
                this._user.next(user);
                subscriber.next(user);
                subscriber.complete();
            });
        });
    }

    refresh(): void {
        this.apiService.whoAmI().then(async (user: Partial<User> | null) => {
            this._user.next(user);
        });
    }

    update(user: UserInput): Observable<User> {
        return new Observable<User>((subscriber: Subscriber<Partial<User> | null>) => {
            this.apiService.whoAmI().then(async (user: Partial<User> | null) => {
                this._user.next(user as User);
                subscriber.next(null);
                subscriber.complete();
            });
        });
    }
}
