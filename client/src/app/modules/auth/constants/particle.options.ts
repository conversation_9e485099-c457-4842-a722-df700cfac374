import { IParticlesProps } from '@tsparticles/angular';

export const PARTICLE_OPTIONS: IParticlesProps = {
        particles: {
            number: {
                value: 80,
            },
            color: {
                value: '#ffffff',
            },
            links: {
                enable: true,
                distance: 200,
                opacity: 0.2,
            },
            shape: {
                type: 'circle',
            },
            opacity: {
                value: 0.4,
            },
            size: {
                value: {
                    min: 2,
                    max: 4,
                }
            },
            move: {
                enable: true,
                speed: 0.7,
            }
        },
        background: {
            color: '#000000',
        },
        fullScreen: false,
        poisson: {
            enable: true,
        },
        style: {
            'width': '50% !important',
            'height': '100% !important',
            'position': 'fixed !important',
            'zIndex': '0 !important',
            'top': '0px !important',
            'left': '50% !important',
            'pointerEvents': 'none !important',
        },
    };