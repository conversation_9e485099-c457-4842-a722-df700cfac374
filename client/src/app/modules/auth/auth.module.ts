import { inject, NgModule, provideEnvironmentInitializer } from '@angular/core';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { Route, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxParticlesModule } from '@tsparticles/angular';
import { DEFAULT_APP_ROUTE } from '../../app.constants';
import { FuseModule } from '../fuse/fuse.module';
import { MaterialModule } from '../material/material.module';
import { SharedModule } from '../shared/shared.module';
import { TranslationModule } from '../translation/translation.module';

// Components
import { AuthConfirmationRequiredComponent } from './components/confirmation-required/confirmation-required.component';
import { AuthForgotPasswordComponent } from './components/forgot-password/forgot-password.component';
import { AuthResetPasswordComponent } from './components/reset-password/reset-password.component';
import { AuthResetPasswordSuccessComponent } from './components/reset-password-success/reset-password-success.component';
import { AuthSignInComponent } from './components/sign-in/sign-in.component';
import { AuthSignOutComponent } from './components/sign-out/sign-out.component';
import { AuthCustomerSignUpComponent } from './components/customer-sign-up/customer-sign-up.component';
import { AuthPartnerSignUpComponent } from './components/partner-sign-up/partner-sign-up.component';
import { AuthSignUpComponent } from './components/sign-up/sign-up.component';
import { HeaderComponent } from './components/header/header.component';
import { FooterComponent } from './components/footer/footer.component';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { TeaserComponent } from './components/teaser/teaser.component';
import { UserComponent } from './components/user/user.component';

// Directives

//Providers
import { AuthService } from './providers/auth.service';
import { authInterceptor } from './providers/auth.interceptor';
import { UserService } from './providers/user.service';
import { NoAuthGuard } from './guards/noAuth.guard';

const COMPONENTS: any[] = [
    AuthConfirmationRequiredComponent,
    AuthForgotPasswordComponent,
    AuthResetPasswordComponent,
    AuthResetPasswordSuccessComponent,
    AuthSignInComponent,
    AuthSignOutComponent,
    AuthCustomerSignUpComponent,
    AuthPartnerSignUpComponent,
    AuthSignUpComponent,
    HeaderComponent,
    FooterComponent,
    UserComponent,
    TeaserComponent,
];

const DIRECTIVES: any[] = [];

export const AUTH_ROUTES: Route[] = [
    { path: 'signed-in-redirect', pathMatch: 'full', redirectTo: DEFAULT_APP_ROUTE },
    {
        path: 'confirmation-required',
        component: LayoutComponent,
        data: { layout: 'empty' },
        canActivate: [NoAuthGuard],
        children: [{ path: '', component: AuthConfirmationRequiredComponent }]
    },
    {
        path: 'forgot-password',
        component: LayoutComponent,
        data: { layout: 'empty' },
        canActivate: [NoAuthGuard],
        children: [{ path: '', component: AuthForgotPasswordComponent }]
    },
    {
        path: 'sign-in',
        component: LayoutComponent,
        data: { layout: 'empty' },
        canActivate: [NoAuthGuard],
        children: [{ path: '', component: AuthSignInComponent }]
    },
    {
        path: 'sign-up',
        component: LayoutComponent,
        data: { layout: 'empty' },
        canActivate: [NoAuthGuard],
        children: [{ path: '', component: AuthSignUpComponent }]
    },
    {
        path: 'customer-sign-up',
        component: LayoutComponent,
        data: { layout: 'empty' },
        canActivate: [NoAuthGuard],
        children: [{ path: '', component: AuthCustomerSignUpComponent }]
    },
    {
        path: 'partner-sign-up/:inviteCode',
        component: LayoutComponent,
        data: { layout: 'empty' },
        canActivate: [NoAuthGuard],
        children: [{ path: '', component: AuthPartnerSignUpComponent }]
    },
    {
        path: 'reset-password/:passwordRecoveryCode',
        component: LayoutComponent,
        canActivate: [NoAuthGuard],
        data: { layout: 'empty' },
        children: [{ path: '', component: AuthResetPasswordComponent }]
    },
    {
        path: 'reset-password-success',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [{ path: '', component: AuthResetPasswordSuccessComponent }]
    },
    {
        path: 'sign-out',
        component: LayoutComponent,
        data: { layout: 'empty' },
        children: [{ path: '', component: AuthSignOutComponent }]
    },

    // TODO change-password
    // TODO verify
];

@NgModule({
    declarations: [
        ...COMPONENTS,
        ...DIRECTIVES,
    ],
    imports: [
        RouterModule.forChild(AUTH_ROUTES),
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        FuseModule,
        MaterialModule,
        SharedModule,
        TranslationModule,
        NgxParticlesModule,
    ],
    providers: [
        AuthService,
        UserService,
        provideHttpClient(withInterceptors([authInterceptor])),
        provideEnvironmentInitializer(() => inject(AuthService)),
    ],
    exports: [
        ...COMPONENTS,
    ],
})
export class AuthModule { }
