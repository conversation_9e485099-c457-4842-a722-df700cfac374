import { ChangeDetectorRef, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { NgForm, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { fuseAnimations } from '../../../fuse/animations/animations';
import { FuseAlertType } from '../../../fuse/components/alert/types/alert.types';
import { TranslatableComponent } from '../../../translation/types/translatable-component';
import { LanguageCode } from '../../../../modules/api/enums/language-code';
import { ApiService } from '../../../../modules/api/providers/api.service';
import { TranslationService } from '../../../translation/providers/translation.service';
import { UnitSetting } from '../../../../modules/api/types/unitsetting';
import { User, UserInput } from '../../../../modules/api/entities/user';

@Component({
    selector: 'auth-sign-up',
    templateUrl: './sign-up.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: false,
})
export class AuthSignUpComponent extends TranslatableComponent implements OnInit {
    @ViewChild('signUpNgForm') signUpNgForm: NgForm | null = null;

    alert: { type: FuseAlertType; message: string } = {
        type: 'success',
        message: '',
    };

    createdUser: Partial<User> | null = null;
    isCustomerCreatSucceeded: boolean = false;

    signUpForm: UntypedFormGroup | null = null;
    showAlert: boolean = false;

    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
        private apiService: ApiService,
        private formBuilder: UntypedFormBuilder,
        private router: Router
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void {
        this.signUpForm = this.formBuilder.group({
            name: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            password: ['', Validators.required],
            company: [''],
            agreements: ['', Validators.requiredTrue],
        });
    }

    createForm(): void {
        this.signUpForm = this.formBuilder.group({
            name: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            password: ['', Validators.required],
            company: [''],
            agreements: ['', Validators.requiredTrue],
        });
    }

    async signUp(): Promise<void> {
        if (this.signUpForm?.invalid || this.signUpForm?.disabled) {
            this.translateAsPromise('Please fill in all required fields and try again.').then(translation => {
                this.alert = {
                    type: 'error',
                    message: translation,
                };
                this.showAlert = true;
                setTimeout(() => {
                    this.showAlert = false;
                }, 3000);
            });
            return;
        }

        try {
            const token = await (window as any).executeRecaptcha('signup');

            this.signUpForm?.disable();

            const formValue = { ...this.signUpForm?.value };

            // Split the name into firstName and lastName
            const fullNameParts = (formValue.name || '').trim().split(' ');
            const firstName = fullNameParts[0] || '';
            const lastName = fullNameParts.slice(1).join(' ') || '';

            const userInput: UserInput = {
                firstName,
                lastName,
                email: formValue.email,
                password: formValue.password,
                languageCode: LanguageCode.de,
                avatarPath: '',
                phoneNumber: '',
                settings: {
                    unitSetting: UnitSetting.Meters,
                },
                isPartner: false,
            };

            await this.apiService.signUp(userInput,token)
                .then(async (user: Partial<User> | null) => {
                    if (!user) {
                        this.signUpForm?.enable();
                        this.signUpNgForm?.resetForm();
                        return;
                    }

                this.createdUser = user;

                this.isCustomerCreatSucceeded = true;

                if (this.isCustomerCreatSucceeded) {
                    this.router.navigateByUrl('/confirmation-required');
                } else {
                    this.signUpForm?.enable();
                    this.signUpNgForm?.resetForm();

                    this.translateAsPromise('Sign up failed. Please try again.').then(translation => {
                        this.alert = {
                            type: 'error',
                            message: translation,
                        };
                        this.showAlert = true;
                        setTimeout(() => {
                            this.showAlert = false;
                        }, 3000);
                    });
                    return;
                }
            });
        } catch (error) {
            this.translateAsPromise('Failed to verify reCAPTCHA. Please try again.').then(translation => {
                this.alert = {
                    type: 'error',
                    message: translation,
                };
                this.showAlert = true;
                setTimeout(() => {
                    this.showAlert = false;
                }, 3000);
            });

            this.signUpForm?.enable();
        }
    }
}
