import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { fuseAnimations } from 'src/app/modules/fuse/animations/animations';
import { TranslationService } from 'src/app/modules/translation/providers/translation.service';
import { TranslatableComponent } from 'src/app/modules/translation/types/translatable-component';

@Component({
    selector: 'auth-header',
    templateUrl: './header.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: false,
})
export class HeaderComponent extends TranslatableComponent implements OnInit {
    isMenuOpen: boolean = false;

    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void { }

    toggleMenu(): void {
        this.isMenuOpen = !this.isMenuOpen;
    }
}
