<header class="dark w-full z-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
            <!-- Logo -->
            <div class="flex items-center">
                <a routerLink="/" class="flex items-center">
                    <img src="assets/images/logo/logo-text-on-dark.svg" alt="Fuse" class="h-8 hidden sm:block" />
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex items-center space-x-8">
                <a routerLink="/" routerLinkActive="text-primary-400" [routerLinkActiveOptions]="{exact: true}"
                    class="text-gray-300 hover:text-primary-400 transition-colors duration-200 font-medium">
                    Home
                </a>
                <a routerLink="/features" routerLinkActive="text-primary-400"
                    class="text-gray-300 hover:text-primary-400 transition-colors duration-200 font-medium">
                    Features
                </a>
                <a routerLink="/pricing" routerLinkActive="text-primary-400"
                    class="text-gray-300 hover:text-primary-400 transition-colors duration-200 font-medium">
                    Pricing
                </a>
                <a routerLink="/faq" routerLinkActive="text-primary-400"
                    class="text-gray-300 hover:text-primary-400 transition-colors duration-200 font-medium">
                    FAQ
                </a>
                <a routerLink="/sign-in"
                    class="bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                    Sign In
                </a>
            </nav>

            <!-- Mobile Menu Button -->
            <button (click)="toggleMenu()" class="md:hidden text-gray-300 focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    @if (!isMenuOpen) {
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    }
                    @if (isMenuOpen) {
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    }
                </svg>
            </button>
        </div>

        <!-- Mobile Navigation -->
        @if (isMenuOpen) {
        <div class="md:hidden">
            <div class="px-2 pt-2 pb-4 space-y-1 sm:px-3">
                <a routerLink="/" routerLinkActive="bg-primary-500 text-white" [routerLinkActiveOptions]="{exact: true}"
                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-primary-500 hover:text-white transition-colors duration-200">
                    Home
                </a>
                <a routerLink="/features" routerLinkActive="bg-primary-500 text-white"
                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-primary-500 hover:text-white transition-colors duration-200">
                    Features
                </a>
                <a routerLink="/pricing" routerLinkActive="bg-primary-500 text-white"
                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-primary-500 hover:text-white transition-colors duration-200">
                    Pricing
                </a>
                <a routerLink="/faq" routerLinkActive="bg-primary-500 text-white"
                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-primary-500 hover:text-white transition-colors duration-200">
                    FAQ
                </a>
                <a routerLink="/sign-in"
                    class="block px-3 py-2 rounded-md text-base font-medium bg-primary-500 text-white hover:bg-primary-600 transition-colors duration-200">
                    Sign In
                </a>
            </div>
        </div>
        }
    </div>
</header>
