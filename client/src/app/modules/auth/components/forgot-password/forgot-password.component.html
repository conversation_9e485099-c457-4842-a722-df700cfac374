<div class="flex min-w-0 flex-auto flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start">
    <div class="dark w-full min-h-screen px-4 py-8 sm:bg-card sm:w-auto sm:rounded-2xl sm:p-12 sm:shadow md:flex md:h-full md:w-1/2 md:items-center md:justify-end md:rounded-none md:p-16 md:shadow-none">
        <div class="mx-auto w-full max-w-80 sm:mx-0 sm:w-80">
            <!-- Logo -->
            <div class="w-12">
                <img src="assets/images/logo/logo.svg" />
            </div>

            <!-- Title -->
            <div
                class="mt-8 text-4xl font-extrabold leading-tight tracking-tight"
            >
                {{ "Forgot password?" | translate: 'auth/forgot-password' | async }}
               
            </div>
            <div class="mt-0.5 font-medium">
                {{ "Fill the form to reset your password" | translate: 'auth/forgot-password' | async }}
            </div>

            <!-- Alert -->
            @if (showAlert) {
                <fuse-alert
                    class="mt-8"
                    [appearance]="'outline'"
                    [showIcon]="false"
                    [type]="alert.type"
                    [@fadeIn]="showAlert"
                    [@fadeOut]="!showAlert"
                    [@shake]="alert.type === 'error'"
                >
                    {{ alert.message }}
                </fuse-alert>
            }

            <!-- Forgot password form -->
            @if (forgotPasswordForm) {
                <form
                    class="mt-8"
                    [formGroup]="forgotPasswordForm"
                    #forgotPasswordNgForm="ngForm"
                >
                    <!-- Email field -->
                    <mat-form-field class="w-full">
                        <mat-label>{{ "Email address" | translate: 'auth/forgot-password' | async }}</mat-label>
                        <input id="email" matInput [formControlName]="'email'" />
                        @if (forgotPasswordForm.get('email')?.hasError('required')) {
                            <mat-error>
                                {{ "Email address is required" | translate: 'auth/forgot-password' | async }}
                            </mat-error>
                        }
                        @if (forgotPasswordForm.get('email')?.hasError('email')) {
                            <mat-error>
                                {{ "Please enter a valid email address" | translate: 'auth/forgot-password' | async }}
                            </mat-error>
                        }
                    </mat-form-field>

                    <!-- Submit button -->
                    <button
                        class="fuse-mat-button-large mt-3 w-full"
                        mat-flat-button
                        [color]="'primary'"
                        [disabled]="forgotPasswordForm.disabled"
                        (click)="sendResetLink()"
                    >
                        @if (!forgotPasswordForm.disabled) {
                            <span>{{ "Send reset link" | translate: 'auth/forgot-password' | async }}</span>
                        }
                        @if (forgotPasswordForm.disabled) {
                            <mat-progress-spinner
                                [diameter]="24"
                                [mode]="'indeterminate'"
                            ></mat-progress-spinner>
                        }
                    </button>

                    <!-- Form footer -->
                    <div class="text-secondary mt-8 text-md font-medium">
                        <span>{{ "Return to" | translate: 'auth/forgot-password' | async }}</span>
                        <a
                            class="ml-1 text-primary-500 hover:underline"
                            [routerLink]="['/sign-in']"
                            >
                            {{ "Login" | translate: 'auth/forgot-password' | async }}
                        </a>
                    </div>
                </form>
            }
        </div>
    </div>

    <auth-teaser class="w-1/2 h-full hidden flex-auto md:flex"></auth-teaser>
</div>
