import { ChangeDetectorR<PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { Engine } from '@tsparticles/engine';
import { loadSlim } from '@tsparticles/slim';
import { Subject, finalize, takeUntil, takeWhile, tap, timer } from 'rxjs';
import { IParticlesProps, NgParticlesService } from '@tsparticles/angular';
import { PARTICLE_OPTIONS } from '../../constants/particle.options';
import { TranslatableComponent } from '../../../translation/types/translatable-component';
import { AuthService } from '../../providers/auth.service';
import { TranslationService } from '../../../translation/providers/translation.service';

@Component({
    selector: 'auth-sign-out',
    templateUrl: './sign-out.component.html',
    styleUrls: ['./sign-out.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false,
})
export class AuthSignOutComponent extends TranslatableComponent implements OnInit, OnDestroy {
    countdown: number = 5;
    countdownMapping: any = {
        '=1': '# second',
        other: '# seconds',
    };
    private unsubscribeAll: Subject<any> = new Subject<any>();

    particlesOptions: IParticlesProps = PARTICLE_OPTIONS;

    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
        private authService: AuthService,
        private ngParticlesService: NgParticlesService,
        private router: Router
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void {
        this.authService.signOut();

        this.ngParticlesService.init(async (engine: Engine) => {
            await loadSlim(engine);
        });

        timer(1000, 1000)
            .pipe(
                finalize(() => {
                    this.router.navigate(['sign-in']);
                }),
                takeWhile(() => this.countdown > 0),
                takeUntil(this.unsubscribeAll),
                tap(() => this.countdown--)
            )
            .subscribe();
    }

    ngOnDestroy(): void {
        this.unsubscribeAll.next(null);
        this.unsubscribeAll.complete();
    }
}
