<div class="flex min-w-0 flex-auto flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start">
    <div class="dark w-full min-h-screen px-4 py-8 sm:bg-card sm:w-auto sm:rounded-2xl sm:p-12 sm:shadow md:flex md:h-full md:w-1/2 md:items-center md:justify-end md:rounded-none md:p-16 md:shadow-none">
        <div class="mx-auto w-full max-w-80 sm:mx-0 sm:w-80">
            <!-- Logo -->
            <div class="w-12">
                <img src="assets/images/logo/logo.svg" />
            </div>

            <!-- Title -->
            <div
                class="mt-8 text-4xl font-extrabold leading-tight tracking-tight"
            >
                {{ "Sign in" | translate: 'auth/sign-in' | async }}
            </div>
            <div class="mt-0.5 flex items-baseline font-medium">
                <div>{{ "Don't have an account?" | translate: 'auth/sign-in' | async }}</div>
                <a
                    class="ml-1 text-primary-500 hover:underline"
                    [routerLink]="['/sign-up']"
                    >
                    {{ "Sign up" | translate: 'auth/sign-in' | async }}
                </a>
            </div>

            <!-- Alert -->
            @if (showAlert) {
                <fuse-alert
                    class="mt-8"
                    [appearance]="'outline'"
                    [showIcon]="false"
                    [type]="alert.type"
                    [@shake]="alert.type === 'error'"
                >
                    {{ alert.message }}
                </fuse-alert>
            }

            <!-- Sign in form -->
            @if (signInForm) {
                <form class="mt-8" [formGroup]="signInForm" #signInNgForm="ngForm">
                    <!-- Email field -->
                    <mat-form-field class="w-full">
                        <mat-label>{{ "Email address" | translate: 'auth/sign-in' | async }}</mat-label>
                        <input id="email" matInput [formControlName]="'email'" />
                        @if (signInForm.get('email')?.hasError('required')) {
                            <mat-error>
                                {{ "Email address is required" | translate: 'auth/sign-in' | async }}
                            </mat-error>
                        }
                        @if (signInForm.get('email')?.hasError('email')) {
                            <mat-error>
                                {{ "Please enter a valid email address" | translate: 'auth/sign-in' | async }}
                            </mat-error>
                        }
                    </mat-form-field>

                    <!-- Password field -->
                    <mat-form-field class="w-full">
                        <mat-label>{{ "Password" | translate: 'auth/sign-in' | async }}</mat-label>
                        <input
                            id="password"
                            matInput
                            type="password"
                            [formControlName]="'password'"
                            #passwordField
                        />
                        <button
                            mat-icon-button
                            type="button"
                            (click)="
                                passwordField.type === 'password'
                                    ? (passwordField.type = 'text')
                                    : (passwordField.type = 'password')
                            "
                            matSuffix
                        >
                            @if (passwordField.type === 'password') {
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:eye'"
                                ></mat-icon>
                            }
                            @if (passwordField.type === 'text') {
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:eye-slash'"
                                ></mat-icon>
                            }
                        </button>
                        <mat-error>
                            {{ "Password is required" | translate: 'auth/sign-in' | async }}
                        </mat-error>
                    </mat-form-field>

                    <!-- Submit button -->
                    <button
                        class="mt-1.5 fuse-mat-button-large w-full"
                        mat-flat-button
                        [color]="'primary'"
                        [disabled]="signInForm.disabled"
                        (click)="signIn()"
                    >
                        @if (!signInForm.disabled) {
                            <span>{{ "Sign in" | translate: 'auth/sign-in' | async }}</span>
                        }
                        @if (signInForm.disabled) {
                            <mat-progress-spinner
                                [diameter]="24"
                                [mode]="'indeterminate'"
                            ></mat-progress-spinner>
                        }
                    </button>

                    <!-- Actions -->
                    <div
                        class="mt-6 inline-flex w-full items-center justify-center"
                    >
                        <a
                            class="text-md font-medium text-primary-500 hover:underline"
                            [routerLink]="['/forgot-password']"
                            >
                            {{ "Forgot password?" | translate: 'auth/sign-in' | async }}
                        </a>
                    </div>

                    <!-- Separator -->
                    <!-- <div class="mt-8 flex items-center">
                        <div class="mt-px flex-auto border-t"></div>
                        <div class="text-secondary mx-2">
                            {{ "Or continue with" | translate: 'auth/sign-in' | async }}
                        </div>
                        <div class="mt-px flex-auto border-t"></div>
                    </div> -->

                    <!-- Single sign-on buttons -->
                    <!-- <div class="mt-8 flex items-center space-x-4">
                        <button class="flex-auto" type="button" mat-stroked-button>
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'feather:facebook'"
                            ></mat-icon>
                        </button>
                        <button class="flex-auto" type="button" mat-stroked-button>
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'feather:twitter'"
                            ></mat-icon>
                        </button>
                        <button class="flex-auto" type="button" mat-stroked-button>
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'feather:github'"
                            ></mat-icon>
                        </button>
                    </div> -->
                </form>
            }
        </div>
    </div>
    <auth-teaser></auth-teaser>
</div>