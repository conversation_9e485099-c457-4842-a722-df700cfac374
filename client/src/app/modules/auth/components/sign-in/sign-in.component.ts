import { ChangeDetectorRef, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormGroup, NgForm, UntypedFormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { fuseAnimations } from '../../../fuse/animations/animations';
import { FuseAlertType } from '../../../fuse/components/alert/types/alert.types';
import { TranslatableComponent } from '../../../translation/types/translatable-component';
import { ApiService } from '../../../api/providers/api.service';
import { TranslationService } from '../../../translation/providers/translation.service';
import { User } from '../../../api/entities/user';

@Component({
    selector: 'auth-sign-in',
    templateUrl: './sign-in.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: false,
})
export class AuthSignInComponent extends TranslatableComponent implements OnInit {
    @ViewChild('signInNgForm') signInNgForm: NgForm | null = null;
   

    alert: { type: FuseAlertType; message: string } = {
        type: 'success',
        message: '',
    };
    signInForm!: FormGroup;
    showAlert: boolean = false;

    loggedInUser: Partial<User> | null = null;

    partnerInviteForm!: FormGroup;
    
    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
        private activatedRoute: ActivatedRoute,
        private apiService: ApiService,
        private formBuilder: UntypedFormBuilder,
        private router: Router,
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void {
        this.createForm();
    }

    createForm(): void {
		this.signInForm = this.formBuilder.group({
			email: ['', [Validators.required, Validators.email]],
			password: ['', Validators.required],
		});
	}

    async signIn(): Promise<void> {
        if (!this.signInNgForm) {
            return;
        }
        
		if (this.signInForm.invalid) {
            return;
        }

        this.signInForm.disable();
        this.showAlert = false;

        this.apiService.logIn(this.signInForm.value['email'], this.signInForm.value['password']).then((user: Partial<User> | null) => {
            if (!user && this.signInNgForm) {
                this.signInForm.enable();
                this.signInNgForm.resetForm();

                this.translate('LOGIN_ERROR', 'auth/sign-in').subscribe((translation: string) => {
                    this.alert = {
                        type: 'error',
                        message: translation,
                    };
                    this.showAlert = true;
                    setTimeout(() => {
                        this.showAlert = false;
                    }, 3000);
                });

                return;
            }

            const targetRoute: string = '/signed-in-redirect';
            const redirectURL = this.activatedRoute.snapshot.queryParamMap.get('redirectURL') || targetRoute;
            
            console.log(redirectURL);

            setTimeout(() => {
                this.router.navigate([redirectURL]);
            }, 300);
        });
	}
}
