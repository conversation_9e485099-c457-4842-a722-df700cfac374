import { ChangeDetectorRef, Component, ViewEncapsulation } from '@angular/core';
import { fuseAnimations } from '../../../fuse/animations/animations';
import { TranslatableComponent } from '../../../translation/types/translatable-component';
import { TranslationService } from '../../../translation/providers/translation.service';

@Component({
    selector: 'auth-confirmation-required',
    templateUrl: './confirmation-required.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: false,
})
export class AuthConfirmationRequiredComponent extends TranslatableComponent {
    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector);
    }
}
