<!-- Button -->
<button mat-icon-button [matMenuTriggerFor]="userActions">
    <span class="relative">
        @if (showAvatar && user && user.avatarPath) {
            <img class="h-7 w-7 rounded-full" [src]="user.avatarPath" />
        }
        @if (!showAvatar || !user || !user.avatarPath) {
            <mat-icon [svgIcon]="'heroicons_outline:user-circle'"></mat-icon>
        }
        @if (user) {
            <span
                class="absolute bottom-1 right-0 h-2 w-2.25 rounded-full z-999 opacity-80"
                [ngClass]="{
                    'mb-px mr-px': !showAvatar || !user.avatarPath,
                    'bg-green-500': user.status === 'online',
                    'bg-amber-500': user.status === 'away',
                    'bg-red-500': user.status === 'busy',
                    'bg-gray-400': user.status === 'not-visible',
                }"
            ></span>
        }
    </span>
</button>

<mat-menu [xPosition]="'before'" #userActions="matMenu">
    <button mat-menu-item>
        <span class="flex flex-col leading-none">
            <span>{{ 'Signed in as' | translate: 'components/user' | async }}</span>
            <span class="mt-1.5 text-md font-medium">{{ user?.email }}</span>
        </span>
    </button>
    <mat-divider class="my-2"></mat-divider>
    <button mat-menu-item [routerLink]="['/profile']">
        <mat-icon [svgIcon]="'heroicons_outline:user-circle'"></mat-icon>
        <span>{{ 'Profile' | translate: 'components/user' | async }}</span>
    </button>
    <button mat-menu-item [routerLink]="['/settings']">
        <mat-icon [svgIcon]="'heroicons_outline:cog-8-tooth'"></mat-icon>
        <span>{{ 'Preferences' | translate: 'components/user' | async }}</span>
    </button>
    <button mat-menu-item [matMenuTriggerFor]="userStatus">
        <mat-icon
            [svgIcon]="'heroicons_outline:ellipsis-horizontal-circle'"
        ></mat-icon>
        <span>{{ 'Status' | translate: 'components/user' | async }}</span>
    </button>
    <mat-divider class="my-2"></mat-divider>
    <button mat-menu-item (click)="signOut()">
        <mat-icon
            [svgIcon]="'heroicons_outline:arrow-right-on-rectangle'"
        ></mat-icon>
        <span>{{ 'Sign out' | translate: 'components/user' | async }}</span>
    </button>
</mat-menu>

<mat-menu class="user-status-menu" #userStatus="matMenu">
    <button mat-menu-item (click)="updateUserStatus('online')">
        <span class="mr-3 inline-flex h-4 w-4 rounded-full bg-green-500"></span>
        <span>{{ 'Online' | translate: 'components/user' | async }}</span>
    </button>
    <button mat-menu-item (click)="updateUserStatus('away')">
        <span class="mr-3 inline-flex h-4 w-4 rounded-full bg-amber-500"></span>
        <span>{{ 'Away' | translate: 'components/user' | async }}</span>
    </button>
    <button mat-menu-item (click)="updateUserStatus('busy')">
        <span class="mr-3 inline-flex h-4 w-4 rounded-full bg-red-500"></span>
        <span>{{ 'Busy' | translate: 'components/user' | async }}</span>
    </button>
    <button mat-menu-item (click)="updateUserStatus('not-visible')">
        <span class="mr-3 inline-flex h-4 w-4 rounded-full bg-gray-400"></span>
        <span>{{ 'Invisible' | translate: 'components/user' | async }}</span>
    </button>
</mat-menu>
