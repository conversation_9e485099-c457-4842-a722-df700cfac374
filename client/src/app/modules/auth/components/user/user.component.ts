import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { TranslatableComponent } from '../../../translation/types/translatable-component';
import { TranslationService } from '../../../translation/providers/translation.service';
import { UserService } from '../../providers/user.service';
import { ApiService } from '../../../../modules/api/providers/api.service';
import { UserStatus } from '../../types/user-status';
import { User } from '../../../../modules/api/entities/user';

@Component({
    selector: 'auth-user',
    templateUrl: './user.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false,
})
export class UserComponent extends TranslatableComponent implements OnInit, OnD<PERSON>roy {
    @Input() showAvatar: boolean = true;
    user: Partial<User> | null = null;

    private unsubscribeAll: Subject<any> = new Subject<any>();

    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
        private router: Router,
        private apiService: ApiService,
        private userService: UserService,
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void {
        // Subscribe to user changes
        this.apiService.whoAmI()
            .then((user: Partial<User> | null) => {
                this.user = user;
                this.changeDetector.markForCheck();
            });
    }

    updateUserStatus(status: UserStatus): void { // TODO: user status is not implemented yet
        // Return if user is not available
        if (!this.user) {
            return;
        }

        // Update the user
        this.userService
            .update(this.user as User)
            .subscribe();
    }

    signOut(): void {
        this.apiService.logOut();
        this.router.navigate(['/sign-out']);
    }

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this.unsubscribeAll.next(null);
        this.unsubscribeAll.complete();
    }
}
