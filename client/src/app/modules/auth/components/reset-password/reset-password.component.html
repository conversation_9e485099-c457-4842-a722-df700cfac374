<div class="flex min-w-0 flex-auto flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start">
    <div class="dark w-full min-h-screen px-4 py-8 sm:bg-card sm:w-auto sm:rounded-2xl sm:p-12 sm:shadow md:flex md:h-full md:w-1/2 md:items-center md:justify-end md:rounded-none md:p-16 md:shadow-none">
        <div class="mx-auto w-full max-w-80 sm:mx-0 sm:w-80">
            <!-- Logo -->
            <div class="w-12">
                <img src="assets/images/logo/logo.svg" />
            </div>

            <!-- Title -->
            <div
                class="mt-8 text-4xl font-extrabold leading-tight tracking-tight"
            >
                {{ "Reset your password" | translate: 'auth/reset-password' | async }}
            </div>
            <div class="mt-0.5 font-medium">
                {{ "Create a new password for your account" | translate: 'auth/reset-password' | async }}
            </div>

            <!-- Alert -->
            @if (showAlert) {
                <fuse-alert
                    class="mt-8"
                    [appearance]="'outline'"
                    [showIcon]="false"
                    [type]="alert.type"
                    [@shake]="alert.type === 'error'"
                >
                    {{ alert.message }}
                </fuse-alert>
            }

            <!-- Reset password form -->
            @if (resetPasswordForm) {
                <form
                    class="mt-8"
                    [formGroup]="resetPasswordForm"
                    #resetPasswordNgForm="ngForm"
                >
                    <!-- Password field -->
                    <mat-form-field class="w-full">
                        <mat-label>{{ "Password" | translate: 'auth/reset-password' | async }}</mat-label>
                        <input
                            id="password"
                            matInput
                            type="password"
                            [formControlName]="'password'"
                            #passwordField
                        />
                        <button
                            mat-icon-button
                            type="button"
                            (click)="
                                passwordField.type === 'password'
                                    ? (passwordField.type = 'text')
                                    : (passwordField.type = 'password')
                            "
                            matSuffix
                        >
                            @if (passwordField.type === 'password') {
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:eye'"
                                ></mat-icon>
                            }
                            @if (passwordField.type === 'text') {
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:eye-slash'"
                                ></mat-icon>
                            }
                        </button>
                        <mat-error>
                            {{ "Password is required" | translate: 'auth/reset-password' | async }}
                        </mat-error>
                    </mat-form-field>

                    <!-- Password confirm field -->
                    <mat-form-field class="w-full">
                        <mat-label>{{ "Password Confirmation" | translate: 'auth/reset-password' | async }}</mat-label>
                        <input
                            id="password-confirm"
                            matInput
                            type="password"
                            [formControlName]="'passwordConfirm'"
                            #passwordConfirmField
                        />
                        <button
                            mat-icon-button
                            type="button"
                            (click)="
                                passwordConfirmField.type === 'password'
                                    ? (passwordConfirmField.type = 'text')
                                    : (passwordConfirmField.type = 'password')
                            "
                            matSuffix
                        >
                            @if (passwordConfirmField.type === 'password') {
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:eye'"
                                ></mat-icon>
                            }
                            @if (passwordConfirmField.type === 'text') {
                                <mat-icon
                                    class="icon-size-5"
                                    [svgIcon]="'heroicons_solid:eye-slash'"
                                ></mat-icon>
                            }
                        </button>
                        @if (resetPasswordForm.get('passwordConfirm')?.hasError('required')) {
                            <mat-error>
                                {{ "Password confirmation is required" | translate: 'auth/reset-password' | async }}
                            </mat-error>
                        }
                        @if (resetPasswordForm.get('passwordConfirm')?.hasError('mustMatch')) {
                            <mat-error>
                                {{ "Passwords must match" | translate: 'auth/reset-password' | async }}
                            </mat-error>
                        }
                    </mat-form-field>

                    <!-- Submit button -->
                    <button
                        class="fuse-mat-button-large mt-3 w-full"
                        mat-flat-button
                        [color]="'primary'"
                        [disabled]="resetPasswordForm.disabled"
                        (click)="resetPassword()"
                    >
                        @if (!resetPasswordForm.disabled) {
                            <span>{{ "Reset your password" | translate: 'auth/reset-password' | async }}</span>
                        }
                        @if (resetPasswordForm.disabled) {
                            <mat-progress-spinner
                                [diameter]="24"
                                [mode]="'indeterminate'"
                            ></mat-progress-spinner>
                        }
                    </button>

                    <!-- Form footer -->
                    <div class="text-secondary mt-8 text-md font-medium">
                        <span>{{ "Return to" | translate: 'auth/reset-password' | async }}</span>
                        <a
                            class="ml-1 text-primary-500 hover:underline"
                            [routerLink]="['/sign-in']"
                            >
                            {{ "Sign in" | translate: 'auth/reset-password' | async }}
                        </a>
                    </div>
                </form>
            }
        </div>
    </div>
    
    <auth-teaser class="w-1/2 h-full hidden flex-auto md:flex"></auth-teaser>
</div>
