import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { NgForm, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { fuseAnimations } from '../../../fuse/animations/animations';
import { FuseAlertType } from '../../../fuse/components/alert/types/alert.types';
import { FuseValidators } from '../../../../modules/fuse/validators/validators';
import { TranslatableComponent } from '../../../translation/types/translatable-component';
import { ApiService } from '../../../../modules/api/providers/api.service';
import { TranslationService } from '../../../translation/providers/translation.service';
import { User } from '../../../../modules/api/entities/user';


@Component({
    selector: 'auth-reset-password',
    templateUrl: './reset-password.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: false,
})
export class AuthResetPasswordComponent extends TranslatableComponent implements OnInit {
    @ViewChild('resetPasswordNgForm') resetPasswordNgForm: NgForm | null = null;

    alert: { type: FuseAlertType; message: string } = {
        type: 'success',
        message: '',
    };

    private passwordRecoveryCode: string = '';
    resetPasswordForm: UntypedFormGroup | null = null;
    showAlert: boolean = false;

    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
        private apiService: ApiService,
        private formBuilder: UntypedFormBuilder,
        private route: ActivatedRoute,
        private router: Router,
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void {
        this.createForm();

        this.route.paramMap.subscribe((params: ParamMap) => {
            const passwordRecoveryCode = params.get('passwordRecoveryCode');
            if (passwordRecoveryCode) {
                this.passwordRecoveryCode = passwordRecoveryCode;
            }
        });
    }

    createForm(): void {
        this.resetPasswordForm = this.formBuilder.group({
            password: ['', Validators.required],
            passwordConfirm: ['', Validators.required]
        }, {
            validators: FuseValidators.mustMatch('password', 'passwordConfirm')
        });
    }

    resetPassword(): void {
        if (!this.resetPasswordForm) {
            return;
        }

        this.resetPasswordForm?.disable();

        if (this.resetPasswordForm.get('password')?.value !== this.resetPasswordForm.get('passwordConfirm')?.value) {
            this.translateAsPromise('Passwords must match', 'auth/reset-password').then(translation => {
                this.alert = {
                    type: 'error',
                    message: translation,
                };
                this.showAlert = true;
                setTimeout(() => {
                    this.showAlert = false;
                }, 4000);
            });

            this.resetPasswordForm?.enable();

            return;
        }

        if (this.resetPasswordForm?.invalid) {
            this.translateAsPromise('Password reset failed. Please try again.', 'auth/reset-password').then(translation => {
                this.alert = {
                    type: 'error',
                    message: translation,
                };
                this.showAlert = true;
                setTimeout(() => {
                    this.showAlert = false;
                }, 4000);
            });

            this.resetPasswordForm?.enable();
            this.resetPasswordForm?.reset();
            return;
        }

        this.apiService.changePassword(this.resetPasswordForm?.get('password')?.value, this.passwordRecoveryCode).then((user: Partial<User> | null) => {
            if (!user) {
                this.translateAsPromise('Password reset failed. Please try again.', 'auth/reset-password').then(translation => {
                    this.alert = {
                        type: 'error',
                        message: translation,
                    };
                    this.showAlert = true;
                    setTimeout(() => {
                        this.showAlert = false;
                    }, 3000);
                });
                return;
            }

            setTimeout(() => {
                this.router.navigate(['/reset-password-success']);
            }, 300);
        });
    }
}
