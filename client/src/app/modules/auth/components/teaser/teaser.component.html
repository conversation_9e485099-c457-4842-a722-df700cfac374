<div class="absolute z-99 w-full h-full flex-auto items-center justify-center overflow-hidden p-16 dark:border-l lg:px-28 bg-gradient-to-b from-sky-900/50 to-neutral-900/50">    
    <!-- Content -->
    <div class="relative top-[33%] max-w-2xl animate-fade-in-1000">
        <div class="text-7xl font-bold leading-none text-gray-100">
            <div>
                {{ "Welcome to" | translate: 'auth/teaser' | async }}
            </div>
            <div>
                {{ "our community" | translate: 'auth/teaser' | async }}
            </div>
        </div>
        <div class="mt-6 text-lg leading-6 tracking-tight text-gray-400">
            {{ "TEASER_TEXT" | translate: 'auth/teaser' | async }}
        </div>
        <div class="mt-8 flex items-center">
            <div class="flex flex-0 items-center -space-x-1.5">
                <img
                    class="h-10 w-10 flex-0 rounded-full object-cover animate-fade-in-3000 ring-2 ring-gray-800/50 ring-offset-1 ring-offset-gray-800"
                    src="assets/images/avatars/female-18.jpg"
                />
                <img
                    class="h-10 w-10 flex-0 rounded-full object-cover animate-fade-in-4000 ring-2 ring-gray-800/50 ring-offset-1 ring-offset-gray-800"
                    src="assets/images/avatars/female-11.jpg"
                />
                <img
                    class="h-10 w-10 flex-0 rounded-full object-cover animate-fade-in-5000 ring-2 ring-gray-800/50 ring-offset-1 ring-offset-gray-800"
                    src="assets/images/avatars/male-09.jpg"
                />
                <img
                    class="h-10 w-10 flex-0 rounded-full object-cover animate-fade-in-6000 ring-2 ring-gray-800/50 ring-offset-1 ring-offset-gray-800"
                    src="assets/images/avatars/male-16.jpg"
                />
            </div>
            <div class="ml-4 font-medium tracking-tight text-gray-400">
                {{ "More than 17k people joined us, it's your turn" | translate: 'auth/teaser' | async }}
               
            </div>
        </div>
    </div>
</div>

<!-- Particles -->
<div class="bg-black h-full w-full absolute -z-10 top-0 left-0 pointer-events-none">
    <ngx-particles
        id="ts-particles"
        class="animate-fade-in-5000"
        [options]="particlesOptions"
    ></ngx-particles>
</div>