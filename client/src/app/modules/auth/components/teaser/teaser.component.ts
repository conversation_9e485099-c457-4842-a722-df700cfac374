import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { IParticlesProps, NgParticlesService } from '@tsparticles/angular';
import { Engine } from '@tsparticles/engine';
import { loadSlim } from "@tsparticles/slim";
import { fuseAnimations } from '../../../fuse/animations/animations';
import { TranslatableComponent } from '../../../translation/types/translatable-component';
import { PARTICLE_OPTIONS } from '../../constants/particle.options';
import { TranslationService } from '../../../translation/providers/translation.service';

@Component({
    selector: 'auth-teaser',
    templateUrl: './teaser.component.html',
    styleUrls: ['./teaser.component.scss'],
    host: {
        class: 'w-1/2 h-full hidden flex-auto md:flex',
    },
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: false,
})
export class TeaserComponent extends TranslatableComponent implements OnInit {
    particlesOptions: IParticlesProps = PARTICLE_OPTIONS;

    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
        private ngParticlesService: NgParticlesService,
    ) {
        super(translationService, changeDetector);
    }

    async ngOnInit(): Promise<void> {
        this.ngParticlesService.init(async (engine: Engine) => {
            await loadSlim(engine);
        });
    }
}
