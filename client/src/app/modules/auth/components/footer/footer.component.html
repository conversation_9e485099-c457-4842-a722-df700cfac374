<footer class="min-w-full py-4 transition-colors duration-300 shadow-inner bg-base-900 mt-auto">
    <div class="container mx-auto">
        <div class="flex justify-between items-center">
            <div class="max-w-[47%] ml-[5%] text-x-dm-default transition-colors duration-300 select-none xl:text-md lg:text-sm mdh:text-sm md:text-sm sm:text-xs">
                TaskFuse ©, {{ 'All rights reserved' | translate: 'auth/footer' | async }} 
            </div>
            <div class="flex flex-row max-w-1/3 mr-[5%] transition-colors duration-300 select-none xl:text-base lg:text-md mdh:text-sm md:text-sm sm:text-sm">
                <a href="/imprint" target="_blank" class="text-x-dm-default xl:mr-4 lg:mr-4 mdh:mr-3 md:mr-3 sm:mr-2">{{ 'Imprint' | translate: 'auth/footer' | async }}</a>
                <span class="text-x-dm-default xl:mr-3 lg:mr-3 mdh:mr-3 md:mr-2 sm:mr-2">|</span>
                <a href="/terms-and-conditions" target="_blank" class="text-x-dm-default xl:mr-4 lg:mr-4 mdh:mr-3 md:mr-2 sm:mr-2">{{ 'Terms and Conditions' | translate: 'auth/footer' | async }}</a>
                <span class="text-x-dm-default xl:mr-3 lg:mr-3 mdh:mr-3 md:mr-2 sm:mr-2">|</span>
                <a href="/privacy-policy" target="_blank" class="text-x-dm-default xl:mr-3 lg:mr-3 mdh:mr-3 md:mr-2 sm:mr-2">{{ 'Privacy Policy' | translate: 'auth/footer' | async }}</a>
            </div>
        </div>
    </div>
</footer>
