import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { fuseAnimations } from '../../../../modules/fuse/animations/animations';
import { TranslatableComponent } from '../../../../modules/translation/types/translatable-component';
import { TranslationService } from '../../../../modules/translation/providers/translation.service';

@Component({
    selector: 'auth-footer',
    templateUrl: './footer.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: fuseAnimations,
    standalone: false,
})
export class FooterComponent extends TranslatableComponent implements OnInit {

    constructor(
        protected override changeDetector: ChangeDetectorRef,
        protected override translationService: TranslationService,
    ) {
        super(translationService, changeDetector);
    }

    ngOnInit(): void {
    }
}