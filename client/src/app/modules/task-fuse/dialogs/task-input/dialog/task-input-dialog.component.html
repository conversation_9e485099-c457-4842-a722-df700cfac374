<div class="relative flex flex-col w-full h-full">

    <!-- Dismiss button -->
    @if (data.dismissible) {
        <div class="absolute top-0 right-0 pt-4 pr-4">
            <button
                mat-icon-button
                [matDialogClose]="undefined">
                <mat-icon
                    class="text-secondary"
                    [svgIcon]="'heroicons_outline:x'"></mat-icon>
            </button>
        </div>
    }
   

    <!-- Content -->
    <div class="flex flex-col sm:flex-row flex-auto items-center sm:items-start p-8 pb-6 sm:pb-8">

        <!-- Icon -->
        @if (data.icon?.show) {
            <div
                class="flex flex-0 items-center justify-center w-10 h-10 sm:mr-4 rounded-full"
                [ngClass]="{
                    'text-primary-600 bg-primary-100 dark:text-primary-50 dark:bg-primary-600': data.icon?.color === 'primary',
                    'text-accent-600 bg-accent-100 dark:text-accent-50 dark:bg-accent-600': data.icon?.color === 'accent',
                    'text-warn-600 bg-warn-100 dark:text-warn-50 dark:bg-warn-600': data.icon?.color === 'warn',
                    'text-gray-600 bg-gray-100 dark:text-gray-50 dark:bg-gray-600': data.icon?.color === 'basic',
                    'text-blue-600 bg-blue-100 dark:text-blue-50 dark:bg-blue-600': data.icon?.color === 'info',
                    'text-green-500 bg-green-100 dark:text-green-50 dark:bg-green-500': data.icon?.color === 'success',
                    'text-amber-500 bg-amber-100 dark:text-amber-50 dark:bg-amber-500': data.icon?.color === 'warning',
                    'text-red-600 bg-red-100 dark:text-red-50 dark:bg-red-600': data.icon?.color === 'error'
                }"
            >
                <mat-icon
                    class="text-current"
                    [svgIcon]="data.icon?.name ?? ''">
                </mat-icon>
            </div>
        }

        @if (data) {
            <div class="grow w-full flex flex-col items-center sm:items-start mt-4 sm:mt-0 sm:pr-8 space-y-1 text-center sm:text-left">
                <!-- Title -->
                @if (data.title) {
                        <div 
                            class="text-xl leading-6 font-medium" 
                            [innerHTML]="data.title"
                        >
                        </div>
                }

                <!-- Message -->
                @if (data.message) {
                    <div
                        class="text-secondary"
                        [innerHTML]="data.message"
                    >
                    </div>
                }

                <!-- Input fields -->
                @if (allowInputText) {
                    <div class="pt-5 w-full">
                        <mat-form-field class="fuse-mat-no-subscript w-full">
                            <mat-label>{{ label }}</mat-label>
                            <input matInput class="w-full" [autofocus] [(ngModel)]="enteredText">
                        </mat-form-field>
                    </div>
                }
               
                <!-- Multiple input fields -->
                @if (multipleInputs) {
                    @for (inputText of inputTexts; track trackByFn($index, inputText); let i = $index) {
                        <div class="pt-5 w-full">
                            <mat-form-field class="fuse-mat-no-subscript w-full">
                                <mat-label>{{ labels[i] }}</mat-label>
                                <input matInput class="w-full" [autofocus]="i === 0" [(ngModel)]="inputTexts[i]">
                            </mat-form-field>
                        </div>
                    }
                }

                <!-- Description -->
                @if (allowDescription) {
                    <div class="pt-5 w-full">
                        <mat-label class="font-medium">{{ descriptionLabel }}</mat-label>
                        <div quillValueAccessor class="w-full" [(ngModel)]="enteredDescription"></div>
                    
                    </div>
                }

                <!-- Priority select -->
                @if (allowPrioritySelect) {
                    <div class="pt-5 w-full">
                        <mat-form-field class="fuse-mat-no-subscript w-full">
                            <mat-label>{{ prioritySelectLabel }}</mat-label>
                            @if (enteredPriority === '0') {
                                <mat-icon matPrefix class="mr-2 min-w-8 text-hint text-blue-500" [svgIcon]="'priorities:lowest'"></mat-icon>
                            }
                            @else if (enteredPriority === '1') {
                                <mat-icon matPrefix class="mr-2 min-w-8 text-hint text-cyan-500" [svgIcon]="'priorities:low'"></mat-icon>
                            }
                            @else if (enteredPriority === '2') {
                                <mat-icon matPrefix class="mr-2 min-w-8 text-hint text-slate-500" [svgIcon]="'priorities:middle'"></mat-icon>
                            }
                            @else if (enteredPriority === '3') {
                                <mat-icon matPrefix class="mr-2 min-w-8 text-hint text-amber-500" [svgIcon]="'priorities:high'"></mat-icon>
                            }
                            @else if (enteredPriority === '4') {
                                <mat-icon matPrefix class="mr-2 min-w-8 text-hint text-orange-500" [svgIcon]="'priorities:highest'"></mat-icon>
                            }
                            @else if (enteredPriority === '5') {
                                <mat-icon matPrefix class="mr-2 min-w-8 text-hint text-red-500" [svgIcon]="'priorities:critical'"></mat-icon>
                            }
                            
                            <mat-select [(ngModel)]="enteredPriority" class="">
                                <mat-option value="5"><mat-icon matPrefix class="mr-2 min-w-8 text-hint text-red-500" [svgIcon]="'priorities:critical'"></mat-icon>{{ 'PRIORITY_5' | translate: 'dialogs/task-input-dialog' | async }}</mat-option>
                                <mat-option value="4"><mat-icon matPrefix class="mr-2 min-w-8 text-hint text-orange-500" [svgIcon]="'priorities:highest'"></mat-icon>{{ 'PRIORITY_4' | translate: 'dialogs/task-input-dialog' | async }}</mat-option>
                                <mat-option value="3"><mat-icon matPrefix class="mr-2 min-w-8 text-primary text-amber-500" [svgIcon]="'priorities:high'"></mat-icon>{{ 'PRIORITY_3' | translate: 'dialogs/task-input-dialog' | async }}</mat-option>
                                <mat-option value="2"><mat-icon matPrefix class="mr-2 min-w-8 text-hint text-slate-500" [svgIcon]="'priorities:middle'"></mat-icon>{{ 'PRIORITY_2' | translate: 'dialogs/task-input-dialog' | async }}</mat-option>
                                <mat-option value="1"><mat-icon matPrefix class="mr-2 min-w-8 text-hint text-cyan-500" [svgIcon]="'priorities:low'"></mat-icon>{{ 'PRIORITY_1' | translate: 'dialogs/task-input-dialog' | async }}</mat-option>
                                <mat-option value="0"><mat-icon matPrefix class="mr-2 min-w-8 text-hint text-blue-500" [svgIcon]="'priorities:lowest'"></mat-icon>{{ 'PRIORITY_0' | translate: 'dialogs/task-input-dialog' | async }}</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                }

               <!-- Date time picker -->
               @if (allowDateTimePicker) {
                <div class="pt-5 w-full">
                    <mat-label class="font-medium">{{ dateTimePickerLabel }}</mat-label>
                    <div class="border border-gray-300 dark:border-gray-700 rounded-lg p-4 mt-2">
                        <!-- Date Picker -->
                        <mat-form-field class="fuse-mat-no-subscript w-full" floatLabel="always">
                            <mat-label>{{ datePickerLabel }}</mat-label>
                            <input
                                #dateInput
                                matInput
                                [matDatepicker]="datePicker"
                                [value]="dateValue"
                                (dateChange)="onDateChange($event.value)"
                                [min]="minDate.toJSDate()"
                                [max]="maxDate.toJSDate()"
                                [required]="!dateTimePickerIsNullable"
                                [placeholder]="('Choose date' | translate: 'dialogs/task-input-dialog' | async) ?? ''"
                            />
                            <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
                            <mat-datepicker #datePicker></mat-datepicker>
                        </mat-form-field>

                        <!-- Time Picker -->
                        <mat-form-field class="fuse-mat-no-subscript w-full mt-4" floatLabel="always">
                            <mat-label>{{ timePickerLabel }}</mat-label>
                            <input
                                matInput
                                [ngxMatTimepicker]="timePicker"
                                [(ngModel)]="enteredTime"
                                name="enteredTime"
                                (ngModelChange)="onTimeChange($event)"
                                (focus)="onDatePickerOpen()"
                                [format]="24"
                                [placeholder]="('Choose time' | translate: 'dialogs/task-input-dialog' | async) ?? ''"
                                [required]="!dateTimePickerIsNullable"
                            />
                            <ngx-mat-timepicker-toggle matSuffix [for]="timePicker"></ngx-mat-timepicker-toggle>
                            <ngx-mat-timepicker
                                #timePicker
                                [format]="24"
                                [minutesGap]="stepMinute"
                                [color]="'primary'"
                                (opened)="onTimePickerOpen()"
                            >
                            </ngx-mat-timepicker>
                        </mat-form-field>
                    </div>
                </div>
            }

                <!-- Color picker -->
                @if (allowColor) {
                    <div class="pt-5 w-full">
                        <mat-label class="font-medium">{{ colorLabel }}</mat-label>
                        <div class="w-full flex flex-col mt-1">
                            <div class="w-full flex flex-row">
                                <div class="w-8 h-8 ml-0 border-2 rounded-sm cursor-pointer hover:border-blue-600" (click)="setColor('#f44336')" [ngClass]="{ 'border-gray-600': enteredColorValue === '#f44336' }" [ngStyle]="{ 'background': '#f44336' }"></div>
                                <div class="w-8 h-8 ml-2 border-2 rounded-sm cursor-pointer hover:border-blue-600" (click)="setColor('#ff9800')" [ngClass]="{ 'border-gray-600': enteredColorValue === '#ff9800' }" [ngStyle]="{ 'background': '#ff9800' }"></div>
                                <div class="w-8 h-8 ml-2 border-2 rounded-sm cursor-pointer hover:border-blue-600" (click)="setColor('#ddcc31')" [ngClass]="{ 'border-gray-600': enteredColorValue === '#ddcc31' }" [ngStyle]="{ 'background': '#ddcc31' }"></div>
                                <div class="w-8 h-8 ml-2 border-2 rounded-sm cursor-pointer hover:border-blue-600" (click)="setColor('#27d127')" [ngClass]="{ 'border-gray-600': enteredColorValue === '#27d127' }" [ngStyle]="{ 'background': '#27d127' }"></div>
                                <div class="w-8 h-8 ml-2 border-2 rounded-sm cursor-pointer hover:border-blue-600" (click)="setColor('#00d4f9')" [ngClass]="{ 'border-gray-600': enteredColorValue === '#00d4f9' }" [ngStyle]="{ 'background': '#00d4f9' }"></div>
                                <div class="w-8 h-8 ml-2 border-2 rounded-sm cursor-pointer hover:border-blue-600" (click)="setColor('#0345fc')" [ngClass]="{ 'border-gray-600': enteredColorValue === '#0345fc' }" [ngStyle]="{ 'background': '#0345fc' }"></div>
                                <div class="w-8 h-8 ml-2 border-2 rounded-sm cursor-pointer hover:border-blue-600" (click)="setColor('#c029db')" [ngClass]="{ 'border-gray-600': enteredColorValue === '#c029db' }" [ngStyle]="{ 'background': '#c029db' }"></div>
                                <div class="w-8 h-8 ml-2 border-2 rounded-sm cursor-pointer hover:border-blue-600" (click)="setColor('#7619a7')" [ngClass]="{ 'border-gray-600': enteredColorValue === '#7619a7' }" [ngStyle]="{ 'background': '#7619a7' }"></div>
                            </div>
                        </div>
                    </div>
                   
                }
            </div>   
        }
    </div>

    <!-- Actions -->
    @if (data.actions?.confirm?.show || data.actions?.cancel?.show) {
        <div class="flex items-center justify-center sm:justify-end px-6 py-4 space-x-3 bg-gray-50 dark:bg-black dark:bg-opacity-10">
            @if (data.actions?.cancel?.show) {
                <button
                    mat-stroked-button
                    [matDialogClose]="null">
                    {{ data.actions?.cancel?.label }}
                </button>
            }

            @if (data.actions?.confirm?.show) {
                <button
                    mat-flat-button
                    [color]="data.actions?.confirm?.color"
                    [matDialogClose]="close()"
                    [disabled]="isConfirmDisabled">
                    {{ data.actions?.confirm?.label }}
                </button>
            }
        </div>
    }
</div>
